import os
from src.utils.db_config import ANSIBLE_DB_URI

class Config:
    # 从 .env 文件读取配置
    SECRET_KEY = os.environ.get('SECRET_KEY', 'dev_secret_key_for_development_only_32chars')
    SQLALCHEMY_DATABASE_URI = ANSIBLE_DB_URI
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Ansible配置（从 .env 文件读取）
    ANSIBLE_HOST = os.environ.get('ANSIBLE_HOST', '***********')
    JUMP_HOST = os.environ.get('JUMP_HOST', '************')
    JUMP_PORT = int(os.environ.get('JUMP_PORT', '6233'))
    ANSIBLE_PORT = int(os.environ.get('ANSIBLE_PORT', '22'))