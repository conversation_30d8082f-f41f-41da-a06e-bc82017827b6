/* MySQL数据库操作日志审计系统样式 */

body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* 侧边栏样式 */
.sidebar {
    min-height: 100vh;
    padding: 0;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar .nav-link {
    font-weight: 500;
    padding: 0.8rem 1rem;
    border-radius: 0;
    margin-bottom: 0.2rem;
}

.sidebar .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar .nav-link.active {
    background-color: #0d6efd;
}

/* 主内容区样式 */
.main-content {
    padding: 0 1.5rem;
    background-color: #f8f9fa;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    font-weight: 600;
    padding: 0.8rem 1.25rem;
}

/* 表格样式 */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    border-top: none;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

/* 风险级别样式 */
.risk-high {
    color: #dc3545;
    font-weight: 600;
}

.risk-warning {
    color: #fd7e14;
    font-weight: 600;
}

.risk-normal {
    color: #198754;
}

/* 操作详情样式 */
.operation-detail {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: pointer;
    color: #0d6efd;
}

/* 模态框样式 */
.modal-body pre {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    max-height: 400px;
    overflow-y: auto;
}

/* 日期选择器样式 */
.daterangepicker {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    padding: 0.375rem 1rem;
}

/* 分页样式 */
.pagination {
    margin-bottom: 0;
}

.pagination .page-link {
    color: #0d6efd;
    border: none;
    margin: 0 2px;
    border-radius: 4px;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
    
    .main-content {
        padding: 0 1rem;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* 加载动画 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
}

.loading-spinner {
    width: 3rem;
    height: 3rem;
}
