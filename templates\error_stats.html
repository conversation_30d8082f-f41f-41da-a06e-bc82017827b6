<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误统计监控</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .controls {
            margin-bottom: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 8px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-danger {
            background: #dc3545;
        }
        .btn-danger:hover {
            background: #c82333;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #218838;
        }
        .error-details {
            margin-top: 20px;
        }
        .error-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        .error-item.high {
            background: #f8d7da;
            border-color: #f5c6cb;
        }
        .error-item.medium {
            background: #fff3cd;
            border-color: #ffeaa7;
        }
        .error-item.low {
            background: #d1ecf1;
            border-color: #bee5eb;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        pre {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 错误统计监控</h1>
            <p>实时监控系统错误状态和统计信息</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="loadErrorStats()">📊 刷新错误统计</button>
            <button class="btn" onclick="loadErrorDetails()">📋 查看错误详情</button>
            <button class="btn" onclick="loadErrorReport()">📄 生成错误报告</button>
            <button class="btn" onclick="loadMonitorStatus()">🔍 监控状态</button>
            <button class="btn btn-success" onclick="generateTestError()">🧪 生成测试错误</button>
            <button class="btn btn-danger" onclick="clearOldErrors()">🗑️ 清理旧错误</button>
        </div>

        <div id="message-area"></div>

        <div class="stats-grid" id="stats-grid">
            <div class="stat-card">
                <h3>总错误数</h3>
                <div class="stat-value" id="total-errors">-</div>
            </div>
            <div class="stat-card">
                <h3>错误率</h3>
                <div class="stat-value" id="error-rate">-</div>
            </div>
            <div class="stat-card">
                <h3>最常见分类</h3>
                <div class="stat-value" id="common-category">-</div>
            </div>
            <div class="stat-card">
                <h3>最常见级别</h3>
                <div class="stat-value" id="common-level">-</div>
            </div>
        </div>

        <div id="content-area">
            <div class="loading">点击上方按钮加载数据...</div>
        </div>
    </div>

    <script>
        function showMessage(message, type = 'info') {
            const messageArea = document.getElementById('message-area');
            const className = type === 'error' ? 'error-message' : 'success-message';
            messageArea.innerHTML = `<div class="${className}">${message}</div>`;
            setTimeout(() => {
                messageArea.innerHTML = '';
            }, 5000);
        }

        function showLoading() {
            document.getElementById('content-area').innerHTML = '<div class="loading">加载中...</div>';
        }

        async function loadErrorStats() {
            showLoading();
            try {
                const response = await fetch('/api/error-stats');
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    
                    // 更新统计卡片
                    document.getElementById('total-errors').textContent = data.total_errors || 0;
                    document.getElementById('error-rate').textContent = (data.summary?.error_rate || 0) + '/小时';
                    document.getElementById('common-category').textContent = data.summary?.most_common_category || '无';
                    document.getElementById('common-level').textContent = data.summary?.most_common_level || '无';
                    
                    // 显示详细统计
                    document.getElementById('content-area').innerHTML = `
                        <h3>📊 错误统计详情</h3>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                    showMessage('错误统计加载成功', 'success');
                } else {
                    showMessage('加载错误统计失败: ' + result.message, 'error');
                }
            } catch (error) {
                showMessage('请求失败: ' + error.message, 'error');
            }
        }

        async function loadErrorDetails() {
            showLoading();
            try {
                const response = await fetch('/api/error-details?limit=10');
                const result = await response.json();
                
                if (result.success) {
                    const errors = result.data.errors || [];
                    let html = '<h3>📋 最近错误详情</h3>';
                    
                    if (errors.length === 0) {
                        html += '<p>暂无错误记录</p>';
                    } else {
                        errors.forEach(error => {
                            html += `
                                <div class="error-item ${error.level}">
                                    <strong>${error.message}</strong><br>
                                    <small>分类: ${error.category} | 级别: ${error.level} | 时间: ${error.timestamp}</small>
                                </div>
                            `;
                        });
                    }
                    
                    document.getElementById('content-area').innerHTML = html;
                    showMessage('错误详情加载成功', 'success');
                } else {
                    showMessage('加载错误详情失败: ' + result.message, 'error');
                }
            } catch (error) {
                showMessage('请求失败: ' + error.message, 'error');
            }
        }

        async function loadErrorReport() {
            showLoading();
            try {
                const response = await fetch('/api/error-report?format=text');
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('content-area').innerHTML = `
                        <h3>📄 错误报告</h3>
                        <pre>${result.data.report}</pre>
                    `;
                    showMessage('错误报告生成成功', 'success');
                } else {
                    showMessage('生成错误报告失败: ' + result.message, 'error');
                }
            } catch (error) {
                showMessage('请求失败: ' + error.message, 'error');
            }
        }

        async function loadMonitorStatus() {
            showLoading();
            try {
                const response = await fetch('/api/error-monitor/status');
                const result = await response.json();
                
                if (result.success) {
                    const data = result.data;
                    document.getElementById('content-area').innerHTML = `
                        <h3>🔍 监控状态</h3>
                        <div class="stat-card">
                            <p><strong>监控状态:</strong> ${data.monitor_enabled ? '启用' : '禁用'}</p>
                            <p><strong>缓冲区大小:</strong> ${data.buffer_size}</p>
                            <p><strong>健康状态:</strong> ${data.monitor_health}</p>
                            <p><strong>最后错误时间:</strong> ${data.last_error_time || '无'}</p>
                            ${data.warning_message ? `<p style="color: orange;"><strong>警告:</strong> ${data.warning_message}</p>` : ''}
                        </div>
                    `;
                    showMessage('监控状态加载成功', 'success');
                } else {
                    showMessage('加载监控状态失败: ' + result.message, 'error');
                }
            } catch (error) {
                showMessage('请求失败: ' + error.message, 'error');
            }
        }

        async function generateTestError() {
            try {
                const errorTypes = ['database', 'network', 'validation', 'business', 'generic'];
                const randomType = errorTypes[Math.floor(Math.random() * errorTypes.length)];
                
                const response = await fetch('/api/test-error', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ type: randomType })
                });
                
                // 测试错误应该返回错误状态，这是正常的
                showMessage(`生成了一个 ${randomType} 类型的测试错误`, 'success');
                
                // 自动刷新统计
                setTimeout(loadErrorStats, 1000);
            } catch (error) {
                showMessage('生成测试错误失败: ' + error.message, 'error');
            }
        }

        async function clearOldErrors() {
            if (!confirm('确定要清理7天前的错误记录吗？')) {
                return;
            }
            
            try {
                const response = await fetch('/api/error-monitor/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ days: 7 })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showMessage(`成功清理了 ${result.data.cleared_count} 条错误记录`, 'success');
                    // 自动刷新统计
                    setTimeout(loadErrorStats, 1000);
                } else {
                    showMessage('清理错误记录失败: ' + result.message, 'error');
                }
            } catch (error) {
                showMessage('请求失败: ' + error.message, 'error');
            }
        }

        // 页面加载时自动加载错误统计
        window.onload = function() {
            loadErrorStats();
        };
    </script>
</body>
</html>
