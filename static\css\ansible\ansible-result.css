/* Ansible执行结果样式 */
.task-result-modal {
    max-width: 90%;
    margin: 2rem auto;
}

.task-result-content {
    height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-body {
    flex: 1;
    overflow: auto;
    padding: 1.5rem;
}

.task-result-output {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    height: 100%;
    overflow: auto;
}

/* 全屏模式 */
.fullscreen-modal {
    max-width: 100% !important;
    margin: 0 !important;
}

.fullscreen-modal .modal-content {
    height: 100vh !important;
    border-radius: 0 !important;
}

/* Ansible输出样式 */
.ansible-output {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    line-height: 1.5;
    font-size: 14px;
    background-color: #2b2b2b;
    color: #f8f8f2;
    padding: 1rem;
    border-radius: 6px;
    overflow: auto;
}

.ansible-error {
    color: #ff5555;
    font-weight: bold;
}

.ansible-success {
    color: #50fa7b;
}

.ansible-warning {
    color: #ffb86c;
}

.ansible-changed {
    color: #f1fa8c;
}

.ansible-skipped {
    color: #8be9fd;
}

/* 主机输出样式 */
.host-success {
    color: #50fa7b;
}

.host-failed {
    color: #ff5555;
}

/* 任务结果区域 */
.task-result-section {
    display: none;
    height: 100%;
    overflow: auto;
}

.task-result-section.active {
    display: block;
}

/* 美化pre标签 */
pre {
    background-color: #2b2b2b;
    color: #f8f8f2;
    padding: 1rem;
    border-radius: 6px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    white-space: pre-wrap;
    overflow: auto;
    max-height: none;
}

/* 美化alert */
.alert {
    border-radius: 6px;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .task-result-modal {
        max-width: 100%;
        margin: 0;
    }
    
    .task-result-content {
        height: 100vh;
        border-radius: 0;
    }
}
