/*!
 * Font Awesome Free 5.15.4 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 */
.fa,.fab,.fad,.fal,.far,.fas{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:inline-block;font-style:normal;font-variant:normal;text-rendering:auto;line-height:1}.fa-lg{font-size:1.33333em;line-height:.75em;vertical-align:-.0667em}.fa-xs{font-size:.75em}.fa-sm{font-size:.875em}.fa-1x{font-size:1em}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-6x{font-size:6em}.fa-7x{font-size:7em}.fa-8x{font-size:8em}.fa-9x{font-size:9em}.fa-10x{font-size:10em}.fa-fw{text-align:center;width:1.25em}.fa-border{border:.08em solid #eee;border-radius:.1em;padding:.2em .25em .15em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left,.fab.fa-pull-left,.fal.fa-pull-left,.far.fa-pull-left,.fas.fa-pull-left{margin-right:.3em}.fa.fa-pull-right,.fab.fa-pull-right,.fal.fa-pull-right,.far.fa-pull-right,.fas.fa-pull-right{margin-left:.3em}

/* 关键图标 */
.fa-minus:before{content:"\f068"}.fa-plus:before{content:"\f067"}.fa-times:before{content:"\f00d"}.fa-download:before{content:"\f019"}.fa-upload:before{content:"\f093"}.fa-trash:before{content:"\f1f8"}.fa-folder:before{content:"\f07b"}.fa-file:before{content:"\f15b"}.fa-check:before{content:"\f00c"}.fa-play:before{content:"\f04b"}.fa-pause:before{content:"\f04c"}.fa-redo:before{content:"\f01e"}.fa-chevron-up:before{content:"\f077"}.fa-chevron-down:before{content:"\f078"}

/* 使用简单的符号替代字体文件 */
.fa, .fas, .far {
  font-family: Arial, sans-serif;
}

/* 简单替换一些常用图标 */
.fa-minus:before { content: "-"; }
.fa-plus:before { content: "+"; }
.fa-times:before { content: "×"; }
.fa-download:before { content: "↓"; }
.fa-upload:before { content: "↑"; }
.fa-trash:before { content: "🗑"; }
.fa-folder:before { content: "📁"; }
.fa-file:before { content: "📄"; }
.fa-check:before { content: "✓"; }
.fa-play:before { content: "▶"; }
.fa-pause:before { content: "⏸"; }
.fa-redo:before { content: "↻"; }
.fa-chevron-up:before { content: "▲"; }
.fa-chevron-down:before { content: "▼"; } 