"""
库表挂接率模块 - 显示目录和库表挂接情况的统计信息
"""
from flask import Blueprint, render_template, jsonify, current_app, request
from sqlalchemy import create_engine, text
import logging
import threading
from functools import lru_cache
import time
from datetime import datetime, timedelta

# 配置日志
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# 创建蓝图
table_connection_rate_bp = Blueprint('table_connection_rate', __name__, url_prefix='/table_connection_rate')

# 创建一个连接缓存，避免频繁创建新连接
connection_cache = {}
connection_cache_lock = threading.Lock()

# # 对统计结果进行缓存，减少重复查询
# CACHE_TIMEOUT = 3600  # 缓存有效期(秒)
# last_cache_time = 0
# cached_stats = None

class DBConnectionManager:
    """管理连接到目录数据库的连接"""
    
    @staticmethod
    def get_connection_config():
        """从数据库获取连接配置
        
        返回:
            dict: 包含连接信息的字典
        """
        from src.utils.database_helpers import get_db_connection, execute_query
        
        try:
            # 获取主数据库连接
            main_engine = get_db_connection()
            
            # 直接使用engine.execute而不是execute_query函数，避免返回值格式问题
            query = """
            SELECT config_name, db_host, db_port, db_username, db_password, db_name 
            FROM db_connection_configs_metadata 
            WHERE config_name = 'ku_biao_gua_jie_lu_db'
            LIMIT 1
            """
            
            with main_engine.connect() as conn:
                result = conn.execute(text(query))
                row = result.fetchone()
                
                if row:
                    config = {
                        "host": row[1],
                        "port": int(row[2]),
                        "user": row[3],
                        "password": row[4],
                        "database": row[5]
                    }
                    return config
                else:
                    logger.error("未找到目录数据库连接配置")
                    # 如果表不存在或没有配置数据，使用硬编码的默认配置
                    # 注意：这只是为了开发环境，生产环境应通过数据库配置管理
                    logger.warning("使用默认的目录数据库连接配置")
                    return {
                        "host": "**************",
                        "port": 3310,
                        "user": "root",
                        "password": "123456",
                        "database": "dsp_catalog"
                    }
                
        except Exception as e:
            logger.error(f"获取目录数据库连接配置时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
    
    @staticmethod
    def get_catalog_db_connection():
        """获取目录数据库连接
        
        返回:
            SQLAlchemy engine 对象
        """
        global connection_cache
        
        with connection_cache_lock:
            # 检查缓存中是否已有连接
            if 'catalog_db' in connection_cache and connection_cache['catalog_db']['time'] > time.time() - 3600:
                return connection_cache['catalog_db']['engine']
                
            # 获取连接配置
            config = DBConnectionManager.get_connection_config()
            if not config:
                logger.error("无法获取目录数据库连接配置")
                return None
                
            try:
                # 创建新连接
                connection_url = f"mysql+pymysql://{config['user']}:{config['password']}@{config['host']}:{config['port']}/{config['database']}"
                engine = create_engine(
                    connection_url,
                    pool_size=5,
                    max_overflow=10,
                    pool_timeout=30,
                    pool_recycle=1800
                )
                
                # 测试连接
                with engine.connect() as conn:
                    conn.execute(text("SELECT 1"))
                    
                # 更新缓存
                connection_cache['catalog_db'] = {
                    'engine': engine,
                    'time': time.time()
                }
                
                logger.info("成功创建到目录数据库的连接")
                return engine
                
            except Exception as e:
                logger.error(f"连接目录数据库时出错: {str(e)}")
                return None

    @staticmethod
    def close_all_connections():
        """关闭所有缓存的数据库连接"""
        global connection_cache
        
        with connection_cache_lock:
            for conn_key in list(connection_cache.keys()):
                try:
                    if 'engine' in connection_cache[conn_key]:
                        engine = connection_cache[conn_key]['engine']
                        # 尝试处理连接池
                        if hasattr(engine, 'dispose'):
                            engine.dispose()
                        logger.info(f"已关闭并释放数据库连接: {conn_key}")
                except Exception as e:
                    logger.error(f"关闭数据库连接 {conn_key} 时出错: {str(e)}")
            
            # 清空缓存
            connection_cache.clear()


def get_connection_stats():
    """获取挂接率统计数据
    
    返回:
        dict: 包含统计数据的字典
    """
    
    start_time = time.time()
    logger.info("开始获取挂接率统计数据")
    
    # 获取目录数据库连接
    engine = DBConnectionManager.get_catalog_db_connection()
    if not engine:
        logger.error("无法获取目录数据库连接")
        # 返回带有正确结构的错误信息，确保模板能正常渲染
        return {
            "error": "无法连接到目录数据库",
            "city": {
                "total_catalogs": 0,
                "connected_catalogs": 0,
                "connection_rate": 0,
                "table_api_connected": 0,
                "table_api_connection_rate": 0
            },
            "counties": {},
            "connection_rates": {}
        }
    
    try:
        # 定义区县代码映射
        county_codes = {
            '秦安县': '620522000000',
            '张家川回族自治县': '620525000000',
            '甘谷县': '620523000000',
            '武山县': '620524000000',
            '清水县': '620521000000',
            '麦积区': '620503000000',
            '秦州区': '620502000000'
        }
        
        # 结果容器
        results = {
            "city": {
                "total_catalogs": 0,
                "connected_catalogs": 0,
                "connection_rate": 0,
                "table_api_connected": 0,
                "table_api_connection_rate": 0
            },
            "counties": {},
            "connection_rates": {}
        }
        
        with engine.connect() as conn:
            # 查询全市总目录数
            total_query = text("SELECT COUNT(1) FROM dsp_catalog.data_catalog WHERE `status` = 4 AND is_del = 0")
            results["city"]["total_catalogs"] = conn.execute(total_query).scalar() or 0
            
            # 查询全市已挂接资源目录数
            connected_query = text("SELECT COUNT(1) FROM dsp_catalog.data_catalog WHERE `status` = 4 AND is_del = 0 AND file_count + table_count + api_count > 0")
            results["city"]["connected_catalogs"] = conn.execute(connected_query).scalar() or 0
            
            # 查询全市已挂接库表接口数
            table_api_connected_query = text("SELECT COUNT(1) FROM dsp_catalog.data_catalog WHERE `status` = 4 AND is_del = 0 AND table_count + api_count > 0")
            results["city"]["table_api_connected"] = conn.execute(table_api_connected_query).scalar() or 0
            
            # 计算全市挂接率
            if results["city"]["total_catalogs"] > 0:
                results["city"]["connection_rate"] = round(results["city"]["connected_catalogs"] / results["city"]["total_catalogs"] * 100, 2)
                results["city"]["table_api_connection_rate"] = round(results["city"]["table_api_connected"] / results["city"]["total_catalogs"] * 100, 2)
            
            # 查询各县区目录数
            for county_name, region_code in county_codes.items():
                # 县区目录总数
                county_total_query = text(f"SELECT COUNT(1) FROM dsp_catalog.data_catalog WHERE `status` = 4 AND is_del = 0 AND region_code = '{region_code}'")
                county_total = conn.execute(county_total_query).scalar() or 0
                
                # 县区已挂接目录数
                county_connected_query = text(f"SELECT COUNT(1) FROM dsp_catalog.data_catalog WHERE `status` = 4 AND is_del = 0 AND table_count + api_count > 0 AND region_code = '{region_code}'")
                county_connected = conn.execute(county_connected_query).scalar() or 0
                
                # 计算县区挂接率
                county_rate = 0
                if county_total > 0:
                    county_rate = round(county_connected / county_total * 100, 2)
                
                results["counties"][county_name] = {
                    "total": county_total,
                    "connected": county_connected
                }
                
                results["connection_rates"][county_name] = county_rate
        
        elapsed_time = time.time() - start_time
        logger.info(f"获取挂接率统计数据完成，耗时: {elapsed_time:.2f}秒")
        
        return results
        
    except Exception as e:
        logger.error(f"获取挂接率统计数据时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return {
            "error": f"获取统计数据失败: {str(e)}",
            "city": {
                "total_catalogs": 0,
                "connected_catalogs": 0,
                "connection_rate": 0,
                "table_api_connected": 0,
                "table_api_connection_rate": 0
            },
            "counties": {},
            "connection_rates": {}
        }


@table_connection_rate_bp.route('/')
def table_connection_rate_page():
    """渲染库表挂接率页面"""
    try:
        # 获取统计数据
        stats = get_connection_stats()
        
        # 检查stats是否有error字段，记录日志但仍继续渲染页面
        if "error" in stats:
            logger.warning(f"获取统计数据出现问题: {stats.get('error')}")
            
            # 检查是否是配置表不存在问题，并提供解决方案
            if "未找到目录数据库连接配置" in stats.get('error', '') or "无法获取目录数据库连接" in stats.get('error', ''):
                # 添加配置表创建指导
                stats['setup_guide'] = True
                stats['setup_sql_file'] = 'create_config_table.sql'
        
        # 版本信息
        version_info = {
            'version': current_app.config.get('CURRENT_VERSION', 'v1.0.0'),
            'show_version_notification': current_app.config.get('SHOW_VERSION_NOTIFICATION', False),
            'version_release_notes': current_app.config.get('VERSION_RELEASE_NOTES', '')
        }
        
        # 渲染模板
        return render_template(
            'table_connection_rate.html',
            stats=stats,
            version=version_info['version'],
            show_version_notification=version_info['show_version_notification'],
            version_release_notes=version_info['version_release_notes']
        )
        
    except Exception as e:
        logger.error(f"渲染库表挂接率页面时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 创建默认数据结构，确保模板能渲染
        default_stats = {
            "error": f"获取统计数据失败: {str(e)}",
            "city": {
                "total_catalogs": 0,
                "connected_catalogs": 0,
                "connection_rate": 0,
                "table_api_connected": 0,
                "table_api_connection_rate": 0
            },
            "counties": {
                "秦安县": {"total": 0, "connected": 0},
                "张家川回族自治县": {"total": 0, "connected": 0},
                "甘谷县": {"total": 0, "connected": 0},
                "武山县": {"total": 0, "connected": 0},
                "清水县": {"total": 0, "connected": 0},
                "麦积区": {"total": 0, "connected": 0},
                "秦州区": {"total": 0, "connected": 0}
            },
            "connection_rates": {
                "秦安县": 0,
                "张家川回族自治县": 0,
                "甘谷县": 0,
                "武山县": 0,
                "清水县": 0,
                "麦积区": 0,
                "秦州区": 0
            }
        }
        
        # 获取版本信息
        version_info = {
            'version': current_app.config.get('CURRENT_VERSION', 'v1.0.0'),
            'show_version_notification': current_app.config.get('SHOW_VERSION_NOTIFICATION', False),
            'version_release_notes': current_app.config.get('VERSION_RELEASE_NOTES', '')
        }
        
        return render_template(
            'table_connection_rate.html', 
            stats=default_stats,
            version=version_info['version'],
            show_version_notification=version_info['show_version_notification'],
            version_release_notes=version_info['version_release_notes'],
            error=str(e)
        )


@table_connection_rate_bp.route('/api/stats')
def api_get_connection_stats():
    """API接口：获取库表挂接率统计数据"""
    try:
        # 获取周期偏移参数
        period_offset = request.args.get('periodOffset', 'current')
        
        # 根据周期偏移计算实际的截止日期
        now = datetime.now()
        cutoff_date = now
        
        if period_offset != 'current':
            try:
                # 尝试将周期偏移转换为整数（例如："-1" -> -1, "-2" -> -2）
                weeks_offset = int(period_offset)
                # 计算偏移后的日期：当前日期加上周数偏移（负数表示过去）
                cutoff_date = now + timedelta(weeks=weeks_offset)
                logger.info(f"接口库表挂接率：根据周期偏移 {period_offset} 计算的截止日期: {cutoff_date.strftime('%Y-%m-%d %H:%M:%S')}")
            except ValueError:
                logger.warning(f"接口库表挂接率：无法解析周期偏移 {period_offset}，使用当前日期")
        
        # 格式化截止日期为数据库查询格式
        cutoff_date_str = cutoff_date.strftime('%Y-%m-%d %H:%M:%S')
        
        # 使用截止日期获取数据
        stats = get_connection_stats_by_date(cutoff_date_str)
        
        # 如果有错误，直接返回错误信息
        if "error" in stats:
            return jsonify(stats), 500
        
        return jsonify(stats)
        
    except Exception as e:
        logger.error(f"API获取库表挂接率统计数据时出错: {str(e)}")
        return jsonify({"error": str(e)}), 500


def convert_county_name_to_key(county_name):
    """将县区中文名称转换为前端使用的英文key"""
    mapping = {
        '秦安县': 'qinan',
        '张家川回族自治县': 'zhangjiachuan',
        '甘谷县': 'gangu',
        '武山县': 'wushan',
        '清水县': 'qingshui',
        '麦积区': 'maiji',
        '秦州区': 'qinzhou'
    }
    return mapping.get(county_name)

# 添加定时刷新缓存的功能（可以由后台任务或定时任务触发）
def refresh_cache():
    """强制刷新缓存数据"""
    # global cached_stats, last_cache_time # 移除全局缓存变量的引用
    # cached_stats = None
    # last_cache_time = 0
    logger.info("缓存机制已移除，refresh_cache 不再执行实际操作。") # 修改日志信息
    return get_connection_stats() 