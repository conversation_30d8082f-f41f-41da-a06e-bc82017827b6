<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统性能监控 - 数据管理平台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        .chart-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-healthy { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-critical { background-color: #dc3545; }
        .table-container {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .refresh-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }
        .nav-pills .nav-link.active {
            background-color: #667eea;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-tachometer-alt text-primary"></i>
                    系统性能监控
                </h1>
            </div>
        </div>

        <!-- 系统指标卡片 -->
        <div class="row" id="systemMetrics">
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="cpuUsage">--</div>
                    <div class="metric-label">
                        <i class="fas fa-microchip"></i> CPU 使用率
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="memoryUsage">--</div>
                    <div class="metric-label">
                        <i class="fas fa-memory"></i> 内存使用率
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="diskUsage">--</div>
                    <div class="metric-label">
                        <i class="fas fa-hdd"></i> 磁盘使用率
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="metric-card">
                    <div class="metric-value" id="systemStatus">
                        <span class="status-indicator status-healthy"></span>
                        健康
                    </div>
                    <div class="metric-label">
                        <i class="fas fa-heartbeat"></i> 系统状态
                    </div>
                </div>
            </div>
        </div>

        <!-- 导航标签 -->
        <div class="row">
            <div class="col-12">
                <ul class="nav nav-pills mb-4" id="performanceTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="overview-tab" data-bs-toggle="pill" data-bs-target="#overview" type="button" role="tab">
                            <i class="fas fa-chart-line"></i> 概览
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="queries-tab" data-bs-toggle="pill" data-bs-target="#queries" type="button" role="tab">
                            <i class="fas fa-database"></i> 慢查询
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="functions-tab" data-bs-toggle="pill" data-bs-target="#functions" type="button" role="tab">
                            <i class="fas fa-code"></i> 函数性能
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="cache-tab" data-bs-toggle="pill" data-bs-target="#cache" type="button" role="tab">
                            <i class="fas fa-layer-group"></i> 缓存状态
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="suggestions-tab" data-bs-toggle="pill" data-bs-target="#suggestions" type="button" role="tab">
                            <i class="fas fa-lightbulb"></i> 优化建议
                        </button>
                    </li>
                </ul>
            </div>
        </div>

        <!-- 标签内容 -->
        <div class="tab-content" id="performanceTabContent">
            <!-- 概览标签 -->
            <div class="tab-pane fade show active" id="overview" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h5>系统资源使用趋势</h5>
                            <canvas id="systemChart"></canvas>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="chart-container">
                            <h5>数据库连接状态</h5>
                            <div id="dbConnections"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 慢查询标签 -->
            <div class="tab-pane fade" id="queries" role="tabpanel">
                <div class="table-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>慢查询列表</h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshSlowQueries()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>查询语句</th>
                                    <th>执行时间</th>
                                    <th>表名</th>
                                    <th>操作类型</th>
                                    <th>时间戳</th>
                                </tr>
                            </thead>
                            <tbody id="slowQueriesTable">
                                <tr>
                                    <td colspan="5" class="text-center">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 函数性能标签 -->
            <div class="tab-pane fade" id="functions" role="tabpanel">
                <div class="table-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>函数性能统计</h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshFunctionPerformance()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>函数名</th>
                                    <th>调用次数</th>
                                    <th>平均耗时</th>
                                    <th>最大耗时</th>
                                    <th>最小耗时</th>
                                    <th>总耗时</th>
                                </tr>
                            </thead>
                            <tbody id="functionsTable">
                                <tr>
                                    <td colspan="6" class="text-center">加载中...</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 缓存状态标签 -->
            <div class="tab-pane fade" id="cache" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="table-container">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h5>缓存统计</h5>
                                <div>
                                    <button class="btn btn-outline-primary btn-sm me-2" onclick="refreshCacheStats()">
                                        <i class="fas fa-sync-alt"></i> 刷新
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="clearAllCache()">
                                        <i class="fas fa-trash"></i> 清空所有缓存
                                    </button>
                                </div>
                            </div>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>缓存名称</th>
                                            <th>大小</th>
                                            <th>命中率</th>
                                            <th>内存使用</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="cacheTable">
                                        <tr>
                                            <td colspan="5" class="text-center">加载中...</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="chart-container">
                            <h5>缓存命中率</h5>
                            <canvas id="cacheChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 优化建议标签 -->
            <div class="tab-pane fade" id="suggestions" role="tabpanel">
                <div class="table-container">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5>系统优化建议</h5>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshSuggestions()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>
                    <div id="suggestionsList">
                        <div class="text-center">加载中...</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 刷新按钮 -->
    <button class="btn btn-primary btn-lg refresh-btn" onclick="refreshAll()" title="刷新所有数据">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let systemChart = null;
        let cacheChart = null;
        let refreshInterval = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            loadInitialData();
            startAutoRefresh();
        });

        // 初始化图表
        function initializeCharts() {
            // 系统资源图表
            const systemCtx = document.getElementById('systemChart').getContext('2d');
            systemChart = new Chart(systemCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU使用率',
                        data: [],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1
                    }, {
                        label: '内存使用率',
                        data: [],
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // 缓存命中率图表
            const cacheCtx = document.getElementById('cacheChart').getContext('2d');
            cacheChart = new Chart(cacheCtx, {
                type: 'doughnut',
                data: {
                    labels: [],
                    datasets: [{
                        data: [],
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }

        // 加载初始数据
        function loadInitialData() {
            refreshSystemMetrics();
            refreshSlowQueries();
            refreshFunctionPerformance();
            refreshCacheStats();
            refreshSuggestions();
            refreshDatabaseConnections();
        }

        // 开始自动刷新
        function startAutoRefresh() {
            refreshInterval = setInterval(() => {
                refreshSystemMetrics();
                refreshDatabaseConnections();
            }, 30000); // 30秒刷新一次
        }

        // 刷新所有数据
        function refreshAll() {
            loadInitialData();
            showToast('数据已刷新', 'success');
        }

        // 刷新系统指标
        function refreshSystemMetrics() {
            fetch('/api/performance/system/metrics')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateSystemMetrics(data.data);
                    }
                })
                .catch(error => console.error('Error:', error));
        }

        // 更新系统指标显示
        function updateSystemMetrics(metrics) {
            document.getElementById('cpuUsage').textContent = metrics.cpu_usage ? metrics.cpu_usage.toFixed(1) + '%' : '--';
            document.getElementById('memoryUsage').textContent = metrics.memory_usage ? metrics.memory_usage.toFixed(1) + '%' : '--';
            document.getElementById('diskUsage').textContent = metrics.disk_usage ? metrics.disk_usage.toFixed(1) + '%' : '--';

            // 更新图表数据
            if (systemChart && metrics.timestamp) {
                const time = new Date(metrics.timestamp).toLocaleTimeString();
                systemChart.data.labels.push(time);
                systemChart.data.datasets[0].data.push(metrics.cpu_usage);
                systemChart.data.datasets[1].data.push(metrics.memory_usage);

                // 保持最近20个数据点
                if (systemChart.data.labels.length > 20) {
                    systemChart.data.labels.shift();
                    systemChart.data.datasets[0].data.shift();
                    systemChart.data.datasets[1].data.shift();
                }

                systemChart.update();
            }
        }

        // 显示提示消息
        function showToast(message, type = 'info') {
            // 简单的提示实现
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.textContent = message;
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.remove();
            }, 3000);
        }

        // 其他刷新函数的占位符
        function refreshSlowQueries() {
            // 实现慢查询刷新逻辑
            console.log('Refreshing slow queries...');
        }

        function refreshFunctionPerformance() {
            // 实现函数性能刷新逻辑
            console.log('Refreshing function performance...');
        }

        function refreshCacheStats() {
            // 实现缓存统计刷新逻辑
            console.log('Refreshing cache stats...');
        }

        function refreshSuggestions() {
            // 实现优化建议刷新逻辑
            console.log('Refreshing suggestions...');
        }

        function refreshDatabaseConnections() {
            // 实现数据库连接状态刷新逻辑
            console.log('Refreshing database connections...');
        }

        function clearAllCache() {
            if (confirm('确定要清空所有缓存吗？')) {
                fetch('/api/performance/cache/clear', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('缓存已清空', 'success');
                        refreshCacheStats();
                    } else {
                        showToast('清空缓存失败: ' + data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showToast('清空缓存失败', 'danger');
                });
            }
        }
    </script>
</body>
</html>
