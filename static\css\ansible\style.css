/* Modern UI Theme for Ansible Web UI */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --secondary-color: #2c3e50;
    --success-color: #2ecc71;
    --danger-color: #e74c3c;
    --warning-color: #f39c12;
    --light-bg: #f8f9fa;
    --dark-bg: #343a40;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --modal-transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), opacity 0.3s ease;
}

body {
    background-color: #f5f7fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
}

/* Navigation */
.navbar {
    box-shadow: var(--box-shadow);
    background: linear-gradient(135deg, var(--secondary-color), var(--dark-bg));
    padding: 1rem;
    margin-bottom: 0;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
    color: white !important;
    letter-spacing: 0.5px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-link {
    font-weight: 500;
    padding: 0.6rem 1.2rem;
    margin: 0 0.2rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background-color: white;
    transition: var(--transition);
    transform: translateX(-50%);
}

.nav-link:hover::after {
    width: 80%;
}

.nav-link:hover {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
    color: white !important;
}

.nav-link.active::after {
    width: 80%;
}

/* Sections */
.section {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 2rem;
    padding: 1.8rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
    opacity: 0;
    transition: var(--transition);
}

.section:hover::before {
    opacity: 1;
}

.section:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.section-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
}

.section-header h2 {
    margin: 0;
    font-weight: 600;
    color: var(--secondary-color);
}

/* Cards */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    border-radius: var(--border-radius);
    transition: var(--transition);
    height: 100%;
    position: relative;
    overflow: hidden;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    opacity: 0;
    transition: var(--transition);
    z-index: 0;
}

.card:hover::before {
    opacity: 0.05;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: rgba(0, 0, 0, 0.03);
    font-weight: 600;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    padding: 1rem 1.25rem;
    position: relative;
    z-index: 1;
}

.card-body {
    padding: 1.5rem;
    position: relative;
    z-index: 1;
}

/* Buttons */
.btn {
    font-weight: 500;
    padding: 0.6rem 1.2rem;
    border-radius: var(--border-radius);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s ease, height 0.6s ease;
}

.btn:hover::before {
    width: 300%;
    height: 300%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(1px);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

/* Forms */
.form-control, .form-select {
    border-radius: var(--border-radius);
    padding: 0.8rem 1rem;
    border: 2px solid #e9ecef;
    transition: var(--transition);
    font-size: 0.95rem;
}

.form-control:hover, .form-select:hover {
    border-color: #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--secondary-color);
    font-size: 0.95rem;
}

/* Modal Animations */
.modal.fade .modal-dialog {
    transform: scale(0.9);
    opacity: 0;
    transition: var(--modal-transition);
}

.modal.show .modal-dialog {
    transform: scale(1);
    opacity: 1;
}

.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.25rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1.25rem;
}

/* Loading States */
.btn-loading {
    position: relative;
    pointer-events: none;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 1rem;
    height: 1rem;
    top: 50%;
    left: 50%;
    margin: -0.5rem 0 0 -0.5rem;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-top-color: white;
    border-radius: 50%;
    animation: spin 0.8s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Tables */
.table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

.table thead th {
    background-color: rgba(0, 0, 0, 0.03);
    border-bottom: 2px solid rgba(0, 0, 0, 0.05);
    font-weight: 600;
    color: var(--secondary-color);
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.table-responsive {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-top: 1.5rem;
    background-color: white;
    overflow: hidden;
}

/* Server Groups */
.group-card {
    transition: var(--transition);
    margin-bottom: 1rem;
}

.group-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* Loading Indicator */
.loading-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-indicator .spinner {
    width: 60px;
    height: 60px;
    border: 6px solid rgba(255, 255, 255, 0.3);
    border-top: 6px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 15px;
}

.loading-indicator .message {
    color: white;
    font-size: 18px;
    font-weight: 500;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Task Results */
.task-result-container {
    background-color: var(--light-bg);
    border-radius: var(--border-radius);
    padding: 20px;
    max-height: 600px;
    overflow-y: auto;
    font-family: 'Consolas', 'Monaco', monospace;
    white-space: pre-wrap;
    word-break: break-all;
    line-height: 1.5;
    font-size: 14px;
}

.task-result-container pre {
    background-color: #f1f1f1;
    padding: 15px;
    border-radius: var(--border-radius);
    margin: 15px 0;
    overflow-x: auto;
    border-left: 4px solid var(--primary-color);
}

.task-result-container h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--secondary-color);
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

/* Status Badges */
.badge {
    padding: 0.5em 0.8em;
    font-weight: 500;
    border-radius: 30px;
}

.badge-success {
    background-color: var(--success-color);
    color: white;
}

.badge-danger {
    background-color: var(--danger-color);
    color: white;
}

.badge-warning {
    background-color: var(--warning-color);
    color: white;
}

.badge-info {
    background-color: var(--primary-color);
    color: white;
}

/* Modal */
.modal-content {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    border: none;
}

.modal-header {
    background-color: var(--secondary-color);
    color: white;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}

.modal-title {
    font-weight: 600;
}

.modal-footer {
    border-top: 1px solid #eee;
    padding: 1rem;
}

/* Dashboard Stats */
.stats-card {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    background-color: white;
    transition: var(--transition);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stats-icon {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.stats-value {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .section {
        padding: 1.2rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
}

/* Modal Styles */
.modal-dialog.modal-lg.task-result-modal {
    max-width: 90%;
    max-height: 90vh;
    margin: 2rem auto;
}

.modal-content.task-result-content {
    height: 90vh;
    display: flex;
    flex-direction: column;
}

.task-result-content .modal-body {
    flex: 1;
    overflow: hidden;
    padding: 0;
    display: flex;
    flex-direction: column;
}

/* Task Result Container */
.task-result-container {
    background-color: var(--light-bg);
    border-radius: var(--border-radius);
    padding: 0;
    overflow: hidden;
    font-family: 'Consolas', 'Monaco', monospace;
    white-space: pre-wrap;
    word-break: break-all;
    line-height: 1.5;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.task-result-info {
    padding: 20px;
    border-bottom: 1px solid #ddd;
}

.task-result-output {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.task-result-nav {
    background-color: #f1f1f1;
    border-bottom: 1px solid #ddd;
    padding: 10px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.task-result-nav-tabs {
    display: flex;
    gap: 10px;
}

.task-result-nav-tab {
    padding: 6px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: 500;
    font-size: 14px;
    transition: var(--transition);
    background-color: transparent;
    border: none;
}

.task-result-nav-tab.active {
    background-color: var(--primary-color);
    color: white;
}

.task-result-nav-tab:hover:not(.active) {
    background-color: rgba(0,0,0,0.05);
}

.task-result-content-wrapper {
    flex: 1;
    overflow: auto;
    padding: 20px;
    position: relative;
}

.task-result-section {
    display: none;
}

.task-result-section.active {
    display: block;
}

.task-result-controls {
    display: flex;
    gap: 10px;
}

.task-result-container pre {
    background-color: #f1f1f1;
    padding: 15px;
    border-radius: var(--border-radius);
    margin: 15px 0;
    overflow-x: auto;
    border-left: 4px solid var(--primary-color);
    max-height: none;
}

.task-result-container h4 {
    margin-top: 20px;
    margin-bottom: 10px;
    font-weight: 600;
    color: var(--secondary-color);
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.task-result-container .line-numbers {
    counter-reset: line;
    padding-left: 0;
}

.task-result-container .line-numbers span {
    counter-increment: line;
    display: inline-block;
    width: 100%;
}

.task-result-container .line-numbers span:before {
    content: counter(line);
    display: inline-block;
    padding: 0 .5em;
    margin-right: .5em;
    color: #888;
    background-color: #f8f8f8;
    border-right: 1px solid #ddd;
    min-width: 3em;
    text-align: right;
}

.task-result-container .collapsible-section {
    margin-bottom: 15px;
    border: 1px solid #ddd;
    border-radius: var(--border-radius);
    overflow: hidden;
}

.task-result-container .collapsible-header {
    background-color: #f8f8f8;
    padding: 10px 15px;
    cursor: pointer;
    font-weight: 600;
    display: flex;
    justify-content: space-between;
    align-items: center;
    transition: var(--transition);
}

.task-result-container .collapsible-header:hover {
    background-color: #eaeaea;
}

.task-result-container .collapsible-content {
    padding: 15px;
    display: none;
    max-height: 500px;
    overflow-y: auto;
}

.task-result-container .collapsible-content.expanded {
    display: block;
}

.task-result-container .search-highlight {
    background-color: #ffeb3b;
    padding: 2px 0;
}

.task-result-search {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: white;
    padding: 10px;
    border-bottom: 1px solid #ddd;
    display: flex;
    gap: 10px;
    align-items: center;
}

.task-result-search input {
    flex: 1;
    padding: 6px 12px;
    border-radius: var(--border-radius);
    border: 1px solid #ddd;
}

.task-result-search-results {
    font-size: 12px;
    color: #666;
}

.task-result-search-nav {
    display: flex;
    gap: 5px;
}

.task-result-search-nav button {
    padding: 4px 8px;
    background-color: #f1f1f1;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.task-result-search-nav button:hover {
    background-color: #e9e9e9;
}

/* 加载指示器样式 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
}

.loading-content .spinner-border {
    width: 3rem;
    height: 3rem;
}

.loading-content .message {
    margin-top: 1rem;
    color: white;
    font-size: 1.1rem;
}

/* Toast通知样式 */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 350px;
}

.toast-notification {
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    margin-bottom: 10px;
    overflow: hidden;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: stretch;
}

.toast-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.toast-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 15px;
    min-width: 50px;
    color: white;
}

.toast-content {
    padding: 15px;
    flex-grow: 1;
}

.toast-title {
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--secondary-color);
}

.toast-message {
    color: #555;
    font-size: 0.95rem;
}

.toast-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 10px;
    color: #999;
    align-self: flex-start;
    transition: var(--transition);
}

.toast-close:hover {
    color: #333;
}

/* Toast类型样式 */
.toast-notification.success .toast-icon {
    background-color: var(--success-color);
}

.toast-notification.error .toast-icon {
    background-color: var(--danger-color);
}

.toast-notification.warning .toast-icon {
    background-color: var(--warning-color);
}

.toast-notification.info .toast-icon {
    background-color: var(--primary-color);
}

/* Toast动画 */
@keyframes toast-in {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes toast-out {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

.toast-in {
    animation: toast-in 0.3s forwards;
}

.toast-out {
    animation: toast-out 0.3s forwards;
}