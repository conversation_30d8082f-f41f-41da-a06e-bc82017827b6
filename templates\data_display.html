<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>入湖数据统计展示 - 数据管理系统</title>
    <!-- 本地Bootstrap CSS -->
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <!-- 自定义CSS -->
    <link rel="stylesheet" href="/static/css/custom.css">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="/static/css/all.min.css">
    <style>
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            height: 100%;
        }
        .stats-card.primary {
            background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        }
        .stats-card.success {
            background: linear-gradient(135deg, #e8f5e9 0%, #c8e6c9 100%);
        }
        .stats-card.info {
            background: linear-gradient(135deg, #e0f7fa 0%, #b2ebf2 100%);
        }
        .stats-card.warning {
            background: linear-gradient(135deg, #fff8e1 0%, #ffe0b2 100%);
        }
        .card-title {
            color: #3498db;
            font-weight: 600;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #dee2e6;
        }
        .card-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .progress-bar-section {
            margin-top: 8px;
            margin-bottom: 5px;
        }
        .month-card {
            background: linear-gradient(135deg, #f1f9fe 0%, #dceffc 100%);
            border-radius: 10px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            overflow: hidden;
        }
        .month-card-header {
            background: #3498db;
            color: white;
            padding: 10px 15px;
            font-weight: 600;
        }
        .month-card-body {
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .month-data-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: #2c3e50;
        }
        .badge.highest {
            background-color: #dc3545;
        }
        .badge.lowest {
            background-color: #ffc107;
            color: #212529;
        }
        .year-selector {
            width: 150px;
            margin-left: auto;
        }
        .page-divider {
            height: 3px;
            background: #e9ecef;
            margin: 2rem 0;
            border-radius: 3px;
        }
        html {
            position: relative;
            min-height: 100%;
        }
        body {
            margin-bottom: 60px; /* 页脚的高度 */
        }
        .footer {
            position: absolute;
            bottom: 0;
            width: 100%;
            height: 40px; /* 设置页脚高度 */
            line-height: 40px; /* 文字垂直居中 */
        }
        
        /* 添加年份选择器样式 */
        #yearSelector {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 0.375rem 0.75rem;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        #yearSelector:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
        }
    </style>
</head>
<body>
    <!-- 主内容区 -->
    <div class="container mb-5">
        <div class="d-flex align-items-center justify-content-between mb-3">
            <h1 class="page-title m-0">
                <i class="fas fa-chart-pie me-2"></i>入湖数据统计展示
            </h1>
            <select class="form-select" id="yearSelector" style="width: 150px;">
                <!-- 年份选项将通过JavaScript动态添加 -->
            </select>
        </div>

        <!-- 全市入湖总数据展示 -->
        <h2 class="mt-4 mb-3" id="cityDataTitle">全市入湖总数据</h2>
        <div class="row">
            <div class="col-md-3 mb-4">
                <div class="stats-card primary">
                    <h3 class="card-title">
                        <i class="fas fa-database me-2"></i>全市数据总量
                    </h3>
                    <div class="card-value" id="totalDataCount">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                    <p class="text-muted">入湖数据总条数</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card info">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-year me-2"></i>本年数据汇聚总量
                    </h3>
                    <div class="card-value" id="currentYearDataCount">
                        <div class="spinner-border text-info" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                    <p class="text-muted">当前年度入湖数据条数</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card success">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-month me-2"></i><span id="currentMonthTitle">本月数据总量</span>
                    </h3>
                    <div class="card-value" id="currentMonthDataCount">
                        <div class="spinner-border text-success" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                    <p class="text-muted">当前月份入湖数据条数</p>
                </div>
            </div>
            <div class="col-md-3 mb-4">
                <div class="stats-card warning">
                    <h3 class="card-title">
                        <i class="fas fa-calendar-week me-2"></i>&#x4E0A;&#x5468;&#x6570;&#x636E;&#x603B;&#x91CF;
                    </h3>
                    <div class="card-value" id="lastWeekDataCount">
                        <div class="spinner-border text-warning" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                    <p class="text-muted">上周入湖数据条数</p>
                </div>
            </div>
        </div>

        <div class="page-divider"></div>

        <!-- 新增：县区人均数据排名柱状图 -->
        <div class="row mt-4">
            <div class="col-md-6 mb-4">
                <div class="chart-container">
                    <h3 class="chart-title"><i class="fas fa-chart-bar me-2"></i>县区总人均量排名</h3>
                    <div id="totalPerCapitaChart" style="height: 400px; width: 100%; min-height: 400px;"></div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="chart-container">
                    <h3 class="chart-title"><i class="fas fa-chart-bar me-2"></i>年度新增人均排名</h3>
                    <div id="annualNewPerCapitaChart" style="height: 400px; width: 100%; min-height: 400px;"></div>
                </div>
            </div>
        </div>

        <div class="page-divider"></div>

        <!-- 新增：县区数据统计展示 -->
        <div class="county-specific-stats mt-4">
            <div class="d-flex align-items-center mb-3">
                <h2 class="m-0" id="countyDataTitle">县区数据汇聚统计</h2>
                <div class="ms-auto">
                    <select class="form-select" id="countyStatsSelector" style="width: 200px;">
                        <option value="">请选择县区...</option>
                        <!-- 县区选项将由JavaScript动态填充 -->
                    </select>
                </div>
            </div>
            <div class="row" id="countyStatsCardsContainer">
                <!-- 卡片1: 县区总汇聚量 -->
                <div class="col-md-3 mb-4">
                    <div class="stats-card"> <!-- 你可以沿用现有的 stats-card 样式，或为其创建新样式 -->
                        <h3 class="card-title">
                            <i class="fas fa-map-marked-alt me-2"></i><span id="selectedCountyName"></span> 总汇聚量
                        </h3>
                        <div class="card-value" id="countyTotalAggregation">
                            -
                        </div>
                        <p class="text-muted">当前所选县区入湖数据总条数</p>
                    </div>
                </div>
                <!-- 卡片2: 年度新增量 -->
                <div class="col-md-3 mb-4">
                    <div class="stats-card">
                        <h3 class="card-title">
                            <i class="fas fa-chart-line me-2"></i>年度新增量
                        </h3>
                        <div class="card-value" id="countyAnnualNewAdditions">
                            -
                        </div>
                        <p class="text-muted">当前所选县区本年新增数据条数</p>
                    </div>
                </div>
                <!-- 卡片3: 总人均量 -->
                <div class="col-md-3 mb-4">
                    <div class="stats-card">
                        <h3 class="card-title">
                            <i class="fas fa-users me-2"></i>总人均量
                        </h3>
                        <div class="card-value" id="countyTotalPerCapita">
                            -
                        </div>
                        <p class="text-muted">当前所选县区总人均数据量</p>
                    </div>
                </div>
                <!-- 卡片4: 年度新增人均 -->
                <div class="col-md-3 mb-4">
                    <div class="stats-card">
                        <h3 class="card-title">
                            <i class="fas fa-user-plus me-2"></i>年度新增人均
                        </h3>
                        <div class="card-value" id="countyAnnualNewPerCapita">
                            -
                        </div>
                        <p class="text-muted">当前所选县区年度新增人均数据量</p>
                    </div>
                </div>
                 <!-- 初始提示或加载状态 -->
                <div class="col-12" id="countyStatsInitialMessage">
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i>请从上方选择一个县区以查看详细统计数据。
                    </div>
                </div>
            </div>
        </div>

        <div class="page-divider"></div>

        <!-- 本月入湖数据统计 -->
        <div class="d-flex align-items-center mb-3">
            <h2 class="m-0" id="monthlyDataTitle">本年每月数据入湖统计</h2>
        </div>
        
        <!-- 月度数据模块 -->
        <div id="monthlyDataContainer" class="row">
            <div class="col-12 text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <p class="mt-2 text-muted">加载月度数据中...</p>
            </div>
        </div>
    </div>

    <!-- 页面底部版本信息 -->
    <footer class="footer mt-auto py-2 bg-light border-top">
        <div class="container text-center">
            <span>2025 数据管理系统</span>
            <span class="mx-2">|</span>
            <span>版本 <span id="appVersion">v4.1</span> <a href="javascript:void(0)" class="version-link text-primary">更新日志</a></span>
        </div>
    </footer>

    <!-- 版本更新日志模态框 -->
    {% include 'changelog_modal.html' %}

    <!-- Bootstrap Bundle with Popper -->
    <script src="{{ url_for('static', filename='js/lib/bootstrap.bundle.min.js') }}"></script>
    <!-- jQuery -->
    <script src="{{ url_for('static', filename='js/lib/jquery-3.6.0.min.js') }}"></script>
    <!-- Chart.js 直接从CDN加载 -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
    <!-- ECharts 从本地加载 -->
    <script src="{{ url_for('static', filename='js/lib/echarts.min.js') }}"></script>
    <!-- 解决ECharts非被动事件监听器警告 -->
    <script>
        // 添加被动事件监听器支持检测
        let supportsPassive = false;
        try {
            const opts = Object.defineProperty({}, 'passive', {
                get: function() {
                    supportsPassive = true;
                    return true;
                }
            });
            window.addEventListener('testPassive', null, opts);
            window.removeEventListener('testPassive', null, opts);
        } catch (e) {}
        
        // 重写addEventListener以使ECharts的事件监听器成为被动的
        const originalAddEventListener = EventTarget.prototype.addEventListener;
        EventTarget.prototype.addEventListener = function(type, listener, options) {
            if (type === 'mousewheel' || type === 'wheel') {
                const useCapture = (typeof options === 'object') ? options.capture : options;
                options = (typeof options === 'object') ? options : {};
                options.passive = true;
                originalAddEventListener.call(this, type, listener, options);
            } else {
                originalAddEventListener.apply(this, arguments);
            }
        };
    </script>
    <!-- 自定义JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1;
            
            const yearSelector = document.getElementById('yearSelector');
            for (let year = currentYear; year >= 2020; year--) {
                const option = document.createElement('option');
                option.value = year;
                option.textContent = year + '年';
                option.selected = (year === currentYear);
                yearSelector.appendChild(option);
            }
            
            updatePageTitles(currentYear);
            loadWeeklyStats(currentYear);
            loadMonthlyStats(currentYear);

            let totalPerCapitaChartInstance = null;
            let annualNewPerCapitaChartInstance = null;
            let resizeHandlerAttached = false;

            const throttledResize = () => {
                if (totalPerCapitaChartInstance && !totalPerCapitaChartInstance.isDisposed()) {
                    try { totalPerCapitaChartInstance.resize(); } catch (e) { console.warn('Error resizing totalPerCapitaChartInstance:', e); }
                }
                if (annualNewPerCapitaChartInstance && !annualNewPerCapitaChartInstance.isDisposed()) {
                    try { annualNewPerCapitaChartInstance.resize(); } catch (e) { console.warn('Error resizing annualNewPerCapitaChartInstance:', e); }
                }
            };
            
            function loadPerCapitaRankingCharts(year) {
                // console.log(`开始加载人均排名数据，年份: ${year}`);
                
                const totalChartDiv = document.getElementById('totalPerCapitaChart');
                const annualChartDiv = document.getElementById('annualNewPerCapitaChart');

                if (!totalChartDiv || !annualChartDiv) {
                    console.error('图表容器DIV未找到!');
                    return;
                }
                
                // 步骤1: 立即 Dispose 旧实例 (如果存在)，在修改DOM之前
                if (totalPerCapitaChartInstance && !totalPerCapitaChartInstance.isDisposed()) {
                    try {
                        totalPerCapitaChartInstance.dispose();
                        // console.log(`Disposed existing totalPerCapitaChartInstance before loading new year ${year}`);
                    } catch (e) {
                        console.warn('Error disposing existing totalPerCapitaChartInstance during new load:', e);
                    }
                }
                totalPerCapitaChartInstance = null; // 确保重置
                
                if (annualNewPerCapitaChartInstance && !annualNewPerCapitaChartInstance.isDisposed()) {
                    try {
                        annualNewPerCapitaChartInstance.dispose();
                        // console.log(`Disposed existing annualNewPerCapitaChartInstance before loading new year ${year}`);
                    } catch (e) {
                        console.warn('Error disposing existing annualNewPerCapitaChartInstance during new load:', e);
                    }
                }
                annualNewPerCapitaChartInstance = null; // 确保重置

                // 步骤2: 设置加载状态 (修改innerHTML)
                totalChartDiv.innerHTML = `
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">加载排名数据中...</p>
                    </div>`;
                annualChartDiv.innerHTML = `
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2 text-muted">加载排名数据中...</p>
                    </div>`;
                
                fetch(`/api/county_summary/per_capita_ranking?year=${year}`)
                    .then(response => response.json())
                    .then(data => {
                        // console.log(`获取到的排名数据 (${year}):`, data);
                        
                        // 步骤3: 清空加载动画，准备渲染图表或提示信息
                        // 旧的 dispose 和实例重置逻辑已移至函数开头

                        // 清空divs中的加载动画
                        if(totalChartDiv) totalChartDiv.innerHTML = ''; 
                        if(annualChartDiv) annualChartDiv.innerHTML = '';

                        let chartsEffectivelyInitialized = false;

                        if (data.error) {
                            console.error(`获取县区人均数据排名失败 (${year}): ${data.error}`);
                            if(totalChartDiv) totalChartDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>加载数据出错: ${data.error}</div>`;
                            if(annualChartDiv) annualChartDiv.innerHTML = `<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>加载数据出错: ${data.error}</div>`;
                            return;
                        }

                        // Filter out data with per_capita <= 0 or is NaN
                        const validCountyPerCapitaData = Array.isArray(data.total_per_capita_ranking) ? data.total_per_capita_ranking.filter(item => typeof item.per_capita === 'number') : [];

                        const countyPerCapitaOptions = {
                            title: {
                                text: '县区人均汇聚量排名',
                                left: 'center'
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: { type: 'shadow' },
                                formatter: params => `${params[0].name}: ${parseFloat(params[0].value).toFixed(2)} 条/人`
                            },
                            grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
                            xAxis: { type: 'category', data: validCountyPerCapitaData.map(item => item.county_name), axisLabel: { interval: 0, rotate: 45 } },
                            yAxis: { type: 'value', name: '条/人' },
                            series: [{
                                name: '总人均量', type: 'bar',
                                data: validCountyPerCapitaData.map(item => parseFloat(item.per_capita)),
                                itemStyle: { color: params => ['#FF4500', '#FF6347', '#FF7F50', '#FFA07A', '#FFD700', '#FFEC8B', '#90EE90', '#98FB98', '#87CEFA', '#B0E0E6'][params.dataIndex % 10] },
                                label: { show: true, position: 'top', formatter: '{c}' }
                            }]
                        };

                        // Filter out data with increment_per_capita <= 0 or is NaN
                        const validYearlyIncrementPerCapitaData = Array.isArray(data.annual_new_per_capita_ranking) ? data.annual_new_per_capita_ranking.filter(item => typeof item.per_capita === 'number') : [];

                        const yearlyIncrementPerCapitaOptions = {
                            title: {
                                text: '年度新增人均排名',
                                left: 'center'
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: { type: 'shadow' },
                                formatter: params => `${params[0].name}: ${parseFloat(params[0].value).toFixed(2)} 条/人`
                            },
                            grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
                            xAxis: { type: 'category', data: validYearlyIncrementPerCapitaData.map(item => item.county_name), axisLabel: { interval: 0, rotate: 45 } },
                            yAxis: { type: 'value', name: '条/人' },
                            series: [{
                                name: '年度新增人均', type: 'bar',
                                data: validYearlyIncrementPerCapitaData.map(item => parseFloat(item.per_capita)),
                                itemStyle: { color: params => ['#32CD32', '#3CB371', '#20B2AA', '#00CED1', '#1E90FF', '#6495ED', '#7B68EE', '#9370DB', '#BA55D3', '#FF69B4'][params.dataIndex % 10] },
                                label: { show: true, position: 'top', formatter: '{c}' }
                            }]
                        };

                        if (validCountyPerCapitaData.length > 0) {
                            if (totalChartDiv) {
                                try {
                                    totalPerCapitaChartInstance = echarts.init(totalChartDiv);
                                    totalPerCapitaChartInstance.setOption(countyPerCapitaOptions);
                                    chartsEffectivelyInitialized = true;
                                } catch (e) {
                                    console.error(`初始化总人均量图表失败 (${year}):`, e);
                                    if(totalChartDiv) totalChartDiv.innerHTML = '<div class="alert alert-danger">图表加载失败</div>';
                                }
                            }
                        } else {
                            if(totalChartDiv) totalChartDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>暂无县区总人均量数据</div>';
                        }
                        
                        if (validYearlyIncrementPerCapitaData.length > 0) {
                            if (annualChartDiv) {
                                try {
                                    annualNewPerCapitaChartInstance = echarts.init(annualChartDiv);
                                    annualNewPerCapitaChartInstance.setOption(yearlyIncrementPerCapitaOptions);
                                    chartsEffectivelyInitialized = true;
                                } catch (e) {
                                    console.error(`初始化年度新增人均图表失败 (${year}):`, e);
                                    if(annualChartDiv) annualChartDiv.innerHTML = '<div class="alert alert-danger">图表加载失败</div>';
                                }
                            }
                        } else {
                            if(annualChartDiv) annualChartDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-info-circle me-2"></i>暂无年度新增人均数据</div>';
                        }
                        
                        if (chartsEffectivelyInitialized && !resizeHandlerAttached) {
                            window.addEventListener('resize', throttledResize);
                            resizeHandlerAttached = true;
                        }
                        // Removed setTimeout wrapper
                    })
                    .catch(error => {
                        console.error(`获取县区人均数据排名出错 (${year}):`, error);
                        if(totalChartDiv) totalChartDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>加载数据出错: 网络错误</div>';
                        if(annualChartDiv) annualChartDiv.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle me-2"></i>加载数据出错: 网络错误</div>';
                        // Instance variables are reset at the start of the function or if init fails.
                    });
            }

            loadPerCapitaRankingCharts(currentYear);
            
            // 监听年份选择变化
            yearSelector.addEventListener('change', function() {
                const selectedYear = this.value;
                
                // 更新页面所有标题的年份
                updatePageTitles(selectedYear);
                
                // 更新数据
                loadMonthlyStats(selectedYear);
                loadWeeklyStats(selectedYear);
                loadPerCapitaRankingCharts(selectedYear);
                
                // 如果已经选择了县区，则更新县区统计数据
                const selectedCounty = countySelector ? countySelector.value : '';
                if (selectedCounty) {
                    loadCountySpecificStats(selectedCounty, selectedYear);
                }
            });
            
            // 绑定版本更新链接点击事件
            const updateLogLink = document.querySelector('.update-log-link');
            if (updateLogLink) {
                updateLogLink.addEventListener('click', function(e) {
                    e.preventDefault();
                    const changelogModal = new bootstrap.Modal(document.getElementById('changelogModal'));
                    changelogModal.show();
                });
            }

            // --- 新增：县区特定数据统计的JS逻辑 ---
            const countySelector = document.getElementById('countyStatsSelector');
            const countyStatsCardsContainer = document.getElementById('countyStatsCardsContainer');
            const countyTotalAggregationEl = document.getElementById('countyTotalAggregation');
            const countyAnnualNewAdditionsEl = document.getElementById('countyAnnualNewAdditions');
            const countyTotalPerCapitaEl = document.getElementById('countyTotalPerCapita');
            const countyAnnualNewPerCapitaEl = document.getElementById('countyAnnualNewPerCapita');
            const selectedCountyNameEl = document.getElementById('selectedCountyName');
            const countyStatsInitialMessageEl = document.getElementById('countyStatsInitialMessage');

            // 1. 加载县区列表到下拉框
            function loadCountyOptions() {
                if (!countySelector) return; // Guard clause
                fetch('/api/county_summary/counties')
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            console.error('获取县区列表失败:', data.error);
                            countySelector.innerHTML = '<option value="">加载县区失败</option>';
                            return;
                        }
                        if (data.length === 0) {
                            countySelector.innerHTML = '<option value="">无可用县区</option>';
                            // Ensure initial message is shown if no counties are available
                            if(countyStatsInitialMessageEl) countyStatsInitialMessageEl.style.display = 'block';
                            return;
                        }
                        
                        // Clear previous options except the placeholder, if any. Or clear all and re-add placeholder.
                        // countySelector.innerHTML = '<option value="">请选择县区...</option>';
                        data.forEach(county => {
                            const option = document.createElement('option');
                            option.value = county.table_name;
                            option.textContent = county.display_name;
                            countySelector.appendChild(option);
                        });

                        // --- 新增：尝试设置并加载默认县区 (张家川) ---
                        let defaultCountySelected = false;
                        let zhangjiachuanTable = null;

                        // 优先按显示名称查找 "张家川" 或 "张家川县"
                        const zhangjiachuanCounty = data.find(county => 
                            county.display_name === '张家川' || county.display_name === '张家川县'
                        );

                        if (zhangjiachuanCounty) {
                            zhangjiachuanTable = zhangjiachuanCounty.table_name;
                        } else {
                            // 如果按显示名称找不到，尝试按预设的table_name查找 (作为备选)
                            const potentialOption = data.find(county => county.table_name === 'excel_data_zhang_jia_chuan');
                            if (potentialOption) {
                                zhangjiachuanTable = potentialOption.table_name;
                            }
                        }
                        
                        if (zhangjiachuanTable) {
                            countySelector.value = zhangjiachuanTable;
                            // 派发 change 事件以触发现有逻辑加载数据和更新UI
                            const event = new Event('change', { bubbles: true });
                            countySelector.dispatchEvent(event);
                            defaultCountySelected = true;
                        } 
                        // --- 结束：新增默认县区逻辑 ---

                        // 如果没有成功设置默认值 (例如张家川未找到或列表为空)
                        // 并且当前下拉框值为空 (即 "请选择县区..."), 则确保初始提示显示
                        if (!defaultCountySelected && countySelector.value === "" && countyStatsInitialMessageEl) {
                            countyStatsInitialMessageEl.style.display = 'block';
                            // 确保卡片列此时是隐藏的或显示占位符，这应该由 'change' 事件的 else 分支处理
                        }

                    })
                    .catch(error => {
                        console.error('请求县区列表出错:', error);
                        if (countySelector) countySelector.innerHTML = '<option value="">加载县区出错</option>';
                        if(countyStatsInitialMessageEl) countyStatsInitialMessageEl.style.display = 'block';
                    });
            }

            // 2. 根据选择的县区加载统计数据
            function loadCountySpecificStats(countyTableName, year) {
                // 如果没有指定年份，使用当前选中的年份
                if (!year) {
                    const yearSelector = document.getElementById('yearSelector');
                    year = yearSelector ? yearSelector.value : new Date().getFullYear();
                }
                
                // 显示加载状态
                const cardValueElements = [countyTotalAggregationEl, countyAnnualNewAdditionsEl, countyTotalPerCapitaEl, countyAnnualNewPerCapitaEl];
                cardValueElements.forEach(el => {
                    if(el) el.innerHTML = `<div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">加载中...</span></div>`;
                });
                if(selectedCountyNameEl) selectedCountyNameEl.textContent = '加载中...';

                // 使用新的API接口，传递年份参数
                fetch(`/api/county_summary/yearly_stats?table_name=${countyTableName}&year=${year}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            console.error(`获取 ${countyTableName} 统计数据失败:`, data.error, data.details);
                            cardValueElements.forEach(el => { if(el) el.textContent = '错误'; });
                            if(selectedCountyNameEl) selectedCountyNameEl.textContent = '错误';
                            return;
                        }

                        // 更新县区名称，显示年份信息
                        if(selectedCountyNameEl) {
                            const displayName = data.county_name || countyTableName.replace('excel_data_', '').replace(/_/g, ' ');
                            // 不再在县区名称后面显示年份信息
                            selectedCountyNameEl.textContent = displayName;
                        }
                        
                        // 更新统计数据
                        if(countyTotalAggregationEl) countyTotalAggregationEl.textContent = formatNumber(data.total_aggregation);
                        if(countyAnnualNewAdditionsEl) countyAnnualNewAdditionsEl.textContent = formatNumber(data.annual_new_additions);
                        if(countyTotalPerCapitaEl) countyTotalPerCapitaEl.textContent = data.total_per_capita !== null && !isNaN(parseFloat(data.total_per_capita)) ? parseFloat(data.total_per_capita).toFixed(2) : '-';
                        
                        if(countyAnnualNewPerCapitaEl) {
                            countyAnnualNewPerCapitaEl.textContent = data.annual_new_per_capita !== null && !isNaN(parseFloat(data.annual_new_per_capita)) ? parseFloat(data.annual_new_per_capita).toFixed(2) : '-';
                        }
                    })
                    .catch(error => {
                        console.error(`请求 ${countyTableName} 年度统计数据出错:`, error);
                        cardValueElements.forEach(el => { if(el) el.textContent = '请求错误'; });
                        if(selectedCountyNameEl) selectedCountyNameEl.textContent = '请求错误';
                    });
            }

            // 3. 初始化和事件监听
            if (countySelector) {
                loadCountyOptions(); 

                countySelector.addEventListener('change', function() {
                    const selectedValue = this.value;
                    if (selectedValue) {
                        if(countyStatsInitialMessageEl) countyStatsInitialMessageEl.style.display = 'none';
                        // Ensure card columns are visible
                         if(countyStatsCardsContainer) {
                            Array.from(countyStatsCardsContainer.querySelectorAll('.col-md-3')).forEach(col => col.style.display = 'block');
                        }
                        
                        // 获取当前选中的年份
                        const yearSelector = document.getElementById('yearSelector');
                        const selectedYear = yearSelector ? yearSelector.value : new Date().getFullYear();
                        
                        // 加载县区特定年份的统计数据
                        loadCountySpecificStats(selectedValue, selectedYear);
                    } else {
                        if(countyStatsInitialMessageEl) countyStatsInitialMessageEl.style.display = 'block';
                        if(selectedCountyNameEl) selectedCountyNameEl.textContent = '';
                        if(countyTotalAggregationEl) countyTotalAggregationEl.textContent = '-';
                        if(countyAnnualNewAdditionsEl) countyAnnualNewAdditionsEl.textContent = '-';
                        if(countyTotalPerCapitaEl) countyTotalPerCapitaEl.textContent = '-';
                        if(countyAnnualNewPerCapitaEl) countyAnnualNewPerCapitaEl.textContent = '-';
                        // Optionally hide card columns if no county is selected
                        // if(countyStatsCardsContainer) {
                        //    Array.from(countyStatsCardsContainer.querySelectorAll('.col-md-3')).forEach(col => col.style.display = 'none');
                        // }
                    }
                });
                 // Initial state: hide data cards, show prompt (or manage via CSS)
                // if(countyStatsCardsContainer) {
                //    Array.from(countyStatsCardsContainer.querySelectorAll('.col-md-3')).forEach(col => col.style.display = 'none');
                // }
                // Ensure initial message is shown if no county is pre-selected
                 if (countySelector.value === "") {
                    if(countyStatsInitialMessageEl) countyStatsInitialMessageEl.style.display = 'block';
                }


            }
            // --- 结束：县区特定数据统计的JS逻辑 ---
        });
        
        // 加载上周数据统计
        function loadWeeklyStats(year) {
            // 如果没有指定年份，使用当前年份
            if (!year) {
                year = new Date().getFullYear();
            }
            
            // 更新上周数据卡片标题，不再显示年份信息
            const weeklyDataTitle = document.querySelector('.stats-card.warning .card-title');
            if (weeklyDataTitle) {
                // 恢复原始标题，不带年份
                weeklyDataTitle.innerHTML = `<i class="fas fa-calendar-week me-2"></i>上周数据总量`;
            }
            
            // 显示加载状态
            const weeklyDataCountEl = document.getElementById('lastWeekDataCount');
            if (weeklyDataCountEl) {
                weeklyDataCountEl.innerHTML = `
                    <div class="spinner-border spinner-border-sm text-warning" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                `;
            }
            
            // 请求指定年份的上周数据
            fetch(`/api/data_display/weekly_stats?year=${year}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('获取上周数据失败:', data.error);
                        if (weeklyDataCountEl) weeklyDataCountEl.textContent = '加载失败';
                        return;
                    }
                    
                    // 更新上周数据总量
                    if (weeklyDataCountEl) {
                        weeklyDataCountEl.textContent = formatNumber(data.total_count);
                    }
                    
                    // 更新周数据卡片描述
                    const weekDataDescription = document.querySelector('.stats-card.warning .text-muted');
                    if (weekDataDescription) {
                        const startDate = new Date(data.start_date);
                        const endDate = new Date(data.end_date);
                        
                        const formatDate = (date) => {
                            return `${date.getMonth() + 1}月${date.getDate()}日`;
                        };
                        
                        weekDataDescription.textContent = `${formatDate(startDate)}-${formatDate(endDate)}入湖数据条数`;
                    }
                })
                .catch(error => {
                    console.error('获取上周数据出错:', error);
                    if (weeklyDataCountEl) weeklyDataCountEl.textContent = '请求错误';
                });
        }
        
        // 加载月度数据统计
        function loadMonthlyStats(year) {
            const container = document.getElementById('monthlyDataContainer');
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1; // 月份是从0开始的，所以要+1
            
            // 显示加载中
            container.innerHTML = `
                <div class="col-12 text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2 text-muted">加载月度数据中...</p>
                </div>
            `;
            
            // 加载指定年份的截止数据总量
            loadYearlyTotalStats(year);
            
            fetch(`/api/data_display/monthly_stats?year=${year}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('获取月度数据失败:', data.error);
                        return;
                    }
                    
                    // 计算年度总量
                    let yearlyTotal = 0;
                    if (data.monthly_stats && data.monthly_stats.length > 0) {
                        data.monthly_stats.forEach(monthData => {
                            yearlyTotal += monthData.count;
                        });
                        
                        // 更新年度数据总量
                        document.getElementById('currentYearDataCount').textContent = formatNumber(yearlyTotal);
                    }
                    
                    // 获取对应月份的数据（用于顶部展示）
                    // 如果是选择了当前年份，显示当前月份数据
                    // 如果选择了其他年份，显示同月份的数据
                    const monthToShow = currentMonth;
                    const monthData = data.monthly_stats.find(item => item.month === monthToShow);
                    
                    if (monthData) {
                        document.getElementById('currentMonthDataCount').textContent = formatNumber(monthData.count);
                    } else {
                        document.getElementById('currentMonthDataCount').textContent = '0';
                    }
                    
                    // 更新标题显示对应月份
                    document.getElementById('currentMonthTitle').textContent = `${monthToShow}月数据总量`;
                    
                    // 更新月度数据模块
                    updateMonthlyDataModules(data.monthly_stats, year);
                })
                .catch(error => {
                    console.error('获取月度数据出错:', error);
                    container.innerHTML = `
                        <div class="col-12 text-center py-5">
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-triangle me-2"></i>加载数据出错
                            </div>
                        </div>
                    `;
                });
        }
        
        // 加载年度总量数据
        function loadYearlyTotalStats(year) {
            const totalDataCountEl = document.getElementById('totalDataCount');

            // 显示加载动画
            totalDataCountEl.innerHTML = `
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
            `;
            
            fetch(`/api/data_display/yearly_total_stats?year=${year}`)
                .then(response => response.json())
                .then(data => {
                    if (data.error) {
                        console.error('获取年度总量数据失败:', data.error);
                        totalDataCountEl.textContent = '加载失败';
                        return;
                    }
                    
                    // 直接使用后端返回的数据，后端已经包含固定值计算
                    totalDataCountEl.textContent = formatNumber(data.total_records);
                })
                .catch(error => {
                    console.error('获取年度总量数据出错:', error);
                    totalDataCountEl.textContent = '加载出错';
                });
        }
        
        // 格式化数字，根据数值大小自动选择适合的单位（亿、千万、百万等）
        function formatNumber(num) {
            // 处理0值
            if (num === 0) return "0";
            
            // 根据数值大小选择合适的单位
            if (num >= 100000000) { // 1亿及以上
                return (num / 100000000).toFixed(2) + ' 亿';
            } else if (num >= 10000000) { // 1千万及以上
                return (num / 10000000).toFixed(2) + ' 千万';
            } else if (num >= 1000000) { // 1百万及以上
                return (num / 1000000).toFixed(2) + ' 百万';
            } else if (num >= 10000) { // 1万及以上
                return (num / 10000).toFixed(2) + ' 万';
            } else {
                // 小于1万的数据直接显示原始数字，加千分位
                return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
            }
        }
        
        // 更新月度数据模块
        function updateMonthlyDataModules(monthlyStats, year) {
            const container = document.getElementById('monthlyDataContainer');
            
            // 清空现有内容
            container.innerHTML = '';
            
            // 获取当前年月
            const currentDate = new Date();
            const currentYear = currentDate.getFullYear();
            const currentMonth = currentDate.getMonth() + 1;
            
            // 限制未来年份的月份只显示到当前月份
            const filteredStats = monthlyStats.filter(item => {
                // 过滤掉数据为0的月份
                if (item.count === 0) return false;
                
                // 如果是当前年份，过滤掉当前月份（已在顶部展示）
                if (parseInt(year) === currentYear && item.month === currentMonth) return false;
                
                // 限制未来年份只显示到当前月份
                if (parseInt(year) > currentYear || (parseInt(year) === currentYear && item.month > currentMonth)) {
                    return false;
                }
                
                return true;
            });
            
            // 如果没有数据，显示提示信息
            if (filteredStats.length === 0) {
                container.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>暂无数据
                        </div>
                    </div>
                `;
                return;
            }
            
            // 找出最大值和最小值
            const maxCount = Math.max(...filteredStats.map(item => item.count));
            const minCount = Math.min(...filteredStats.map(item => item.count));
            
            // 创建月度数据模块（按月份顺序排列）
            filteredStats.sort((a, b) => a.month - b.month).forEach(item => {
                const percentage = maxCount > 0 ? (item.count / maxCount * 100) : 0;
                
                // 确定是否为最高/最低值
                let badgeHTML = '';
                if (item.count === maxCount) {
                    badgeHTML = '<span class="badge highest ms-2">最高</span>';
                } else if (item.count === minCount) {
                    badgeHTML = '<span class="badge lowest ms-2">最低</span>';
                }
                
                const monthCard = document.createElement('div');
                monthCard.className = 'col-md-4 mb-4';
                monthCard.innerHTML = `
                    <div class="month-card">
                        <div class="month-card-header">
                            <i class="fas fa-calendar-day me-2"></i>${item.month}月${badgeHTML}
                        </div>
                        <div class="month-card-body">
                            <div>
                                <div class="month-data-value">${formatNumber(item.count)}</div>
                                <div class="text-muted small">入湖数据条数</div>
                                <div class="progress-bar-section">
                                    <div class="progress" style="height: 6px;">
                                        <div class="progress-bar" role="progressbar" style="width: ${percentage}%" aria-valuenow="${percentage}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <i class="fas fa-database text-primary" style="font-size: 2rem; opacity: 0.7"></i>
                            </div>
                        </div>
                    </div>
                `;
                
                container.appendChild(monthCard);
            });
        }

        // 新增函数：更新页面所有标题的年份
        function updatePageTitles(year) {
            const cityDataTitle = document.getElementById('cityDataTitle');
            const countyDataTitle = document.getElementById('countyDataTitle');
            const monthlyDataTitle = document.getElementById('monthlyDataTitle');
            
            const currentYear = new Date().getFullYear();
            
            if (parseInt(year) !== currentYear) {
                // 添加年份到标题
                cityDataTitle.textContent = `${year}年全市入湖总数据`;
                countyDataTitle.textContent = `${year}年县区数据汇聚统计`;
                monthlyDataTitle.textContent = `${year}年每月数据入湖统计`;
            } else {
                // 当前年份，使用默认标题
                cityDataTitle.textContent = `全市入湖总数据`;
                countyDataTitle.textContent = `县区数据汇聚统计`;
                monthlyDataTitle.textContent = `本年每月数据入湖统计`;
            }
        }
    </script>
    
    <!-- 版本信息脚本 -->
    <script>
      $(function(){
        // 获取并更新版本号
        fetch('/api/version')
          .then(response => response.json())
          .then(data => {
            $('#appVersion').text(data.version);
          })
          .catch(error => {
            console.error('获取版本信息失败:', error);
          });
      });
    </script>

    <!-- 引入通用版本更新日志模态框 -->
    {% include 'changelog_modal.html' %}

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 更新日志点击事件
        const versionLink = document.querySelector('.version-link');
        if (versionLink) {
            versionLink.addEventListener('click', function(e) {
                e.preventDefault();
                const changelogModal = new bootstrap.Modal(document.getElementById('changelogModal'));
                changelogModal.show();
            });
        }
    });
    </script>
</body>
</html> 

