/* 自定义CSS样式 - 美化Excel数据管理系统 */

/* 全局样式 */
body {
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    margin-top: 80px; /* 为顶部导航栏留出空间 */
    margin-bottom: 50px;
    max-width: 1400px;
}

/* 页面标题 */
.page-title {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #3498db;
    position: relative;
}

.page-title:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 2px;
    background-color: #e74c3c;
}

/* 导航栏样式 */
.main-navbar {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 1rem;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1030;
}

.navbar-brand {
    color: #fff !important;
    font-weight: 600;
    font-size: 1.5rem;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.navbar-brand:hover {
    transform: translateY(-2px);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.navbar-nav {
    display: flex;
    align-items: center;
}

.nav-item {
    position: relative;
    margin: 0 5px;
}

.nav-link {
    color: #fff !important;
    font-weight: 500;
    padding: 0.7rem 1.2rem;
    border-radius: 5px;
    transition: all 0.3s;
}

.nav-link:hover, .nav-link:focus {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
    transform: translateY(-2px);
}

/* 下拉菜单样式 */
.dropdown-menu {
    border: none;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    min-width: 180px;
    animation: fadeIn 0.3s ease;
}

.dropdown-item {
    padding: 0.6rem 1.2rem;
    color: #495057;
    font-weight: 500;
    transition: all 0.2s;
}

.dropdown-item:hover, .dropdown-item:focus {
    background-color: #f8f9fa;
    color: #3498db;
}

/* 二级下拉菜单 */
.dropdown-submenu {
    position: relative;
}

.dropdown-submenu .dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -0.5rem;
    margin-left: 0.1rem;
}

.dropdown-submenu:hover > .dropdown-menu {
    display: block;
}

.dropdown-submenu > .dropdown-item:after {
    display: inline-block;
    margin-left: 0.5rem;
    content: "\f054";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    font-size: 0.8rem;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 响应式导航栏 */
@media (max-width: 991.98px) {
    .navbar-nav {
        padding: 1rem 0;
    }
    
    .nav-item {
        margin: 0.25rem 0;
        width: 100%;
    }
    
    .nav-link {
        width: 100%;
        text-align: left;
    }
    
    .dropdown-menu {
        background-color: rgba(255, 255, 255, 0.05);
        border: none;
        box-shadow: none;
        padding-left: 1.5rem;
    }
    
    .dropdown-item {
        color: rgba(255, 255, 255, 0.8);
    }
    
    .dropdown-item:hover, .dropdown-item:focus {
        background-color: rgba(255, 255, 255, 0.1);
        color: #fff;
    }
    
    .dropdown-submenu .dropdown-menu {
        position: static;
        margin: 0;
        padding-left: 1.5rem;
        background-color: transparent;
    }
    
    .dropdown-submenu > .dropdown-item:after {
        transform: rotate(90deg);
    }
    
    .container {
        margin-top: 70px;
    }
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s, box-shadow 0.3s;
    margin-bottom: 25px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* 统计信息卡片 */
.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-left: 5px solid #3498db;
}

.stats-card p {
    margin-bottom: 12px;
    font-size: 1.05em;
    color: #495057;
    display: flex;
    align-items: center;
}

.stats-card strong {
    color: #2c3e50;
    margin-right: 8px;
    min-width: 150px;
    display: inline-block;
}

.stats-card .value {
    color: #3498db;
    font-weight: 500;
}

/* 选择器区域 */
.selector-area {
    background: #ffffff;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    border-left: 5px solid #2ecc71;
}

.selector-area .form-label {
    color: #2c3e50;
    font-weight: 600;
    margin-bottom: 10px;
}

.selector-area .form-select {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 10px 15px;
    height: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
    transition: all 0.3s;
}

.selector-area .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

/* 按钮组样式 */
.btn-group {
    margin-bottom: 25px;
    padding: 5px;
    background: #ffffff;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s;
    margin-right: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f6aa5 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-success {
    background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #27ae60 0%, #219653 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-info {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    border: none;
    color: white;
}

.btn-info:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f6aa5 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-warning {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    border: none;
    color: white;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e67e22 0%, #d35400 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 表格容器 */
.table-container {
    background: #ffffff;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
    border-left: 5px solid #9b59b6;
}

.table {
    margin-bottom: 0;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.table th, .table td {
    text-align: center;
    vertical-align: middle;
    padding: 15px 10px;
}

.table thead th {
    background-color: #f8f9fa;
    color: #2c3e50;
    font-weight: 600;
    border-bottom: 2px solid #e9ecef;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody tr {
    transition: all 0.2s;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table tbody tr td {
    border-bottom: 1px solid #f0f0f0;
}

/* 操作按钮 */
.table .btn-sm {
    padding: 5px 10px;
    font-size: 0.8em;
    margin: 0 3px;
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-header {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border-radius: 10px 10px 0 0;
    padding: 15px 20px;
}

.modal-title {
    font-weight: 600;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #f0f0f0;
}

.form-control {
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 10px 15px;
    height: auto;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
    transition: all 0.3s;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

/* 自动完成下拉框 */
.ui-autocomplete {
    max-height: 250px;
    overflow-y: auto;
    overflow-x: hidden;
    z-index: 9999 !important;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    padding: 8px 0;
}

.ui-menu-item {
    padding: 10px 15px;
    cursor: pointer;
    transition: all 0.2s;
}

.ui-menu-item:hover {
    background-color: #f8f9fa;
}

.ui-menu-item-wrapper {
    padding: 5px;
}

.ui-menu-item small {
    color: #6c757d;
    display: block;
    margin-top: 4px;
    font-size: 0.85em;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .stats-card, .selector-area, .table-container {
        padding: 15px;
    }
    
    .btn {
        padding: 8px 15px;
        font-size: 0.9em;
    }
    
    .stats-card strong {
        min-width: auto;
        margin-right: 5px;
    }
}

/* 状态标签 */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8em;
    font-weight: 500;
}

.status-yes {
    background-color: #d4f8e8;
    color: #1e7e50;
}

.status-no {
    background-color: #ffe5e5;
    color: #b91c1c;
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: #3498db;
    animation: spin 1s ease-in-out infinite;
    margin-right: 10px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 提示工具 */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: #2c3e50;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity 0.3s;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}
