/**
 * 版本提醒模块
 * 用于检查系统版本并在新版本发布时显示提醒弹窗
 */
(function() {
    // 本地存储的键名
    const VERSION_STORAGE_KEY = 'lastSeenVersion';
    
    // 在页面加载完成后检查版本
    document.addEventListener('DOMContentLoaded', checkVersion);
    
    /**
     * 检查当前系统版本
     */
    function checkVersion() {
        fetch('/api/version')
            .then(response => {
                if (!response.ok) {
                    throw new Error('获取版本信息失败: ' + response.statusText);
                }
                return response.json();
            })
            .then(versionData => {
                // 如果服务器端关闭了版本提醒，则不显示
                if (!versionData.show_notification) {
                    return;
                }
                
                const currentVersion = versionData.version;
                const lastSeenVersion = localStorage.getItem(VERSION_STORAGE_KEY);
                
                // 如果没有记录或版本不一致，显示弹窗
                if (!lastSeenVersion || lastSeenVersion !== currentVersion) {
                    showVersionNotification(versionData);
                    // 更新本地存储的版本号
                    localStorage.setItem(VERSION_STORAGE_KEY, currentVersion);
                }
            })
            .catch(error => {
                console.error('版本检查出错:', error);
            });
    }
    
    /**
     * 显示版本更新提醒弹窗
     * @param {Object} versionData - 版本信息数据
     */
    function showVersionNotification(versionData) {
        // 创建模态框元素
        const modalId = 'versionNotificationModal';
        
        // 检查是否已存在相同ID的模态框
        if (document.getElementById(modalId)) {
            return;
        }
        
        // 构建更新内容HTML
        let releaseNotesHtml = '';
        if (versionData.release_notes && versionData.release_notes.length > 0) {
            releaseNotesHtml = '<ul class="list-group list-group-flush mt-3">';
            versionData.release_notes.forEach(note => {
                releaseNotesHtml += `<li class="list-group-item"><i class="fas fa-check-circle text-success me-2"></i>${note}</li>`;
            });
            releaseNotesHtml += '</ul>';
        }
        
        // 创建模态框HTML
        const modalHtml = `
        <div class="modal fade" id="${modalId}" tabindex="-1" aria-labelledby="versionNotificationModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title" id="versionNotificationModalLabel">
                            <i class="fas fa-info-circle me-2"></i>系统已更新
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
                    </div>
                    <div class="modal-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0">
                                <i class="fas fa-code-branch fa-3x text-primary"></i>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h5 class="mb-1">系统已更新到 v${versionData.version}</h5>
                                <p class="text-muted mb-0">以下是此版本的更新内容：</p>
                            </div>
                        </div>
                        ${releaseNotesHtml}
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">我知道了</button>
                    </div>
                </div>
            </div>
        </div>`;
        
        // 将模态框添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // 显示模态框
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();
    }
})();
