import json
import os
import tempfile
import logging
import re
from threading import Lock
from queue import Queue
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from .ssh_proxy import SSHProxy

# 配置日志
logger = logging.getLogger(__name__)

class AnsibleAPI:
    def __init__(self, ssh_proxy=None):
        """初始化AnsibleAPI"""
        # 使用绝对路径指定ansible-playbook命令
        self.ansible_playbook_cmd = "/usr/bin/ansible-playbook"
        self.ansible_cmd = "ansible"
        self.inventory_file = "/etc/ansible/hosts"  # 默认库存文件
        self.running_tasks = {}  # 存储正在运行的任务状态
        self.task_lock = Lock()  # 用于线程安全访问任务状态
        self.task_queue = Queue()
        self.max_workers = 5
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        
        # 设置SSH代理
        self.ssh_proxy = ssh_proxy
        if self.ssh_proxy:
            logger.debug("SSH代理已设置")

    def set_ssh_proxy(self, ssh_proxy):
        """设置SSH代理连接"""
        self.ssh_proxy = ssh_proxy
        logger.debug("SSH代理已设置")

    def run_playbook(self, task_id, playbook_path, extra_vars=None, inventory_path=None, callback=None):
        """
        运行Ansible Playbook
        :param task_id: 任务ID
        :param playbook_path: Playbook路径
        :param extra_vars: 额外变量
        :param inventory_path: 库存文件路径
        :param callback: 回调函数
        :return: 执行结果
        """
        try:
            # 检查SSH连接
            if not self.ssh_proxy:
                logger.error("SSH连接未建立")
                return {
                    'rc': 1,
                    'stdout': '',
                    'stderr': 'SSH连接未建立',
                    'formatted_output': '执行Playbook异常: SSH连接未建立',
                    'end_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
                }
            
            # 检查是否有任务正在运行
            with self.task_lock:
                # 如果任务已存在，并且状态为运行中，则返回错误
                if task_id in self.running_tasks and self.running_tasks[task_id].get('status') == 'running':
                    logger.warning(f"任务ID {task_id} 已在运行中")
                    return {'error': 'Task already running'}
                
                # 如果任务已存在但状态不是"running"，则清除旧任务状态
                if task_id in self.running_tasks:
                    logger.info(f"清除任务ID {task_id} 的旧状态: {self.running_tasks[task_id].get('status')}")
                    del self.running_tasks[task_id]
                
                # 初始化新任务状态
                self.running_tasks[task_id] = {
                    'status': 'pending',
                    'start_time': datetime.utcnow(),
                    'result': None
                }
            
            def execute_playbook():
                """执行Playbook的函数"""
                try:
                    with self.task_lock:
                        self.running_tasks[task_id]['status'] = 'running'
                    
                    # 构建ansible-playbook命令
                    # 使用提供的inventory_path或默认的inventory_file
                    inventory = inventory_path if inventory_path else f"-i {self.inventory_file}"
                    
                    # 先查找ansible-playbook的确切路径
                    find_cmd = "which ansible-playbook || find / -name ansible-playbook 2>/dev/null | head -1"
                    logger.debug(f"查找ansible-playbook路径: {find_cmd}")
                    result = self.ssh_proxy.execute_command(None, find_cmd)
                    
                    ansible_path = result.get('stdout', '').strip()
                    if ansible_path:
                        logger.debug(f"找到ansible-playbook路径: {ansible_path}")
                        self.ansible_playbook_cmd = ansible_path
                    else:
                        logger.warning("未找到ansible-playbook路径，使用默认路径")
                    
                    # 添加环境变量设置
                    cmd = f"source /etc/profile && {self.ansible_playbook_cmd} {inventory}"
                    
                    # 添加playbook路径，确保它是一个有效的路径
                    if playbook_path.startswith('/'):
                        # 绝对路径
                        cmd += f" {playbook_path}"
                    else:
                        # 相对路径，假设是相对于/etc/ansible/playbooks
                        cmd += f" /etc/ansible/playbooks/{playbook_path}"
                    
                    # 添加详细输出选项和计时选项
                    # 移除 -vvv 参数，不需要详细日志
                    # cmd += " -vvv"
                    
                    # 添加差异选项，但移除不支持的timing选项
                    # 移除 --diff 参数，不需要差异信息
                    # cmd += " --diff"
                    
                    # 如果有额外变量，添加到命令中
                    if extra_vars:
                        # 处理extra_vars，确保它是字典类型
                        if isinstance(extra_vars, str):
                            try:
                                import json
                                extra_vars_dict = json.loads(extra_vars)
                                # 移除临时库存文件路径，避免将其作为变量传递给ansible
                                if 'temp_inventory' in extra_vars_dict:
                                    extra_vars_dict.pop('temp_inventory')
                                extra_vars_str = ' '.join(f'{key}={value}' for key, value in extra_vars_dict.items() if key != 'temp_inventory')
                            except Exception as e:
                                logger.warning(f"解析extra_vars失败: {str(e)}")
                                extra_vars_str = ''
                        elif isinstance(extra_vars, dict):
                            # 移除临时库存文件路径，避免将其作为变量传递给ansible
                            extra_vars_dict = {k: v for k, v in extra_vars.items() if k != 'temp_inventory'}
                            extra_vars_str = ' '.join(f'{key}={value}' for key, value in extra_vars_dict.items())
                        else:
                            extra_vars_str = str(extra_vars)
                        
                        if extra_vars_str:
                            cmd += f" -e '{extra_vars_str}'"
                    
                    logger.debug(f"执行命令: {cmd}")
                    
                    # 通过SSH执行命令
                    result = self.ssh_proxy.execute_command(None, cmd)
                    
                    # 解析结果
                    stdout_str = result.get('stdout', '')
                    stderr_str = result.get('stderr', '')
                    rc = result.get('rc', 0)
                    
                    # 格式化输出
                    formatted_output = self.format_ansible_output(stdout_str, stderr_str)
                    
                    # 更新任务状态
                    with self.task_lock:
                        self.running_tasks[task_id].update({
                            'status': 'completed',
                            'end_time': datetime.utcnow(),
                            'result': {
                                'rc': rc,
                                'stdout': stdout_str,
                                'stderr': stderr_str,
                                'formatted_output': formatted_output
                            }
                        })
                    
                    # 如果有回调函数，调用它
                    if callback:
                        callback(self.running_tasks[task_id])
                    
                    return {
                        'rc': rc,
                        'stdout': stdout_str,
                        'stderr': stderr_str,
                        'formatted_output': formatted_output,
                        'end_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                except Exception as e:
                    logger.error(f"执行Playbook异常: {str(e)}")
                    error_result = {
                        'rc': 1,
                        'stdout': '',
                        'stderr': str(e),
                        'formatted_output': f"执行Playbook异常: {str(e)}",
                        'end_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
                    }
                    
                    # 更新任务状态
                    with self.task_lock:
                        self.running_tasks[task_id].update({
                            'status': 'failed',
                            'end_time': datetime.utcnow(),
                            'result': error_result
                        })
                    
                    # 如果有回调函数，调用它
                    if callback:
                        callback(self.running_tasks[task_id])
                    
                    return error_result
            
            # 将任务添加到队列
            self.task_queue.put((task_id, execute_playbook))
            # 提交任务到线程池并等待结果
            future = self.executor.submit(execute_playbook)
            # 等待任务完成并返回结果
            result = future.result()
            return result
            
        except Exception as e:
            logger.error(f"提交任务异常: {str(e)}")
            return {
                'rc': 1,
                'stdout': '',
                'stderr': str(e),
                'formatted_output': f"提交任务异常: {str(e)}",
                'end_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
            }

    def format_ansible_output(self, stdout, stderr):
        """格式化Ansible输出结果

        Args:
            stdout: 标准输出
            stderr: 标准错误输出

        Returns:
            str: 格式化后的HTML输出
        """
        # 添加CSS样式
        output = """<style>
.ansible-output { 
    font-family: monospace; 
    white-space: pre-wrap; 
    line-height: 1.5; 
    padding: 10px; 
    background-color: #f5f5f5; 
    border-radius: 5px; 
}
.host-success { color: #27ae60; }
.host-changed { color: #f39c12; }
.host-failed { color: #e74c3c; }
.ansible-error { color: #e74c3c; font-weight: bold; }
</style>
<div class='ansible-output'>
<pre>"""

        # 处理标准输出
        if stdout:
            # 尝试解析JSON输出
            try:
                import json
                json_output = json.loads(stdout)
                # 如果是JSON格式，美化输出
                output += json.dumps(json_output, indent=2, ensure_ascii=False)
            except:
                # 不是JSON格式，直接添加输出
                # 为不同的输出类型添加颜色
                for line in stdout.splitlines():
                    if 'ok:' in line:
                        output += f'<span class="host-success">{line}</span>\n'
                    elif 'changed:' in line:
                        output += f'<span class="host-changed">{line}</span>\n'
                    elif 'failed:' in line or 'fatal:' in line:
                        output += f'<span class="host-failed">{line}</span>\n'
                    else:
                        output += line + '\n'

        # 处理标准错误输出
        if stderr:
            output += '\n<span class="ansible-error">错误输出:</span>\n'
            output += stderr

        output += "</pre>\n</div>"
        return output

    def _format_playbook_output(self, stdout, stderr):
        """格式化Playbook输出（已弃用，使用format_ansible_output代替）"""
        return self.format_ansible_output(stdout, stderr)

    def _format_adhoc_output(self, stdout, stderr):
        """格式化Ad-hoc命令输出（已弃用，使用format_ansible_output代替）"""
        return self.format_ansible_output(stdout, stderr)

    def run_adhoc_command(self, hosts, module_name, module_args=None):
        """执行ad-hoc命令
        
        Args:
            hosts: 目标主机，可以是字符串或列表
            module_name: 模块名称
            module_args: 模块参数
            
        Returns:
            dict: 命令执行结果
        """
        try:
            # 确保SSH代理已设置
            if not self.ssh_proxy:
                raise ValueError("SSH代理未设置")
            
            logging.debug("SSH代理已设置")
            
            # 基础命令
            cmd = f"{self.ansible_cmd} "
            
            # 处理主机参数
            if isinstance(hosts, list):
                if not hosts:  # 如果主机列表为空
                    return {
                        'rc': 1,
                        'stdout': '',
                        'stderr': '没有指定目标服务器',
                        'formatted_output': "<div class='ansible-error'>没有指定目标服务器</div>"
                    }
                
                host_str = ','.join(hosts)
                # 对于多个主机，使用"all"作为目标，并通过-i指定临时清单
                cmd += f"all -i \"{host_str},\" "
            else:
                if not hosts:  # 如果主机字符串为空
                    return {
                        'rc': 1,
                        'stdout': '',
                        'stderr': '没有指定目标服务器',
                        'formatted_output': "<div class='ansible-error'>没有指定目标服务器</div>"
                    }
                
                host_str = hosts
                # 单个主机也使用同样的格式保持一致性
                cmd += f"all -i \"{host_str},\" "
            
            # 添加模块名称
            cmd += f"-m {module_name} "
            
            # 添加模块参数
            if module_args:
                # 记录模块参数
                logging.debug(f"模块参数: {module_args}, 类型: {type(module_args)}")
                
                # 特殊处理command和shell模块，确保命令参数正确传递
                if (module_name == 'command' or module_name == 'shell') and isinstance(module_args, str):
                    # 如果是字符串，直接作为命令参数
                    cmd += f"-a \"{module_args}\" "
                    logging.debug(f"使用字符串命令参数: {module_args}")
                elif (module_name == 'command' or module_name == 'shell') and isinstance(module_args, dict) and 'cmd' in module_args:
                    # 如果是字典且包含cmd键，使用cmd的值作为命令参数
                    cmd += f"-a \"{module_args['cmd']}\" "
                    logging.debug(f"使用字典中的cmd参数: {module_args['cmd']}")
                elif isinstance(module_args, dict):
                    # 将字典转换为字符串
                    args_str = ' '.join(f'{key}={value}' for key, value in module_args.items())
                    cmd += f"-a \"{args_str}\" "
                    logging.debug(f"使用字典转换的参数: {args_str}")
                else:
                    # 其他情况，直接使用参数
                    cmd += f"-a \"{module_args}\" "
                    logging.debug(f"使用其他类型参数: {module_args}")
            
            # 添加SSH连接参数，确保可以连接到未知主机
            # 修复引号嵌套问题
            cmd += "--ssh-common-args=\"-o StrictHostKeyChecking=no\" "
            
            logging.debug(f"执行Ad-hoc命令: {cmd}")
            
            # 通过SSH代理执行命令
            result = self.ssh_proxy.execute_command(None, cmd)
            
            # 解析结果
            stdout_str = result.get('stdout', '')
            stderr_str = result.get('stderr', '')
            rc = result.get('rc', 0)
            
            # 格式化输出
            formatted_output = self.format_ansible_output(stdout_str, stderr_str)
            
            # 返回结果
            return {
                'rc': rc,
                'stdout': stdout_str,
                'stderr': stderr_str,
                'formatted_output': formatted_output,
                'end_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
            }
        except Exception as e:
            logging.error(f"执行Ad-hoc命令异常: {str(e)}")
            return {
                'rc': 1,
                'stdout': '',
                'stderr': str(e),
                'formatted_output': f"<div class='ansible-error'>执行Ad-hoc命令异常: {str(e)}</div>",
                'end_time': datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')
            }

    def add_host_to_inventory(self, hostname, ip_address):
        """添加主机到inventory文件"""
        with open(self.inventory_file, 'a') as f:
            f.write(f'{hostname} ansible_host={ip_address}\n')
    
    def get_task_status(self, task_id):
        """获取任务状态和结果"""
        with self.task_lock:
            if task_id not in self.running_tasks:
                return {'error': 'Task not found'}
            return self.running_tasks[task_id]
    
    def cancel_task(self, task_id):
        """取消正在运行的任务"""
        with self.task_lock:
            if task_id not in self.running_tasks:
                return {'error': 'Task not found'}
            if self.running_tasks[task_id]['status'] == 'running':
                self.running_tasks[task_id]['status'] = 'cancelled'
                return {'message': 'Task cancelled'}
            return {'error': 'Task cannot be cancelled'}
    
    def cleanup_task(self, task_id):
        """清理已完成的任务"""
        with self.task_lock:
            if task_id in self.running_tasks:
                if self.running_tasks[task_id]['status'] in ['completed', 'failed', 'cancelled']:
                    del self.running_tasks[task_id]
                    return {'message': 'Task cleaned up'}
                return {'error': 'Task is still running'}
            return {'error': 'Task not found'}