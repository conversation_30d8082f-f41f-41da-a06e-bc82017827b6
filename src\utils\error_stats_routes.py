#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
错误统计API路由
提供错误监控和统计的API接口
"""

from flask import Blueprint, request, jsonify
from .error_handler import (
    create_success_response, 
    create_error_response,
    handle_exceptions,
    ErrorCategory,
    ErrorLevel
)
from .error_monitor import error_monitor

# 创建错误统计蓝图
error_stats_bp = Blueprint('error_stats', __name__)

@error_stats_bp.route('/api/error-stats', methods=['GET'])
@handle_exceptions(category=ErrorCategory.SYSTEM, level=ErrorLevel.LOW)
def get_error_stats():
    """获取错误统计信息"""
    try:
        # 获取查询参数
        hours = request.args.get('hours', 24, type=int)
        
        # 验证参数
        if hours <= 0 or hours > 8760:  # 最多一年
            return create_error_response(
                "时间范围参数无效，必须在1-8760小时之间", 
                "INVALID_HOURS_PARAMETER", 
                400
            )
        
        # 获取错误统计
        stats = error_monitor.get_stats(hours)
        
        # 添加额外的统计信息
        enhanced_stats = {
            **stats,
            'query_parameters': {
                'hours': hours,
                'description': f'最近{hours}小时的错误统计'
            },
            'summary': {
                'has_errors': stats['total_errors'] > 0,
                'error_rate': _calculate_error_rate(stats['total_errors'], hours),
                'most_common_category': _get_most_common_category(stats.get('errors_by_category', {})),
                'most_common_level': _get_most_common_level(stats.get('errors_by_level', {}))
            }
        }
        
        return create_success_response(enhanced_stats, f"成功获取最近{hours}小时的错误统计")
        
    except Exception as e:
        return create_error_response(
            f"获取错误统计失败: {str(e)}", 
            "ERROR_STATS_FAILED", 
            500
        )

@error_stats_bp.route('/api/error-details', methods=['GET'])
@handle_exceptions(category=ErrorCategory.SYSTEM, level=ErrorLevel.LOW)
def get_error_details():
    """获取错误详情"""
    try:
        # 获取查询参数
        category = request.args.get('category')
        error_code = request.args.get('error_code')
        limit = request.args.get('limit', 100, type=int)
        
        # 验证参数
        if limit <= 0 or limit > 1000:
            return create_error_response(
                "限制参数无效，必须在1-1000之间", 
                "INVALID_LIMIT_PARAMETER", 
                400
            )
        
        # 获取错误详情
        errors = error_monitor.get_error_details(
            error_code=error_code,
            category=category,
            limit=limit
        )
        
        # 构建响应数据
        response_data = {
            'errors': errors,
            'total_count': len(errors),
            'query_parameters': {
                'category': category,
                'error_code': error_code,
                'limit': limit
            },
            'filters_applied': {
                'category_filter': category is not None,
                'error_code_filter': error_code is not None
            }
        }
        
        return create_success_response(
            response_data, 
            f"成功获取{len(errors)}条错误详情"
        )
        
    except Exception as e:
        return create_error_response(
            f"获取错误详情失败: {str(e)}", 
            "ERROR_DETAILS_FAILED", 
            500
        )

@error_stats_bp.route('/api/error-report', methods=['GET'])
@handle_exceptions(category=ErrorCategory.SYSTEM, level=ErrorLevel.LOW)
def get_error_report():
    """获取错误报告"""
    try:
        # 获取查询参数
        hours = request.args.get('hours', 24, type=int)
        format_type = request.args.get('format', 'json')
        
        # 验证参数
        if hours <= 0 or hours > 8760:
            return create_error_response(
                "时间范围参数无效，必须在1-8760小时之间", 
                "INVALID_HOURS_PARAMETER", 
                400
            )
        
        if format_type not in ['json', 'text']:
            return create_error_response(
                "格式参数无效，支持的格式: json, text", 
                "INVALID_FORMAT_PARAMETER", 
                400
            )
        
        if format_type == 'text':
            # 生成文本格式报告
            report = error_monitor.generate_report(hours)
            return create_success_response(
                {'report': report, 'format': 'text'}, 
                f"成功生成最近{hours}小时的文本错误报告"
            )
        else:
            # 生成JSON格式报告
            stats = error_monitor.get_stats(hours)
            report_data = {
                'report_info': {
                    'generated_at': stats.get('last_updated'),
                    'time_range_hours': hours,
                    'report_type': 'comprehensive'
                },
                'summary': {
                    'total_errors': stats['total_errors'],
                    'error_categories': len(stats.get('errors_by_category', {})),
                    'error_levels': len(stats.get('errors_by_level', {})),
                    'time_span': f"最近{hours}小时"
                },
                'detailed_stats': stats
            }
            
            return create_success_response(
                report_data, 
                f"成功生成最近{hours}小时的JSON错误报告"
            )
        
    except Exception as e:
        return create_error_response(
            f"生成错误报告失败: {str(e)}", 
            "ERROR_REPORT_FAILED", 
            500
        )

@error_stats_bp.route('/api/error-monitor/status', methods=['GET'])
@handle_exceptions(category=ErrorCategory.SYSTEM, level=ErrorLevel.LOW)
def get_monitor_status():
    """获取错误监控状态"""
    try:
        # 获取监控状态信息
        status_info = {
            'monitor_enabled': True,
            'storage_path': error_monitor.storage_path,
            'buffer_size': len(error_monitor.errors_buffer),
            'last_error_time': None,
            'monitor_health': 'healthy'
        }
        
        # 获取最后一个错误的时间
        if error_monitor.errors_buffer:
            last_error = error_monitor.errors_buffer[-1]
            status_info['last_error_time'] = last_error.get('timestamp')
        
        # 检查监控健康状态
        if len(error_monitor.errors_buffer) > 900:  # 接近缓冲区限制
            status_info['monitor_health'] = 'warning'
            status_info['warning_message'] = '错误缓冲区接近满载，建议清理旧记录'
        
        return create_success_response(status_info, "成功获取错误监控状态")
        
    except Exception as e:
        return create_error_response(
            f"获取监控状态失败: {str(e)}", 
            "MONITOR_STATUS_FAILED", 
            500
        )

@error_stats_bp.route('/api/error-monitor/clear', methods=['POST'])
@handle_exceptions(category=ErrorCategory.SYSTEM, level=ErrorLevel.MEDIUM)
def clear_old_errors():
    """清理旧错误记录"""
    try:
        # 获取请求参数
        data = request.get_json() or {}
        days = data.get('days', 7)
        
        # 验证参数
        if not isinstance(days, int) or days <= 0 or days > 365:
            return create_error_response(
                "天数参数无效，必须在1-365之间", 
                "INVALID_DAYS_PARAMETER", 
                400
            )
        
        # 记录清理前的状态
        before_count = len(error_monitor.errors_buffer)
        
        # 执行清理
        error_monitor.clear_old_errors(days)
        
        # 记录清理后的状态
        after_count = len(error_monitor.errors_buffer)
        cleared_count = before_count - after_count
        
        result = {
            'cleared_count': cleared_count,
            'remaining_count': after_count,
            'days_threshold': days,
            'operation': 'clear_old_errors'
        }
        
        return create_success_response(
            result, 
            f"成功清理{cleared_count}条超过{days}天的错误记录"
        )
        
    except Exception as e:
        return create_error_response(
            f"清理错误记录失败: {str(e)}", 
            "CLEAR_ERRORS_FAILED", 
            500
        )

# 辅助函数
def _calculate_error_rate(total_errors: int, hours: int) -> float:
    """计算错误率（每小时错误数）"""
    if hours == 0:
        return 0.0
    return round(total_errors / hours, 2)

def _get_most_common_category(errors_by_category: dict) -> str:
    """获取最常见的错误分类"""
    if not errors_by_category:
        return "无"
    
    return max(errors_by_category.items(), key=lambda x: x[1])[0]

def _get_most_common_level(errors_by_level: dict) -> str:
    """获取最常见的错误级别"""
    if not errors_by_level:
        return "无"

    return max(errors_by_level.items(), key=lambda x: x[1])[0]

# 测试路由 - 用于生成测试错误数据
@error_stats_bp.route('/api/test-error', methods=['POST'])
@handle_exceptions(category=ErrorCategory.SYSTEM, level=ErrorLevel.LOW)
def test_error():
    """测试错误生成（仅用于开发和测试）"""
    try:
        data = request.get_json() or {}
        error_type = data.get('type', 'generic')

        if error_type == 'database':
            from .error_handler import DatabaseError
            raise DatabaseError("测试数据库错误", details={'test': True})
        elif error_type == 'network':
            from .error_handler import NetworkError
            raise NetworkError("测试网络错误", details={'test': True})
        elif error_type == 'validation':
            from .error_handler import ValidationError
            raise ValidationError("测试验证错误", details={'test': True})
        elif error_type == 'business':
            from .error_handler import BusinessError
            raise BusinessError("测试业务错误", details={'test': True})
        else:
            raise Exception("测试通用错误")

    except Exception as e:
        # 这里的异常会被装饰器捕获并记录
        raise
