#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
通用工具函数模块
整合项目中重复使用的工具函数
"""

import re
import math
import logging
from datetime import datetime, date
from typing import Any, Optional, Union, List, Dict
from decimal import Decimal, InvalidOperation

logger = logging.getLogger(__name__)

# ==================== 数据类型转换工具 ====================

def safe_int(value: Any, default: int = 0) -> int:
    """安全的整数转换"""
    if value is None:
        return default
    
    try:
        if isinstance(value, (int, float)):
            return int(value)
        elif isinstance(value, str):
            # 移除空格和逗号
            cleaned = value.strip().replace(',', '')
            if cleaned == '':
                return default
            return int(float(cleaned))
        elif isinstance(value, Decimal):
            return int(value)
        else:
            return default
    except (ValueError, TypeError, InvalidOperation):
        return default

def safe_float(value: Any, default: float = 0.0) -> float:
    """安全的浮点数转换"""
    if value is None:
        return default
    
    try:
        if isinstance(value, (int, float)):
            return float(value)
        elif isinstance(value, str):
            # 移除空格和逗号
            cleaned = value.strip().replace(',', '')
            if cleaned == '':
                return default
            return float(cleaned)
        elif isinstance(value, Decimal):
            return float(value)
        else:
            return default
    except (ValueError, TypeError, InvalidOperation):
        return default

def safe_decimal(value: Any, default: Decimal = Decimal('0')) -> Decimal:
    """安全的Decimal转换"""
    if value is None:
        return default
    
    try:
        if isinstance(value, Decimal):
            return value
        elif isinstance(value, (int, float)):
            return Decimal(str(value))
        elif isinstance(value, str):
            # 移除空格和逗号
            cleaned = value.strip().replace(',', '')
            if cleaned == '':
                return default
            return Decimal(cleaned)
        else:
            return default
    except (ValueError, TypeError, InvalidOperation):
        return default

def safe_str(value: Any, default: str = '') -> str:
    """安全的字符串转换"""
    if value is None:
        return default
    
    try:
        return str(value).strip()
    except (ValueError, TypeError):
        return default

# ==================== 日期时间工具 ====================

def format_date(date_obj: Any, format_str: str = '%Y-%m-%d') -> str:
    """统一日期格式化"""
    if date_obj is None:
        return ''
    
    try:
        if isinstance(date_obj, str):
            # 尝试解析字符串日期
            if date_obj.strip() == '':
                return ''
            # 常见日期格式
            for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y-%m-%d %H:%M:%S', '%Y/%m/%d %H:%M:%S']:
                try:
                    parsed_date = datetime.strptime(date_obj.strip(), fmt)
                    return parsed_date.strftime(format_str)
                except ValueError:
                    continue
            return date_obj  # 如果无法解析，返回原字符串
        elif isinstance(date_obj, datetime):
            return date_obj.strftime(format_str)
        elif isinstance(date_obj, date):
            return date_obj.strftime(format_str)
        else:
            return str(date_obj)
    except Exception as e:
        logger.warning(f"日期格式化失败: {date_obj}, 错误: {e}")
        return str(date_obj) if date_obj else ''

def format_datetime(datetime_obj: Any, format_str: str = '%Y-%m-%d %H:%M:%S') -> str:
    """统一日期时间格式化"""
    return format_date(datetime_obj, format_str)

def get_current_year() -> int:
    """获取当前年份"""
    return datetime.now().year

def get_current_date() -> str:
    """获取当前日期字符串"""
    return datetime.now().strftime('%Y-%m-%d')

def get_current_datetime() -> str:
    """获取当前日期时间字符串"""
    return datetime.now().strftime('%Y-%m-%d %H:%M:%S')

# ==================== 数据格式化工具 ====================

def format_number(value: Any, decimal_places: int = 2, use_comma: bool = True) -> str:
    """格式化数字显示"""
    num = safe_float(value)
    
    if num == 0:
        return '0'
    
    try:
        if decimal_places == 0:
            formatted = f"{int(num)}"
        else:
            formatted = f"{num:.{decimal_places}f}"
        
        if use_comma and abs(num) >= 1000:
            # 添加千位分隔符
            parts = formatted.split('.')
            parts[0] = f"{int(parts[0]):,}"
            formatted = '.'.join(parts)
        
        return formatted
    except Exception as e:
        logger.warning(f"数字格式化失败: {value}, 错误: {e}")
        return str(value) if value is not None else '0'

def format_percentage(value: Any, decimal_places: int = 2) -> str:
    """格式化百分比显示"""
    num = safe_float(value)
    try:
        return f"{num:.{decimal_places}f}%"
    except Exception as e:
        logger.warning(f"百分比格式化失败: {value}, 错误: {e}")
        return "0.00%"

def format_file_size(size_bytes: int) -> str:
    """格式化文件大小显示"""
    if size_bytes == 0:
        return "0 B"
    
    size_names = ["B", "KB", "MB", "GB", "TB"]
    i = int(math.floor(math.log(size_bytes, 1024)))
    p = math.pow(1024, i)
    s = round(size_bytes / p, 2)
    
    return f"{s} {size_names[i]}"

# ==================== 字符串处理工具 ====================

def clean_string(value: str) -> str:
    """清理字符串（移除多余空格、特殊字符等）"""
    if not value:
        return ''
    
    # 移除首尾空格
    cleaned = value.strip()
    
    # 替换多个连续空格为单个空格
    cleaned = re.sub(r'\s+', ' ', cleaned)
    
    return cleaned

def truncate_string(value: str, max_length: int = 50, suffix: str = '...') -> str:
    """截断字符串"""
    if not value or len(value) <= max_length:
        return value
    
    return value[:max_length - len(suffix)] + suffix

def get_table_display_name(table_name: str) -> str:
    """获取表的显示名称"""
    if not table_name:
        return ''
    
    # 移除excel_data_前缀
    display_name = table_name.replace('excel_data_', '')
    # 将下划线替换为空格
    return display_name.replace('_', ' ')

def camel_to_snake(name: str) -> str:
    """驼峰命名转下划线命名"""
    s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
    return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

def snake_to_camel(name: str) -> str:
    """下划线命名转驼峰命名"""
    components = name.split('_')
    return components[0] + ''.join(x.title() for x in components[1:])

# ==================== 数据验证工具 ====================

def is_valid_email(email: str) -> bool:
    """验证邮箱格式"""
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def is_valid_phone(phone: str) -> bool:
    """验证手机号格式"""
    if not phone:
        return False
    
    # 简单的手机号验证（11位数字）
    pattern = r'^1[3-9]\d{9}$'
    return re.match(pattern, phone) is not None

def is_valid_id_card(id_card: str) -> bool:
    """验证身份证号格式"""
    if not id_card:
        return False
    
    # 18位身份证号验证
    pattern = r'^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$'
    return re.match(pattern, id_card) is not None

# ==================== 分页工具 ====================

def paginate_data(data: List[Any], page: int = 1, per_page: int = 20) -> Dict[str, Any]:
    """数据分页处理"""
    if not data:
        return {
            'data': [],
            'total': 0,
            'page': page,
            'per_page': per_page,
            'pages': 0,
            'has_prev': False,
            'has_next': False
        }
    
    total = len(data)
    pages = math.ceil(total / per_page)
    
    # 确保页码在有效范围内
    page = max(1, min(page, pages))
    
    start = (page - 1) * per_page
    end = start + per_page
    
    return {
        'data': data[start:end],
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': pages,
        'has_prev': page > 1,
        'has_next': page < pages
    }

def get_pagination_info(total: int, page: int = 1, per_page: int = 20) -> Dict[str, Any]:
    """获取分页信息"""
    pages = math.ceil(total / per_page) if total > 0 else 0
    page = max(1, min(page, pages)) if pages > 0 else 1
    
    return {
        'total': total,
        'page': page,
        'per_page': per_page,
        'pages': pages,
        'has_prev': page > 1,
        'has_next': page < pages,
        'prev_num': page - 1 if page > 1 else None,
        'next_num': page + 1 if page < pages else None
    }

# ==================== 错误处理工具 ====================

def safe_execute(func, *args, default_return=None, log_error=True, **kwargs):
    """安全执行函数，捕获异常并返回默认值"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        if log_error:
            logger.error(f"执行函数 {func.__name__} 时发生错误: {str(e)}")
        return default_return

def get_error_message(exception: Exception) -> str:
    """获取友好的错误消息"""
    error_type = type(exception).__name__
    error_message = str(exception)
    
    # 常见错误的友好提示
    friendly_messages = {
        'ConnectionError': '网络连接错误，请检查网络设置',
        'TimeoutError': '操作超时，请稍后重试',
        'PermissionError': '权限不足，请联系管理员',
        'FileNotFoundError': '文件未找到',
        'ValueError': '数据格式错误',
        'KeyError': '缺少必要的参数',
        'AttributeError': '对象属性错误'
    }
    
    return friendly_messages.get(error_type, f"{error_type}: {error_message}")

# ==================== 配置工具 ====================

def get_env_bool(env_var: str, default: bool = False) -> bool:
    """从环境变量获取布尔值"""
    import os
    value = os.environ.get(env_var, '').lower()
    return value in ('true', '1', 'yes', 'on')

def get_env_int(env_var: str, default: int = 0) -> int:
    """从环境变量获取整数值"""
    import os
    return safe_int(os.environ.get(env_var), default)

def get_env_float(env_var: str, default: float = 0.0) -> float:
    """从环境变量获取浮点数值"""
    import os
    return safe_float(os.environ.get(env_var), default)
