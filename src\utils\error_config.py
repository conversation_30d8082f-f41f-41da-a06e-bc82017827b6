#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
错误处理配置
定义错误处理的配置参数和策略
"""

import os
from typing import Dict, List, Any
from enum import Enum

class LogLevel(Enum):
    """日志级别"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"

class ErrorHandlingConfig:
    """错误处理配置类"""
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', LogLevel.INFO.value)
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE_PATH = os.environ.get('LOG_FILE_PATH', 'logs/system.log')
    LOG_MAX_SIZE = int(os.environ.get('LOG_MAX_SIZE', '10485760'))  # 10MB
    LOG_BACKUP_COUNT = int(os.environ.get('LOG_BACKUP_COUNT', '5'))
    
    # 错误监控配置
    ERROR_MONITOR_ENABLED = os.environ.get('ERROR_MONITOR_ENABLED', 'true').lower() == 'true'
    ERROR_STORAGE_PATH = os.environ.get('ERROR_STORAGE_PATH', 'logs/error_monitor.json')
    ERROR_RETENTION_DAYS = int(os.environ.get('ERROR_RETENTION_DAYS', '7'))
    ERROR_BUFFER_SIZE = int(os.environ.get('ERROR_BUFFER_SIZE', '1000'))
    
    # 错误报告配置
    ERROR_REPORT_ENABLED = os.environ.get('ERROR_REPORT_ENABLED', 'true').lower() == 'true'
    ERROR_REPORT_INTERVAL_HOURS = int(os.environ.get('ERROR_REPORT_INTERVAL_HOURS', '24'))
    ERROR_REPORT_EMAIL_ENABLED = os.environ.get('ERROR_REPORT_EMAIL_ENABLED', 'false').lower() == 'true'
    
    # 重试配置
    DEFAULT_RETRY_ATTEMPTS = int(os.environ.get('DEFAULT_RETRY_ATTEMPTS', '3'))
    DEFAULT_RETRY_DELAY = float(os.environ.get('DEFAULT_RETRY_DELAY', '1.0'))
    MAX_RETRY_DELAY = float(os.environ.get('MAX_RETRY_DELAY', '60.0'))
    
    # 错误分类配置
    CRITICAL_ERROR_CATEGORIES = [
        'database',
        'authentication',
        'system'
    ]
    
    HIGH_PRIORITY_ERROR_PATTERNS = [
        'connection refused',
        'access denied',
        'permission denied',
        'out of memory',
        'disk full',
        'timeout'
    ]
    
    # 错误响应配置
    SHOW_DETAILED_ERRORS = os.environ.get('SHOW_DETAILED_ERRORS', 'false').lower() == 'true'
    ERROR_RESPONSE_TIMEOUT = int(os.environ.get('ERROR_RESPONSE_TIMEOUT', '30'))
    
    # 数据库错误配置
    DATABASE_ERROR_PATTERNS = {
        'connection': [
            'connection refused',
            'connection timeout',
            'connection lost',
            'connection failed'
        ],
        'authentication': [
            'access denied',
            'authentication failed',
            'invalid credentials'
        ],
        'syntax': [
            'syntax error',
            'invalid query',
            'unknown column',
            'table doesn\'t exist'
        ],
        'constraint': [
            'duplicate entry',
            'foreign key constraint',
            'unique constraint',
            'check constraint'
        ]
    }
    
    # 网络错误配置
    NETWORK_ERROR_PATTERNS = {
        'timeout': [
            'connection timeout',
            'read timeout',
            'socket timeout'
        ],
        'unreachable': [
            'network unreachable',
            'host unreachable',
            'no route to host'
        ],
        'dns': [
            'name resolution failed',
            'dns lookup failed',
            'unknown host'
        ]
    }
    
    # 文件操作错误配置
    FILE_ERROR_PATTERNS = {
        'permission': [
            'permission denied',
            'access denied',
            'operation not permitted'
        ],
        'not_found': [
            'file not found',
            'no such file',
            'directory not found'
        ],
        'space': [
            'no space left',
            'disk full',
            'quota exceeded'
        ]
    }

class ErrorResponseTemplates:
    """错误响应模板"""
    
    GENERIC_ERROR = {
        'message': '系统发生错误，请稍后重试',
        'suggestion': '如果问题持续存在，请联系系统管理员'
    }
    
    DATABASE_ERROR = {
        'message': '数据库操作失败',
        'suggestion': '请检查数据库连接状态，或联系系统管理员'
    }
    
    NETWORK_ERROR = {
        'message': '网络连接失败',
        'suggestion': '请检查网络连接，或稍后重试'
    }
    
    AUTHENTICATION_ERROR = {
        'message': '身份验证失败',
        'suggestion': '请检查用户名和密码，或联系管理员'
    }
    
    VALIDATION_ERROR = {
        'message': '数据验证失败',
        'suggestion': '请检查输入数据的格式和内容'
    }
    
    PERMISSION_ERROR = {
        'message': '权限不足',
        'suggestion': '请联系管理员获取相应权限'
    }
    
    BUSINESS_ERROR = {
        'message': '业务逻辑错误',
        'suggestion': '请检查操作流程，或联系业务管理员'
    }
    
    SYSTEM_ERROR = {
        'message': '系统内部错误',
        'suggestion': '系统正在处理中，请稍后重试'
    }

class ErrorHandlingStrategies:
    """错误处理策略"""
    
    @staticmethod
    def get_retry_strategy(error_category: str) -> Dict[str, Any]:
        """获取重试策略"""
        strategies = {
            'database': {
                'max_attempts': 3,
                'delay': 2.0,
                'backoff_factor': 2.0,
                'max_delay': 30.0
            },
            'network': {
                'max_attempts': 5,
                'delay': 1.0,
                'backoff_factor': 1.5,
                'max_delay': 60.0
            },
            'external_api': {
                'max_attempts': 3,
                'delay': 5.0,
                'backoff_factor': 2.0,
                'max_delay': 120.0
            },
            'file_operation': {
                'max_attempts': 2,
                'delay': 0.5,
                'backoff_factor': 1.0,
                'max_delay': 5.0
            },
            'default': {
                'max_attempts': ErrorHandlingConfig.DEFAULT_RETRY_ATTEMPTS,
                'delay': ErrorHandlingConfig.DEFAULT_RETRY_DELAY,
                'backoff_factor': 1.5,
                'max_delay': ErrorHandlingConfig.MAX_RETRY_DELAY
            }
        }
        
        return strategies.get(error_category, strategies['default'])
    
    @staticmethod
    def should_retry(error_message: str, attempt: int, max_attempts: int) -> bool:
        """判断是否应该重试"""
        if attempt >= max_attempts:
            return False
        
        # 不应该重试的错误模式
        no_retry_patterns = [
            'authentication failed',
            'access denied',
            'permission denied',
            'invalid credentials',
            'syntax error',
            'invalid query',
            'duplicate entry'
        ]
        
        error_lower = error_message.lower()
        for pattern in no_retry_patterns:
            if pattern in error_lower:
                return False
        
        return True
    
    @staticmethod
    def get_error_template(error_category: str) -> Dict[str, str]:
        """获取错误响应模板"""
        templates = {
            'database': ErrorResponseTemplates.DATABASE_ERROR,
            'network': ErrorResponseTemplates.NETWORK_ERROR,
            'authentication': ErrorResponseTemplates.AUTHENTICATION_ERROR,
            'validation': ErrorResponseTemplates.VALIDATION_ERROR,
            'business': ErrorResponseTemplates.BUSINESS_ERROR,
            'system': ErrorResponseTemplates.SYSTEM_ERROR
        }
        
        return templates.get(error_category, ErrorResponseTemplates.GENERIC_ERROR)

# 导出配置实例
error_config = ErrorHandlingConfig()
error_templates = ErrorResponseTemplates()
error_strategies = ErrorHandlingStrategies()
