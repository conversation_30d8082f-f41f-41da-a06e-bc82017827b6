"""
Excel处理服务类
负责Excel文件的读取、解析、数据清洗和格式转换
"""
import pandas as pd
import openpyxl
from datetime import datetime
import uuid
from typing import Dict, List, Tuple, Any, Optional

class ExcelProcessingService:
    def __init__(self):
        pass

    def clean_column_name(self, name: str) -> str:
        """清理列名，确保符合MySQL标识符要求"""
        if not name:
            return "unnamed_column"
        
        # 移除特殊字符，只保留字母、数字和下划线
        cleaned = ''.join(c if c.isalnum() or c == '_' else '_' for c in str(name))
        
        # 确保不以数字开头
        if cleaned[0].isdigit():
            cleaned = 'col_' + cleaned
            
        # 转换为小写
        cleaned = cleaned.lower()
        
        return cleaned

    def clean_value(self, value: Any) -> Any:
        """清理数据值，确保SQL安全"""
        if value is None:
            return None
        if isinstance(value, (int, float)):
            return value
        if isinstance(value, datetime):
            return value.strftime('%Y-%m-%d %H:%M:%S')
        return str(value).strip()

    def fill_merged_cells(self, df: pd.DataFrame) -> pd.DataFrame:
        """填充合并单元格的空值"""
        return df.fillna(method='ffill')

    def read_excel_with_openpyxl(self, file_obj) -> Tuple[bool, Dict[str, List[Dict]]]:
        """使用openpyxl读取Excel文件，支持文件流对象"""
        try:
            workbook = openpyxl.load_workbook(file_obj, data_only=True)
            sheets_data = {}
            for sheet_name in workbook.sheetnames:
                sheet = workbook[sheet_name]
                headers = []
                for col in range(1, sheet.max_column + 1):
                    cell = sheet.cell(row=1, column=col)
                    if cell.value:
                        headers.append(self.clean_column_name(str(cell.value)))
                data = []
                for row in range(2, sheet.max_row + 1):
                    row_data = {'id': str(uuid.uuid4())}
                    for col, header in enumerate(headers, 1):
                        cell = sheet.cell(row=row, column=col)
                        row_data[header] = self.clean_value(cell.value)
                    data.append(row_data)
                sheets_data[sheet_name] = data
            return True, sheets_data
        except Exception as e:
            return False, str(e)

    def process_excel_file(self, file_path: str, table_name: str) -> Tuple[bool, str]:
        """处理Excel文件并返回处理结果"""
        try:
            success, result = self.read_excel_with_openpyxl(file_path)
            if not success:
                return False, f"读取Excel文件失败: {result}"
            
            # 只处理第一个sheet的数据
            for sheet_name, data in result.items():
                return True, data
            
            return False, "Excel文件中没有数据"
        except Exception as e:
            return False, f"处理Excel文件时发生错误: {str(e)}" 