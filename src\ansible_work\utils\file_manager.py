import os
import paramiko
import tempfile
import shutil
from flask import session
import logging
import json
from datetime import datetime
import tarfile
import io

class FileManager:
    """文件管理模块，用于处理7.26服务器与7.3服务器之间的文件操作"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.temp_dir = tempfile.mkdtemp()  # 创建临时目录用于文件上传
        
    def __del__(self):
        """清理临时目录"""
        try:
            if hasattr(self, 'temp_dir') and os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
        except Exception as e:
            self.logger.error(f"清理临时目录失败: {str(e)}")
    
    def authenticate(self, password):
        """验证7.26服务器密码并建立SSH连接"""
        try:
            # 创建SSH客户端
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接到7.26服务器
            ssh.connect('************', 6233, 'root', password)
            
            # 测试连接到7.3服务器（假设已配置免密登录）
            stdin, stdout, stderr = ssh.exec_command('ssh -p 22 root@*********** "echo 连接成功"')
            result = stdout.read().decode('utf-8')
            
            if '连接成功' in result:
                # 将SSH客户端保存到会话中
                session['file_manager_authenticated'] = True
                session['file_manager_password'] = password  # 安全起见，实际生产环境应避免存储密码
                ssh.close()
                return True, "认证成功"
            else:
                ssh.close()
                return False, "无法连接到7.3服务器，请检查免密配置"
        except Exception as e:
            self.logger.error(f"认证失败: {str(e)}")
            return False, f"认证失败: {str(e)}"
    
    def is_authenticated(self):
        """检查是否已认证"""
        return session.get('file_manager_authenticated', False)
    
    def get_ssh_client(self):
        """获取SSH客户端连接"""
        if not self.is_authenticated():
            return None
        
        try:
            # 创建SSH客户端
            ssh = paramiko.SSHClient()
            ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 使用会话中的密码连接到7.26服务器
            password = session.get('file_manager_password')
            ssh.connect('************', 6233, 'root', password)
            return ssh
        except Exception as e:
            self.logger.error(f"创建SSH连接失败: {str(e)}")
            return None
    
    def list_directory(self, directory_path):
        """列出指定目录下的文件和子目录"""
        if not self.is_authenticated():
            return False, "未认证，请先登录"
        
        try:
            ssh = self.get_ssh_client()
            if not ssh:
                return False, "SSH连接失败"
            
            # 通过SSH连接到7.3服务器并列出目录内容，加上-L参数确保能识别符号链接
            command = f'ssh -p 22 root@*********** "ls -la --time-style=long-iso {directory_path}"'
            stdin, stdout, stderr = ssh.exec_command(command)
            
            output = stdout.read().decode('utf-8')
            error = stderr.read().decode('utf-8')
            
            if error and not output:
                ssh.close()
                return False, f"获取目录内容失败: {error}"
            
            # 解析ls命令输出
            lines = output.strip().split('\n')
            items = []
            
            # 记录调试信息
            self.logger.debug(f"目录内容原始输出:\n{output}")
            
            # 跳过前一行（总计行）
            for line in lines[1:]:
                parts = line.split()
                if len(parts) >= 8:
                    permissions = parts[0]
                    size = int(parts[4])
                    date = parts[5]
                    time = parts[6]
                    name = ' '.join(parts[7:])
                    
                    # 判断是文件还是目录或链接
                    is_dir = permissions.startswith('d')
                    is_link = permissions.startswith('l')
                    
                    # 如果是符号链接，检查它指向的是否是目录
                    if is_link:
                        # 提取链接指向
                        if '->' in name:
                            name_parts = name.split(' -> ')
                            name = name_parts[0]
                            link_target = name_parts[1]
                            
                            # 检查链接目标是否是目录
                            check_cmd = f'ssh -p 22 root@*********** "if [ -d \'{os.path.join(directory_path, link_target)}\' ] || [ -d \'{link_target}\' ]; then echo \'IS_DIR\'; fi"'
                            _, stdout, _ = ssh.exec_command(check_cmd)
                            check_result = stdout.read().decode('utf-8').strip()
                            
                            # 如果链接指向目录，也将其标记为目录
                            if check_result == 'IS_DIR':
                                is_dir = True
                    
                    # 构建完整路径用于后续操作
                    full_path = os.path.join(directory_path, name).replace('\\', '/')
                    if full_path.endswith('/') and len(full_path) > 1:
                        full_path = full_path[:-1]  # 移除末尾的斜杠
                    
                    items.append({
                        'name': name,
                        'is_directory': is_dir,
                        'size': size,
                        'modified_time': f"{date} {time}",
                        'permissions': permissions,
                        'path': full_path
                    })
                    
                    # 记录调试信息
                    self.logger.debug(f"解析项目: {name}, 类型: {'目录' if is_dir else '文件'}, 路径: {full_path}")
            
            ssh.close()
            return True, items
        except Exception as e:
            self.logger.error(f"列出目录内容失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False, f"列出目录内容失败: {str(e)}"
    
    def upload_file(self, file, destination_path, overwrite=False):
        """上传文件到7.3服务器
        
        Args:
            file: 要上传的文件对象
            destination_path: 目标路径
            overwrite: 是否覆盖已存在的文件，默认False
        
        Returns:
            (success, message): 成功/失败标志和消息
        """
        if not self.is_authenticated():
            return False, "未认证，请先登录"
        
        if file.filename == '':
            return False, "没有选择文件"
        
        try:
            # 保存文件到临时目录
            temp_file_path = os.path.join(self.temp_dir, file.filename)
            file.save(temp_file_path)
            
            # 获取SSH客户端
            ssh = self.get_ssh_client()
            if not ssh:
                return False, "SSH连接失败"
            
            # 创建SFTP客户端
            sftp = ssh.open_sftp()
            
            # 上传文件到7.26服务器临时目录
            remote_temp_path = f'/tmp/{file.filename}'
            sftp.put(temp_file_path, remote_temp_path)
            
            # 确保目标目录存在
            # 规范化路径，移除多余的斜杠
            destination_path = os.path.normpath(destination_path).replace('\\', '/')
            ensure_dir_command = f'ssh -p 22 root@*********** "mkdir -p \'{destination_path}\'"'
            stdin, stdout, stderr = ssh.exec_command(ensure_dir_command)
            exit_status = stdout.channel.recv_exit_status()
            
            # 检查目标服务器上是否已存在同名文件
            target_path = os.path.join(destination_path, file.filename).replace('\\', '/')
            check_file_command = f'ssh -p 22 root@*********** "if [ -f \'{target_path}\' ]; then echo \'FILE_EXISTS\'; else echo \'FILE_NOT_EXISTS\'; fi"'
            stdin, stdout, stderr = ssh.exec_command(check_file_command)
            check_result = stdout.read().decode('utf-8').strip()
            
            if "FILE_EXISTS" in check_result and not overwrite:
                sftp.close()
                ssh.close()
                return False, f"上传失败：目标位置已存在同名文件 '{file.filename}'，请重命名后再上传或选择覆盖选项"
            
            # 从7.26服务器传输到7.3服务器
            command = f'scp -P 22 {remote_temp_path} root@***********:"{target_path}"'
            stdin, stdout, stderr = ssh.exec_command(command)
            
            # 等待命令完成
            exit_status = stdout.channel.recv_exit_status()
            error = stderr.read().decode('utf-8')
            
            # 忽略SSH欢迎信息，只检查实际错误
            if error and "You have logged onto a secured server" not in error and exit_status != 0:
                sftp.close()
                ssh.close()
                return False, f"上传文件失败: {error}"
            
            # 验证文件是否成功传输到7.3服务器
            # 使用单引号包裹路径，避免特殊字符问题
            verify_command = f'ssh -p 22 root@*********** "ls -la \'{target_path}\' 2>/dev/null || echo \'FILE_NOT_FOUND\'"'
            stdin, stdout, stderr = ssh.exec_command(verify_command)
            verify_output = stdout.read().decode('utf-8')
            
            if "FILE_NOT_FOUND" in verify_output or not verify_output:
                # 尝试直接使用SSH命令复制文件
                direct_copy_command = f'ssh -p 22 root@*********** "cat > \'{target_path}\'" < {remote_temp_path}'
                stdin, stdout, stderr = ssh.exec_command(direct_copy_command)
                exit_status = stdout.channel.recv_exit_status()
                
                # 再次验证文件
                verify_command = f'ssh -p 22 root@*********** "ls -la \'{target_path}\' 2>/dev/null || echo \'FILE_NOT_FOUND\'"'
                stdin, stdout, stderr = ssh.exec_command(verify_command)
                verify_output = stdout.read().decode('utf-8')
                
                if "FILE_NOT_FOUND" in verify_output or not verify_output:
                    sftp.close()
                    ssh.close()
                    return False, "文件上传失败：无法在目标服务器上验证文件"
            
            # 清理临时文件
            ssh.exec_command(f'rm {remote_temp_path}')
            
            sftp.close()
            ssh.close()
            
            # 根据是否覆盖返回不同的成功消息
            if "FILE_EXISTS" in check_result and overwrite:
                return True, f"文件上传成功（覆盖了已有文件）"
            else:
                return True, "文件上传成功"
        except Exception as e:
            self.logger.error(f"上传文件失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False, f"上传文件失败: {str(e)}"
    
    def download_file(self, file_path):
        """从7.3服务器下载文件"""
        if not self.is_authenticated():
            return False, "未认证，请先登录", None
        
        try:
            # 获取SSH客户端
            ssh = self.get_ssh_client()
            if not ssh:
                return False, "SSH连接失败", None
            
            # 从7.3服务器复制到7.26服务器
            filename = os.path.basename(file_path)
            remote_temp_path = f'/tmp/{filename}'
            
            # 使用ssh命令而不是scp，避免可能的端口问题
            # 注意：重定向符号(>)需要在远程服务器上执行，所以我们将整个命令包装在一个引号中
            command = f'ssh -p 22 root@*********** "cat \'{file_path}\'" | cat > {remote_temp_path}'
            stdin, stdout, stderr = ssh.exec_command(command)
            
            # 等待命令完成
            exit_status = stdout.channel.recv_exit_status()
            error = stderr.read().decode('utf-8')
            
            # 检查文件是否成功创建
            _, stdout, _ = ssh.exec_command(f'ls -la {remote_temp_path}')
            file_check = stdout.read().decode('utf-8')
            
            if exit_status != 0 or ('No such file' in error) or not file_check:
                ssh.close()
                return False, f"文件不存在或无法访问: {file_path} (错误: {error})", None
            
            # 创建SFTP客户端
            sftp = ssh.open_sftp()
            
            # 创建内存文件对象
            memory_file = io.BytesIO()
            
            # 从7.26服务器下载文件到内存
            sftp.getfo(remote_temp_path, memory_file)
            
            # 清理临时文件
            ssh.exec_command(f'rm {remote_temp_path}')
            
            # 重置文件指针到开始位置
            memory_file.seek(0)
            
            sftp.close()
            ssh.close()
            return True, "文件下载成功", memory_file
        except Exception as e:
            self.logger.error(f"下载文件失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False, f"下载文件失败: {str(e)}", None
    
    def batch_download(self, file_paths):
        """批量下载文件（打包为tar.gz）"""
        if not self.is_authenticated():
            return False, "未认证，请先登录", None
        
        try:
            # 获取SSH客户端
            ssh = self.get_ssh_client()
            if not ssh:
                return False, "SSH连接失败", None
            
            # 生成唯一的临时文件名
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            tar_filename = f'batch_download_{timestamp}.tar.gz'
            remote_tar_path = f'/tmp/{tar_filename}'
            
            # 构建tar命令，从7.3服务器打包文件
            file_paths_str = ' '.join([f'"{path}"' for path in file_paths])
            tar_command = f'ssh -p 22 root@*********** "cd / && tar -czf - {file_paths_str}" | cat > {remote_tar_path}'
            stdin, stdout, stderr = ssh.exec_command(tar_command)
            
            # 等待命令完成
            exit_status = stdout.channel.recv_exit_status()
            error = stderr.read().decode('utf-8')
            
            # 检查文件是否成功创建
            _, stdout, _ = ssh.exec_command(f'ls -la {remote_tar_path}')
            file_check = stdout.read().decode('utf-8')
            
            if exit_status != 0 or ('No such file' in error) or not file_check:
                ssh.close()
                return False, f"部分文件不存在或无法访问: {error}", None
            
            # 创建SFTP客户端
            sftp = ssh.open_sftp()
            
            # 创建内存文件对象
            memory_file = io.BytesIO()
            
            # 从7.26服务器下载打包文件到内存
            sftp.getfo(remote_tar_path, memory_file)
            
            # 清理临时文件
            ssh.exec_command(f'rm {remote_tar_path}')
            
            # 重置文件指针到开始位置
            memory_file.seek(0)
            
            sftp.close()
            ssh.close()
            return True, "批量下载成功", memory_file
        except Exception as e:
            self.logger.error(f"批量下载文件失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False, f"批量下载文件失败: {str(e)}", None
    
    def check_file_exists(self, file_path):
        """检查指定文件是否存在"""
        if not self.is_authenticated():
            return False
        
        try:
            ssh = self.get_ssh_client()
            if not ssh:
                return False
            
            # 使用引号包裹路径，避免特殊字符问题
            command = f'ssh -p 22 root@*********** "[ -f \'{file_path}\' ] && echo \'EXISTS\' || echo \'NOT_EXISTS\'"'
            stdin, stdout, stderr = ssh.exec_command(command)
            
            output = stdout.read().decode('utf-8').strip()
            ssh.close()
            
            return output == 'EXISTS'
        except Exception as e:
            self.logger.error(f"检查文件存在失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return False

    def get_file_info(self, file_path):
        """获取文件的元数据信息"""
        if not self.is_authenticated():
            return None
        
        try:
            ssh = self.get_ssh_client()
            if not ssh:
                return None
            
            # 执行stat命令获取文件信息
            command = f'ssh -p 22 root@*********** "stat -c \'%s,%Y,%A,%F\' \'{file_path}\' 2>/dev/null || echo \'FILE_NOT_FOUND\'"'
            stdin, stdout, stderr = ssh.exec_command(command)
            
            output = stdout.read().decode('utf-8').strip()
            ssh.close()
            
            if output == 'FILE_NOT_FOUND':
                return None
            
            # 解析输出 (大小,修改时间,权限,文件类型)
            parts = output.split(',')
            if len(parts) >= 4:
                size = int(parts[0])
                mod_time = datetime.fromtimestamp(int(parts[1]))
                permissions = parts[2]
                file_type = parts[3]
                
                return {
                    'path': file_path,
                    'name': os.path.basename(file_path),
                    'size': size,
                    'modified_time': mod_time.strftime('%Y-%m-%d %H:%M:%S'),
                    'permissions': permissions,
                    'is_directory': 'directory' in file_type.lower()
                }
            
            return None
        except Exception as e:
            self.logger.error(f"获取文件信息失败: {str(e)}")
            if 'ssh' in locals() and ssh:
                ssh.close()
            return None
