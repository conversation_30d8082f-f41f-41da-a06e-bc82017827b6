"""
性能监控API路由
提供系统性能监控、缓存管理、查询分析等API接口
"""

from flask import Blueprint, jsonify, request, render_template
from datetime import datetime, timedelta
import logging

from .performance_monitor import performance_monitor
from .cache_manager import cache_manager
from .query_optimizer import query_optimizer
from .db_manager import DatabaseManager

logger = logging.getLogger(__name__)

# 创建性能监控蓝图
performance_bp = Blueprint('performance', __name__, url_prefix='/api/performance')

# 创建一个简单的性能监控页面蓝图（不带API前缀）
performance_page_bp = Blueprint('performance_page', __name__)

@performance_page_bp.route('/performance')
def performance_page():
    """性能监控页面（简单访问路径）"""
    return render_template('performance/dashboard.html')

@performance_bp.route('/dashboard')
def performance_dashboard():
    """性能监控仪表盘页面"""
    return render_template('performance/dashboard.html')

@performance_bp.route('/system/metrics')
def get_system_metrics():
    """获取系统性能指标"""
    try:
        metrics = performance_monitor.get_system_metrics()
        return jsonify({
            'success': True,
            'data': metrics
        })
    except Exception as e:
        logger.error(f"获取系统指标失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取系统指标失败: {str(e)}'
        }), 500

@performance_bp.route('/queries/slow')
def get_slow_queries():
    """获取慢查询列表"""
    try:
        limit = request.args.get('limit', 20, type=int)
        slow_queries = performance_monitor.get_slow_queries(limit=limit)
        
        return jsonify({
            'success': True,
            'data': slow_queries,
            'total': len(slow_queries)
        })
    except Exception as e:
        logger.error(f"获取慢查询失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取慢查询失败: {str(e)}'
        }), 500

@performance_bp.route('/functions/performance')
def get_function_performance():
    """获取函数性能统计"""
    try:
        limit = request.args.get('limit', 30, type=int)
        function_stats = performance_monitor.get_function_performance(limit=limit)
        
        return jsonify({
            'success': True,
            'data': function_stats,
            'total': len(function_stats)
        })
    except Exception as e:
        logger.error(f"获取函数性能统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取函数性能统计失败: {str(e)}'
        }), 500

@performance_bp.route('/metrics/recent')
def get_recent_metrics():
    """获取最近的性能指标"""
    try:
        minutes = request.args.get('minutes', 30, type=int)
        metrics = performance_monitor.get_recent_metrics(minutes=minutes)
        
        return jsonify({
            'success': True,
            'data': metrics,
            'total': len(metrics),
            'time_range': f'最近 {minutes} 分钟'
        })
    except Exception as e:
        logger.error(f"获取最近指标失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取最近指标失败: {str(e)}'
        }), 500

@performance_bp.route('/report')
def get_performance_report():
    """获取性能报告"""
    try:
        report = performance_monitor.generate_performance_report()
        return jsonify({
            'success': True,
            'data': report
        })
    except Exception as e:
        logger.error(f"生成性能报告失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'生成性能报告失败: {str(e)}'
        }), 500

@performance_bp.route('/cache/stats')
def get_cache_stats():
    """获取缓存统计信息"""
    try:
        stats = cache_manager.get_all_stats()
        return jsonify({
            'success': True,
            'data': stats
        })
    except Exception as e:
        logger.error(f"获取缓存统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取缓存统计失败: {str(e)}'
        }), 500

@performance_bp.route('/cache/clear', methods=['POST'])
def clear_cache():
    """清空缓存"""
    try:
        cache_name = request.json.get('cache_name') if request.json else None
        
        if cache_name:
            cache = cache_manager.get_cache(cache_name)
            if cache:
                cache.clear()
                message = f'缓存 {cache_name} 已清空'
            else:
                return jsonify({
                    'success': False,
                    'message': f'缓存 {cache_name} 不存在'
                }), 404
        else:
            cache_manager.clear_all()
            message = '所有缓存已清空'
        
        return jsonify({
            'success': True,
            'message': message
        })
    except Exception as e:
        logger.error(f"清空缓存失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'清空缓存失败: {str(e)}'
        }), 500

@performance_bp.route('/query/analyze', methods=['POST'])
def analyze_query():
    """分析查询语句"""
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({
                'success': False,
                'message': '请提供要分析的查询语句'
            }), 400
        
        query = data['query']
        analysis = query_optimizer.analyze_query(query)
        
        return jsonify({
            'success': True,
            'data': {
                'query_type': analysis.query_type,
                'tables': analysis.tables,
                'complexity_score': analysis.complexity_score,
                'optimization_suggestions': analysis.optimization_suggestions,
                'index_suggestions': analysis.index_suggestions
            }
        })
    except Exception as e:
        logger.error(f"查询分析失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'查询分析失败: {str(e)}'
        }), 500

@performance_bp.route('/database/connections')
def get_database_connections():
    """获取数据库连接状态"""
    try:
        connections_info = []
        
        # 获取所有数据库引擎的状态
        for db_name in ['main', 'mysql_audit', 'ansible', 'catalog']:
            try:
                engine = DatabaseManager.get_engine(db_name)
                if engine:
                    pool = engine.pool
                    connections_info.append({
                        'database': db_name,
                        'status': 'connected',
                        'pool_size': pool.size(),
                        'checked_in': pool.checkedin(),
                        'checked_out': pool.checkedout(),
                        'overflow': pool.overflow(),
                        'invalid': pool.invalid()
                    })
                else:
                    connections_info.append({
                        'database': db_name,
                        'status': 'disconnected',
                        'pool_size': 0,
                        'checked_in': 0,
                        'checked_out': 0,
                        'overflow': 0,
                        'invalid': 0
                    })
            except Exception as e:
                connections_info.append({
                    'database': db_name,
                    'status': 'error',
                    'error': str(e)
                })
        
        return jsonify({
            'success': True,
            'data': connections_info
        })
    except Exception as e:
        logger.error(f"获取数据库连接状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取数据库连接状态失败: {str(e)}'
        }), 500

@performance_bp.route('/optimization/suggestions')
def get_optimization_suggestions():
    """获取系统优化建议"""
    try:
        suggestions = []
        
        # 获取性能报告
        report = performance_monitor.generate_performance_report()
        suggestions.extend(report.get('recommendations', []))
        
        # 获取缓存统计
        cache_stats = cache_manager.get_all_stats()
        for cache_name, stats in cache_stats.items():
            if stats['hit_rate'] < 50:
                suggestions.append(f"缓存 {cache_name} 命中率较低 ({stats['hit_rate']}%)，建议检查缓存策略")
            
            if stats['size'] >= stats['max_size'] * 0.9:
                suggestions.append(f"缓存 {cache_name} 接近容量上限，建议增加缓存大小或调整TTL")
        
        # 检查慢查询
        slow_queries = performance_monitor.get_slow_queries(limit=5)
        if len(slow_queries) > 0:
            suggestions.append(f"检测到 {len(slow_queries)} 个慢查询，建议优化数据库查询")
        
        return jsonify({
            'success': True,
            'data': {
                'suggestions': suggestions,
                'total': len(suggestions),
                'generated_at': datetime.now().isoformat()
            }
        })
    except Exception as e:
        logger.error(f"获取优化建议失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'获取优化建议失败: {str(e)}'
        }), 500

@performance_bp.route('/health')
def health_check():
    """系统健康检查"""
    try:
        health_status = {
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'checks': {}
        }
        
        # 检查数据库连接
        try:
            engine = DatabaseManager.get_main_db()
            if engine:
                with engine.connect() as conn:
                    conn.execute("SELECT 1")
                health_status['checks']['database'] = 'healthy'
            else:
                health_status['checks']['database'] = 'unhealthy'
                health_status['status'] = 'degraded'
        except Exception as e:
            health_status['checks']['database'] = f'error: {str(e)}'
            health_status['status'] = 'unhealthy'
        
        # 检查系统资源
        system_metrics = performance_monitor.get_system_metrics()
        if system_metrics:
            if system_metrics.get('memory_usage', 0) > 90:
                health_status['checks']['memory'] = 'critical'
                health_status['status'] = 'unhealthy'
            elif system_metrics.get('memory_usage', 0) > 80:
                health_status['checks']['memory'] = 'warning'
                if health_status['status'] == 'healthy':
                    health_status['status'] = 'degraded'
            else:
                health_status['checks']['memory'] = 'healthy'
            
            if system_metrics.get('cpu_usage', 0) > 90:
                health_status['checks']['cpu'] = 'critical'
                health_status['status'] = 'unhealthy'
            elif system_metrics.get('cpu_usage', 0) > 80:
                health_status['checks']['cpu'] = 'warning'
                if health_status['status'] == 'healthy':
                    health_status['status'] = 'degraded'
            else:
                health_status['checks']['cpu'] = 'healthy'
        
        # 检查缓存状态
        cache_stats = cache_manager.get_all_stats()
        cache_healthy = all(stats['hit_rate'] > 30 for stats in cache_stats.values())
        health_status['checks']['cache'] = 'healthy' if cache_healthy else 'warning'
        
        return jsonify({
            'success': True,
            'data': health_status
        })
    except Exception as e:
        logger.error(f"健康检查失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'健康检查失败: {str(e)}'
        }), 500
