/**
 * 文件传输管理器
 * 用于管理后台文件上传和下载任务，显示进度和状态
 */

// 传输任务管理器
class FileTransferManager {
    constructor() {
        this.tasks = [];
        this.isInitialized = false;
        this.containerId = 'fileTransferManager';
        this.notificationsEnabled = false;
        this.checkNotificationPermission();
        
        // 绑定方法
        this.initialize = this.initialize.bind(this);
        this.addDownloadTask = this.addDownloadTask.bind(this);
        this.addUploadTask = this.addUploadTask.bind(this);
        this.updateTaskProgress = this.updateTaskProgress.bind(this);
        this.removeTask = this.removeTask.bind(this);
        this.togglePanel = this.togglePanel.bind(this);
        this.renderTasks = this.renderTasks.bind(this);
    }
    
    // 初始化管理器
    initialize() {
        if (this.isInitialized) return;
        
        // 创建传输管理器容器
        this.createContainer();
        
        // 设置WebSocket连接以获取实时进度更新
        this.setupWebSocket();
        
        // 注册事件处理程序
        this.registerEventHandlers();
        
        this.isInitialized = true;
        console.log('文件传输管理器已初始化');
    }
    
    // 创建容器
    createContainer() {
        // 检查容器是否已存在
        if (document.getElementById(this.containerId)) {
            return;
        }
        
        // 创建主容器
        const container = document.createElement('div');
        container.id = this.containerId;
        container.className = 'file-transfer-manager';
        container.innerHTML = `
            <div class="transfer-header">
                <span class="transfer-title">传输管理器</span>
                <span class="transfer-count">0</span>
                <button class="transfer-toggle-btn">
                    <i class="fas fa-chevron-up"></i>
                </button>
            </div>
            <div class="transfer-body">
                <div class="transfer-list"></div>
            </div>
        `;
        
        // 添加样式
        this.addStyles();
        
        // 添加到文档
        document.body.appendChild(container);
        
        // 绑定切换面板事件
        const toggleBtn = container.querySelector('.transfer-toggle-btn');
        toggleBtn.addEventListener('click', this.togglePanel);
    }
    
    // 添加样式
    addStyles() {
        // 检查样式是否已存在
        if (document.getElementById('fileTransferManagerStyles')) {
            return;
        }
        
        const style = document.createElement('style');
        style.id = 'fileTransferManagerStyles';
        style.textContent = `
            .file-transfer-manager {
                position: fixed;
                bottom: 0;
                right: 20px;
                width: 350px;
                background: #fff;
                border-radius: 8px 8px 0 0;
                box-shadow: 0 0 10px rgba(0,0,0,0.2);
                z-index: 9999;
                font-family: Arial, sans-serif;
                transition: height 0.3s ease;
                max-height: 400px;
                display: flex;
                flex-direction: column;
            }
            
            .transfer-header {
                padding: 10px 15px;
                background: #f8f9fa;
                border-bottom: 1px solid #eee;
                display: flex;
                align-items: center;
                border-radius: 8px 8px 0 0;
                cursor: pointer;
            }
            
            .transfer-title {
                font-weight: bold;
                flex-grow: 1;
            }
            
            .transfer-count {
                background: #007bff;
                color: white;
                border-radius: 50%;
                width: 20px;
                height: 20px;
                display: inline-flex;
                align-items: center;
                justify-content: center;
                font-size: 12px;
                margin-right: 10px;
            }
            
            .transfer-toggle-btn {
                background: none;
                border: none;
                cursor: pointer;
                color: #6c757d;
            }
            
            .transfer-body {
                padding: 10px;
                overflow-y: auto;
                flex-grow: 1;
                max-height: 350px;
            }
            
            .transfer-list {
                display: flex;
                flex-direction: column;
                gap: 10px;
            }
            
            .transfer-item {
                border: 1px solid #eee;
                padding: 10px;
                border-radius: 4px;
                position: relative;
            }
            
            .transfer-item-header {
                display: flex;
                justify-content: space-between;
                margin-bottom: 5px;
            }
            
            .transfer-filename {
                font-weight: bold;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                max-width: 200px;
            }
            
            .transfer-type {
                font-size: 12px;
                padding: 2px 5px;
                border-radius: 3px;
                color: white;
            }
            
            .transfer-type.download {
                background: #28a745;
            }
            
            .transfer-type.upload {
                background: #17a2b8;
            }
            
            .transfer-progress-bar {
                height: 4px;
                background: #e9ecef;
                margin: 5px 0;
                border-radius: 4px;
            }
            
            .transfer-progress-value {
                height: 100%;
                background: #007bff;
                border-radius: 4px;
                transition: width 0.3s ease;
            }
            
            .transfer-details {
                display: flex;
                justify-content: space-between;
                font-size: 12px;
                color: #6c757d;
            }
            
            .transfer-controls {
                margin-top: 5px;
                display: flex;
                gap: 5px;
            }
            
            .transfer-btn {
                border: none;
                background: none;
                cursor: pointer;
                font-size: 12px;
                padding: 2px 5px;
                border-radius: 3px;
            }
            
            .transfer-btn:hover {
                background: #f8f9fa;
            }
            
            .transfer-btn.pause {
                color: #fd7e14;
            }
            
            .transfer-btn.resume {
                color: #28a745;
            }
            
            .transfer-btn.cancel {
                color: #dc3545;
            }
            
            .transfer-btn.retry {
                color: #007bff;
            }
            
            .transfer-collapsed .transfer-body {
                display: none;
            }
            
            .transfer-collapsed .transfer-toggle-btn i {
                transform: rotate(180deg);
            }
            
            @keyframes progress-animation {
                0% { background-position: 0% 0%; }
                100% { background-position: 100% 0%; }
            }
            
            .transfer-indeterminate .transfer-progress-value {
                width: 100% !important;
                background: linear-gradient(to right, #007bff, #28a745, #007bff);
                background-size: 200% 100%;
                animation: progress-animation 2s linear infinite;
            }
        `;
        
        document.head.appendChild(style);
    }
    
    // 设置WebSocket连接
    setupWebSocket() {
        // 这里可以实现WebSocket连接来获取实时进度更新
        // 简化版本中使用轮询
        this.progressInterval = setInterval(() => {
            this.tasks.forEach(task => {
                if (task.status === 'in_progress') {
                    this.pollTaskProgress(task.id);
                }
            });
        }, 1000);
    }
    
    // 轮询任务进度
    pollTaskProgress(taskId) {
        fetch(`/ansible/api/transfer/status/${taskId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    this.updateTaskProgress(taskId, data.progress, data.speed, data.status);
                }
            })
            .catch(error => {
                console.error('获取任务进度失败', error);
            });
    }
    
    // 注册事件处理程序
    registerEventHandlers() {
        // 监听文件下载点击事件
        document.addEventListener('click', event => {
            const downloadLink = event.target.closest('[data-file-download]');
            if (downloadLink) {
                event.preventDefault();
                
                const filePath = downloadLink.getAttribute('data-file-path');
                const fileName = downloadLink.getAttribute('data-file-name') || filePath.split('/').pop();
                
                this.initiateBackgroundDownload(filePath, fileName);
            }
        });
    }
    
    // 检查通知权限
    checkNotificationPermission() {
        if ('Notification' in window) {
            if (Notification.permission === 'granted') {
                this.notificationsEnabled = true;
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission().then(permission => {
                    this.notificationsEnabled = (permission === 'granted');
                });
            }
        }
    }
    
    // 显示通知
    showNotification(title, message) {
        if (this.notificationsEnabled) {
            const notification = new Notification(title, {
                body: message,
                icon: '/static/img/logo.png'
            });
            
            notification.onclick = function() {
                window.focus();
                this.close();
            };
        } else {
            // 使用替代通知（如页面内提示）
            this.showToast(title, message);
        }
    }
    
    // 显示页面内提示
    showToast(title, message) {
        const toast = document.createElement('div');
        toast.className = 'transfer-toast';
        toast.innerHTML = `
            <div class="transfer-toast-header">
                <strong>${title}</strong>
                <button type="button" class="toast-close">&times;</button>
            </div>
            <div class="transfer-toast-body">${message}</div>
        `;
        
        // 添加样式
        if (!document.getElementById('transferToastStyles')) {
            const style = document.createElement('style');
            style.id = 'transferToastStyles';
            style.textContent = `
                .transfer-toast {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    width: 300px;
                    background: white;
                    border-radius: 4px;
                    box-shadow: 0 0 10px rgba(0,0,0,0.2);
                    z-index: 10000;
                    overflow: hidden;
                    animation: toast-in 0.3s ease, toast-out 0.3s ease 4.7s forwards;
                }
                
                .transfer-toast-header {
                    display: flex;
                    justify-content: space-between;
                    padding: 10px 15px;
                    background: #f8f9fa;
                    border-bottom: 1px solid #eee;
                }
                
                .transfer-toast-body {
                    padding: 10px 15px;
                }
                
                .toast-close {
                    background: none;
                    border: none;
                    font-size: 20px;
                    cursor: pointer;
                    line-height: 1;
                }
                
                @keyframes toast-in {
                    from { transform: translateX(100%); }
                    to { transform: translateX(0); }
                }
                
                @keyframes toast-out {
                    from { transform: translateX(0); }
                    to { transform: translateX(100%); }
                }
            `;
            document.head.appendChild(style);
        }
        
        document.body.appendChild(toast);
        
        // 添加关闭按钮事件
        const closeBtn = toast.querySelector('.toast-close');
        closeBtn.addEventListener('click', () => {
            document.body.removeChild(toast);
        });
        
        // 自动关闭
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 5000);
    }
    
    // 后台下载文件
    initiateBackgroundDownload(filePath, fileName) {
        // 创建下载任务
        fetch('/ansible/api/transfer/download', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                file_path: filePath,
                file_name: fileName
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.addDownloadTask(data.task_id, fileName, data.file_size);
                this.showToast('下载已开始', `文件 ${fileName} 开始下载`);
            } else {
                this.showToast('下载失败', data.message || '无法开始下载');
            }
        })
        .catch(error => {
            console.error('创建下载任务失败', error);
            this.showToast('下载失败', '无法创建下载任务');
        });
    }
    
    // 添加下载任务
    addDownloadTask(taskId, fileName, fileSize) {
        const task = {
            id: taskId,
            fileName: fileName,
            fileSize: fileSize,
            type: 'download',
            progress: 0,
            speed: '0 KB/s',
            status: 'in_progress',
            createdAt: new Date()
        };
        
        this.tasks.push(task);
        this.updateTaskCount();
        this.renderTasks();
        
        // 开始轮询任务进度
        this.pollTaskProgress(taskId);
    }
    
    // 添加上传任务
    addUploadTask(taskId, fileName, fileSize) {
        const task = {
            id: taskId,
            fileName: fileName,
            fileSize: fileSize,
            type: 'upload',
            progress: 0,
            speed: '0 KB/s',
            status: 'in_progress',
            createdAt: new Date()
        };
        
        this.tasks.push(task);
        this.updateTaskCount();
        this.renderTasks();
        
        // 开始轮询任务进度
        this.pollTaskProgress(taskId);
    }
    
    // 更新任务进度
    updateTaskProgress(taskId, progress, speed, status) {
        const taskIndex = this.tasks.findIndex(task => task.id === taskId);
        if (taskIndex === -1) return;
        
        const task = this.tasks[taskIndex];
        const previousStatus = task.status;
        
        task.progress = progress;
        task.speed = speed;
        task.status = status;
        
        this.renderTasks();
        
        // 如果任务刚刚完成，显示通知
        if (previousStatus === 'in_progress' && status === 'completed') {
            const actionType = task.type === 'download' ? '下载' : '上传';
            this.showNotification(
                `${actionType}完成`,
                `文件 ${task.fileName} 已成功${actionType}`
            );
            
            // 如果是下载，触发实际下载
            if (task.type === 'download') {
                this.triggerFileDownload(taskId, task.fileName);
            }
        } else if (previousStatus === 'in_progress' && status === 'failed') {
            const actionType = task.type === 'download' ? '下载' : '上传';
            this.showNotification(
                `${actionType}失败`,
                `文件 ${task.fileName} ${actionType}失败`
            );
        }
    }
    
    // 触发文件下载
    triggerFileDownload(taskId, fileName) {
        const downloadUrl = `/ansible/api/transfer/download/${taskId}`;
        
        const link = document.createElement('a');
        link.href = downloadUrl;
        link.download = fileName;
        link.style.display = 'none';
        
        document.body.appendChild(link);
        link.click();
        
        setTimeout(() => {
            document.body.removeChild(link);
        }, 100);
    }
    
    // 暂停任务
    pauseTask(taskId) {
        fetch(`/ansible/api/transfer/pause/${taskId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const taskIndex = this.tasks.findIndex(task => task.id === taskId);
                if (taskIndex !== -1) {
                    this.tasks[taskIndex].status = 'paused';
                    this.renderTasks();
                }
            }
        })
        .catch(error => {
            console.error('暂停任务失败', error);
        });
    }
    
    // 继续任务
    resumeTask(taskId) {
        fetch(`/ansible/api/transfer/resume/${taskId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const taskIndex = this.tasks.findIndex(task => task.id === taskId);
                if (taskIndex !== -1) {
                    this.tasks[taskIndex].status = 'in_progress';
                    this.renderTasks();
                }
            }
        })
        .catch(error => {
            console.error('继续任务失败', error);
        });
    }
    
    // 取消任务
    cancelTask(taskId) {
        fetch(`/ansible/api/transfer/cancel/${taskId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                this.removeTask(taskId);
            }
        })
        .catch(error => {
            console.error('取消任务失败', error);
        });
    }
    
    // 重试任务
    retryTask(taskId) {
        fetch(`/ansible/api/transfer/retry/${taskId}`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const taskIndex = this.tasks.findIndex(task => task.id === taskId);
                if (taskIndex !== -1) {
                    this.tasks[taskIndex].status = 'in_progress';
                    this.tasks[taskIndex].progress = 0;
                    this.renderTasks();
                }
            }
        })
        .catch(error => {
            console.error('重试任务失败', error);
        });
    }
    
    // 移除任务
    removeTask(taskId) {
        const taskIndex = this.tasks.findIndex(task => task.id === taskId);
        if (taskIndex !== -1) {
            this.tasks.splice(taskIndex, 1);
            this.updateTaskCount();
            this.renderTasks();
        }
    }
    
    // 更新任务数量
    updateTaskCount() {
        const countElement = document.querySelector(`#${this.containerId} .transfer-count`);
        if (countElement) {
            countElement.textContent = this.tasks.length;
        }
    }
    
    // 切换面板显示/隐藏
    togglePanel() {
        const container = document.getElementById(this.containerId);
        container.classList.toggle('transfer-collapsed');
        
        const icon = container.querySelector('.transfer-toggle-btn i');
        icon.classList.toggle('fa-chevron-up');
        icon.classList.toggle('fa-chevron-down');
    }
    
    // 渲染任务列表
    renderTasks() {
        const listElement = document.querySelector(`#${this.containerId} .transfer-list`);
        if (!listElement) return;
        
        listElement.innerHTML = '';
        
        if (this.tasks.length === 0) {
            listElement.innerHTML = '<div class="no-tasks">无传输任务</div>';
            return;
        }
        
        // 按创建时间降序排序
        const sortedTasks = [...this.tasks].sort((a, b) => b.createdAt - a.createdAt);
        
        sortedTasks.forEach(task => {
            const taskElement = document.createElement('div');
            taskElement.className = `transfer-item ${task.status === 'indeterminate' ? 'transfer-indeterminate' : ''}`;
            taskElement.dataset.taskId = task.id;
            
            // 格式化文件大小
            const formattedSize = this.formatFileSize(task.fileSize);
            const progressText = task.status === 'completed' ? 
                formattedSize : 
                `${this.formatFileSize(task.fileSize * task.progress / 100)} / ${formattedSize}`;
            
            taskElement.innerHTML = `
                <div class="transfer-item-header">
                    <div class="transfer-filename" title="${task.fileName}">${task.fileName}</div>
                    <div class="transfer-type ${task.type}">${task.type === 'download' ? '下载' : '上传'}</div>
                </div>
                <div class="transfer-progress-bar">
                    <div class="transfer-progress-value" style="width: ${task.progress}%"></div>
                </div>
                <div class="transfer-details">
                    <div class="transfer-size">${progressText}</div>
                    <div class="transfer-status">${this.getStatusText(task.status)}</div>
                </div>
                <div class="transfer-speed">${task.status === 'in_progress' ? task.speed : ''}</div>
                <div class="transfer-controls">
                    ${this.getTaskControls(task)}
                </div>
            `;
            
            listElement.appendChild(taskElement);
            
            // 绑定任务控制按钮事件
            this.bindTaskControlEvents(taskElement, task);
        });
    }
    
    // 获取任务状态文本
    getStatusText(status) {
        switch (status) {
            case 'waiting': return '等待中';
            case 'in_progress': return '进行中';
            case 'paused': return '已暂停';
            case 'completed': return '已完成';
            case 'failed': return '失败';
            default: return '未知状态';
        }
    }
    
    // 获取任务控制按钮HTML
    getTaskControls(task) {
        switch (task.status) {
            case 'in_progress':
                return `
                    <button class="transfer-btn pause" data-action="pause">
                        <i class="fas fa-pause"></i> 暂停
                    </button>
                    <button class="transfer-btn cancel" data-action="cancel">
                        <i class="fas fa-times"></i> 取消
                    </button>
                `;
            case 'paused':
                return `
                    <button class="transfer-btn resume" data-action="resume">
                        <i class="fas fa-play"></i> 继续
                    </button>
                    <button class="transfer-btn cancel" data-action="cancel">
                        <i class="fas fa-times"></i> 取消
                    </button>
                `;
            case 'completed':
                return `
                    <button class="transfer-btn cancel" data-action="remove">
                        <i class="fas fa-times"></i> 移除
                    </button>
                `;
            case 'failed':
                return `
                    <button class="transfer-btn retry" data-action="retry">
                        <i class="fas fa-redo"></i> 重试
                    </button>
                    <button class="transfer-btn cancel" data-action="remove">
                        <i class="fas fa-times"></i> 移除
                    </button>
                `;
            default:
                return '';
        }
    }
    
    // 绑定任务控制按钮事件
    bindTaskControlEvents(taskElement, task) {
        const taskId = task.id;
        
        taskElement.querySelectorAll('.transfer-btn').forEach(button => {
            button.addEventListener('click', event => {
                const action = event.currentTarget.dataset.action;
                
                switch (action) {
                    case 'pause':
                        this.pauseTask(taskId);
                        break;
                    case 'resume':
                        this.resumeTask(taskId);
                        break;
                    case 'cancel':
                        this.cancelTask(taskId);
                        break;
                    case 'retry':
                        this.retryTask(taskId);
                        break;
                    case 'remove':
                        this.removeTask(taskId);
                        break;
                }
            });
        });
    }
    
    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0 || !bytes) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 创建和初始化传输管理器实例
const transferManager = new FileTransferManager();

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    transferManager.initialize();
});

// 导出管理器实例
window.transferManager = transferManager; 