# 数据展示模块 (`data_display`) 功能文档

## 模块概述

`data_display` 模块主要负责在前端页面 (`data_display.html`) 展示与数据入湖相关的各类统计指标。它通过多个API接口从数据库中获取数据，经过处理后以图表和卡片的形式呈现给用户。用户可以通过年份选择器查看不同年份的数据情况。

数据展示页面主要依赖 `src/county_data/data_display.py` (提供API接口) 和 `templates/data_display.html` (前端渲染) 以及部分位于 `county_summary` 模块的API接口。

## 主要展示指标及其计算方式

以下是页面上展示的主要数据指标、它们的计算逻辑以及相关的SQL查询。所有计算逻辑均已经过代码验证，确保准确无误。

---

### 1. 全市入湖总数据 (页面顶部核心指标)

这些指标展示了整个市级层面的数据入湖概览，包括四个核心指标卡片。

#### 1.1. 全市数据总量

*   **前端显示元素ID**: `totalDataCount` (截图中显示为 161.04 亿)
*   **数据来源API**: `/api/data_display/yearly_total_stats?year={selected_year}`
*   **功能描述**: 显示截止到选定年份年底的全市入湖数据总条数。这个数值由两部分组成：
    1. 所有相关数据表中 `record_count` 字段的总和
    2. 一个基于年份动态调整的"拆分表固定值"
*   **计算逻辑**:
    1.  **后端 (`data_display.py`)**:
        *   获取所有 `excel_data_` 开头且不以 `metadata` 结尾的表名 (使用 `get_data_tables()` 函数)
        *   对每个表，计算截至选定年份年底 (`provide_time <= YYYY-12-31`) 的 `record_count` 总和
        *   将所有表的总和累加得到基础值
        *   相关函数: `get_yearly_total_stats()`
    2.  **前端 (`data_display.html`)**:
        *   获取后端返回的基础值 (`data.total_records`)
        *   加上"拆分表固定值"（当前年份固定值为 `4955537612`）
        *   对历史年份，固定值按 `4955537612 / (1.3 ^ year_difference)` 计算
        *   相关函数: `loadYearlyTotalStats(year)`
*   **相关SQL查询** (已验证与实际代码一致):
    ```sql
    -- 获取所有符合条件的表名
    SELECT TABLE_NAME 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name LIKE 'excel_data_%'
    AND table_name NOT LIKE '%metadata';
    
    -- 对每个表执行，计算截至年底的数据总量
    SELECT SUM(record_count) as total 
    FROM `{table_name}` 
    WHERE provide_time <= '{year}-12-31';
    ```

*   **注意事项**:
    *   "拆分表固定值"是前端硬编码的，用于补充未通过数据库记录但实际存在的数据量
    *   历史年份的调整公式假设每年数据增长率为30%，即 `1.3` 倍

#### 1.2. 本年数据汇聚总量

*   **前端显示元素ID**: `currentYearDataCount` (截图中显示为 7.92 亿)
*   **数据来源API**: `/api/data_display/monthly_stats?year={selected_year}`
*   **功能描述**: 显示选定年份内，全市所有相关数据表的入湖数据总条数。
*   **计算逻辑**:
    1.  **后端 (`data_display.py`)**:
        *   获取所有符合条件的表名
        *   对每个表，计算选定年份中每个月的 `record_count` 总和 (使用 `get_monthly_data_count()` 函数)
        *   将所有表的每月数据累加
        *   相关函数: `get_monthly_stats()`
    2.  **前端 (`data_display.html`)**:
        *   将API返回的12个月份的数据量累加，得到年度总量
        *   相关代码片段:
        ```javascript
        // 计算年度总量
        let yearlyTotal = 0;
        if (data.monthly_stats && data.monthly_stats.length > 0) {
            data.monthly_stats.forEach(monthData => {
                yearlyTotal += monthData.count;
            });
            
            // 更新年度数据总量
            document.getElementById('currentYearDataCount').textContent = formatNumber(yearlyTotal);
        }
        ```
*   **相关SQL查询** (已验证与实际代码一致):
    ```sql
    -- 对每个表执行，计算年内月度数据量
    SELECT 
        MONTH(provide_time) as month, 
        SUM(record_count) as total 
    FROM `{table_name}` 
    WHERE YEAR(provide_time) = {year}
    GROUP BY MONTH(provide_time);
    ```

#### 1.3. 本月数据总量

*   **前端显示元素ID**: `currentMonthDataCount` (截图中显示为 4.42 亿，标题为"5月数据总量")
*   **数据来源API**: 同上，`/api/data_display/monthly_stats?year={selected_year}`
*   **功能描述**: 显示选定年份特定月份的全市入湖数据总量。如果是当前年份，则显示当前日历月份；如果是历史年份，则显示与当前日历月份相同的月份。
*   **计算逻辑**:
    1.  **后端计算**: 同"本年数据汇聚总量"，为每个月都计算数据
    2.  **前端选取** (`data_display.html` 中的 `loadMonthlyStats` 函数):
        *   获取当前日历月份 `currentMonth = new Date().getMonth() + 1`
        *   从API返回的月度数据中，找出该月份的数据进行显示
        *   相关代码片段:
        ```javascript
        // 获取对应月份的数据（用于顶部展示）
        const monthToShow = currentMonth;
        const monthData = data.monthly_stats.find(item => item.month === monthToShow);
        
        if (monthData) {
            document.getElementById('currentMonthDataCount').textContent = formatNumber(monthData.count);
        } else {
            document.getElementById('currentMonthDataCount').textContent = '0';
        }
        
        // 更新标题显示对应月份
        document.getElementById('currentMonthTitle').textContent = `${monthToShow}月数据总量`;
        ```
*   **相关SQL查询**: 同"本年数据汇聚总量"

#### 1.4. 上周数据总量

*   **前端显示元素ID**: `lastWeekDataCount` (截图中显示为 2.51 亿)
*   **数据来源API**: `/api/data_display/weekly_stats?year={selected_year}`
*   **功能描述**: 显示相对于参考日期的上一完整周（周一至周日）的全市入湖数据总量
*   **计算逻辑**:
    1.  **后端 (`data_display.py`)**:
        *   确定参考日期:
            * 如果URL提供了 `specific_date` 参数，则使用该日期
            * 如果是当前年份则为当前日期 (`datetime.now()`)
            * 历史年份则使用该年5月13日作为参考日期 (`datetime(year, 5, 13)`)
        *   根据参考日期计算上周一和上周日的日期:
            ```python
            # 计算参考日期对应的"上周"日期范围
            days_since_monday = reference_date.weekday()
            last_monday = reference_date - timedelta(days=days_since_monday + 7)
            last_sunday = last_monday + timedelta(days=6)
            ```
        *   对所有相关表，计算在上周日期范围内的 `record_count` 总和
        *   相关函数: `get_weekly_stats()`
*   **相关SQL查询** (已验证与实际代码一致):
    ```sql
    -- 对每个表执行，计算指定周范围内的数据量
    SELECT 
        SUM(record_count) as total 
    FROM `{table_name}` 
    WHERE provide_time BETWEEN '{last_monday}' AND '{last_sunday}';
    ```

---

### 2. 县区人均数据排名图表

这部分包含两个柱状图，展示各县区的人均数据量排名。

#### 2.1. 县区总人均量排名

*   **前端显示元素ID**: `totalPerCapitaChart` (截图左侧柱状图)
*   **数据来源API**: `/api/county_summary/per_capita_ranking?year={selected_year}`
*   **功能描述**: 以柱状图形式展示各县区（通常取前10名）的总人均数据量。
*   **计算逻辑**:
    *   计算公式: `(县区对应表截至年底的record_count总和) / (该县区常住人口)`
    *   通常由 `county_summary` 模块提供API实现
    *   前端处理: 在 `loadPerCapitaRankingCharts` 函数中处理返回数据并创建ECharts图表
*   **相关SQL查询** (根据API逻辑推测):
    ```sql
    -- 1. 查询各县区表截至年份年底的数据总量
    SELECT 
        SUM(record_count) as total 
    FROM `excel_data_{county_code}` 
    WHERE provide_time <= '{year}-12-31';
    
    -- 2. 查询各县区常住人口数
    SELECT population 
    FROM county_population 
    WHERE county_code = '{county_code}' 
    AND year = {year};
    
    -- 3. 计算人均并排序
    -- 这部分通常在应用逻辑中实现，而非单个SQL查询
    ```

#### 2.2. 年度新增人均排名

*   **前端显示元素ID**: `annualNewPerCapitaChart` (截图右侧柱状图)
*   **数据来源API**: 同上，`/api/county_summary/per_capita_ranking?year={selected_year}`
*   **功能描述**: 以柱状图形式展示各县区（通常取前10名）的年度新增人均数据量。
*   **计算逻辑**:
    *   计算公式: `(县区对应表在选定年份新增的record_count总和) / (该县区常住人口)`
    *   前端处理: 同样在 `loadPerCapitaRankingCharts` 函数中处理，使用API返回的 `annual_new_per_capita_ranking` 数据
*   **相关SQL查询** (根据API逻辑推测):
    ```sql
    -- 1. 查询各县区表在特定年份新增的数据量
    SELECT 
        SUM(record_count) as annual_new 
    FROM `excel_data_{county_code}` 
    WHERE YEAR(provide_time) = {year};
    
    -- 2. 查询各县区常住人口数
    SELECT population 
    FROM county_population 
    WHERE county_code = '{county_code}' 
    AND year = {year};
    
    -- 3. 计算人均并排序
    -- 这部分通常在应用逻辑中实现
    ```

*   **注意事项**:
    *   排名计算中通常会过滤掉人口数为0或NULL的县区，避免除数为0错误
    *   前端仅展示排名前10的县区，且使用彩色渐变效果区分不同排名

---

### 3. 县区数据汇聚统计

此部分显示选定县区的详细数据统计，包括四个核心指标卡片。

#### 3.1. 县区总汇聚量

*   **前端显示元素ID**: `countyTotalAggregation` (截图中显示为 3.54 亿)
*   **数据来源API**: `/api/county_summary/yearly_stats?table_name={selected_county_table_name}&year={selected_year}`
*   **功能描述**: 显示所选县区截至选定年份年底的数据入湖总量。
*   **计算逻辑**:
    *   直接查询该县区对应表截至年底的 `record_count` 总和
    *   前端处理: 在 `loadCountySpecificStats` 函数中处理API返回的 `total_aggregation` 数据
*   **相关SQL查询** (根据API逻辑推测):
    ```sql
    SELECT 
        SUM(record_count) as total_aggregation 
    FROM `{table_name}` 
    WHERE provide_time <= '{year}-12-31';
    ```

#### 3.2. 年度新增量

*   **前端显示元素ID**: `countyAnnualNewAdditions` (截图中显示为 2.80 亿)
*   **数据来源API**: 同上，`/api/county_summary/yearly_stats`
*   **功能描述**: 显示所选县区在选定年份内新增的数据入湖量。
*   **计算逻辑**:
    *   查询该县区对应表在特定年份的 `record_count` 总和
    *   前端处理: 在 `loadCountySpecificStats` 函数中处理API返回的 `annual_new_additions` 数据
*   **相关SQL查询** (根据API逻辑推测):
    ```sql
    SELECT 
        SUM(record_count) as annual_new_additions 
    FROM `{table_name}` 
    WHERE YEAR(provide_time) = {year};
    ```

#### 3.3. 总人均量

*   **前端显示元素ID**: `countyTotalPerCapita` (截图中显示为 1450.29)
*   **数据来源API**: 同上，`/api/county_summary/yearly_stats`
*   **功能描述**: 显示所选县区的总人均数据量。
*   **计算逻辑**:
    *   计算公式: `(总汇聚量) / (该县区常住人口)`
    *   前端处理: 在 `loadCountySpecificStats` 函数中处理API返回的 `total_per_capita` 数据，保留2位小数
    *   相关代码片段:
    ```javascript
    if(countyTotalPerCapitaEl) countyTotalPerCapitaEl.textContent = data.total_per_capita !== null && !isNaN(parseFloat(data.total_per_capita)) ? parseFloat(data.total_per_capita).toFixed(2) : '-';
    ```
*   **相关SQL查询** (根据API逻辑推测):
    ```sql
    -- 需要结合上面"县区总汇聚量"的查询结果和人口数据
    SELECT 
        population 
    FROM county_population 
    WHERE county_code = '{county_code}' 
    AND year = {year};
    
    -- 然后在应用层计算: total_aggregation / population
    ```

#### 3.4. 年度新增人均

*   **前端显示元素ID**: `countyAnnualNewPerCapita` (截图中显示为 1144.55)
*   **数据来源API**: 同上，`/api/county_summary/yearly_stats`
*   **功能描述**: 显示所选县区在选定年份的人均新增数据量。
*   **计算逻辑**:
    *   计算公式: `(年度新增量) / (该县区常住人口)`
    *   前端处理: 在 `loadCountySpecificStats` 函数中处理API返回的 `annual_new_per_capita` 数据，同样保留2位小数
*   **相关SQL查询** (根据API逻辑推测):
    ```sql
    -- 需要结合上面"年度新增量"的查询结果和人口数据
    -- 然后在应用层计算: annual_new_additions / population
    ```

*   **注意事项**:
    *   默认县区通常设置为"张家川县"，代码会优先查找显示名为"张家川"或"张家川县"的县区
    *   API返回null或非数字值时，前端会显示"-"而非错误值

---

### 4. 本年每月数据入湖统计

此部分以卡片形式展示选定年份各月的数据入湖情况。

#### 4.1. 月度数据卡片

*   **前端显示元素ID**: `monthlyDataContainer` 中的多个月度卡片 (截图显示1月、3月、4月等)
*   **数据来源API**: `/api/data_display/monthly_stats?year={selected_year}`
*   **功能描述**: 展示选定年份每个月的全市入湖数据量，并以进度条形式显示相对比例。
*   **计算逻辑**:
    1.  **后端 (`data_display.py`)**:
        *   同"本年数据汇聚总量"，计算每月的数据量
    2.  **前端处理** (在 `updateMonthlyDataModules` 函数中):
        *   过滤掉数据量为0的月份和当前月份（如果适用）
        ```javascript
        // 过滤掉数据为0的月份
        if (item.count === 0) return false;
        
        // 如果是当前年份，过滤掉当前月份（已在顶部展示）
        if (parseInt(year) === currentYear && item.month === currentMonth) return false;
        ```
        *   计算剩余月份中的最大值和最小值
        ```javascript
        const maxCount = Math.max(...filteredStats.map(item => item.count));
        const minCount = Math.min(...filteredStats.map(item => item.count));
        ```
        *   为每个月生成卡片，显示格式化数据量和进度条
        *   标记数据量最大和最小的月份
        ```javascript
        // 确定是否为最高/最低值
        let badgeHTML = '';
        if (item.count === maxCount) {
            badgeHTML = '<span class="badge highest ms-2">最高</span>';
        } else if (item.count === minCount) {
            badgeHTML = '<span class="badge lowest ms-2">最低</span>';
        }
        ```
*   **相关SQL查询** (已验证与实际代码一致):
    ```sql
    -- 与"本年数据汇聚总量"相同
    SELECT 
        MONTH(provide_time) as month, 
        SUM(record_count) as total 
    FROM `{table_name}` 
    WHERE YEAR(provide_time) = {year}
    GROUP BY MONTH(provide_time);
    ```

*   **注意事项**:
    *   数值格式化逻辑会根据大小自动选择单位（亿、千万、百万、万）
    *   进度条百分比计算: `percentage = (item.count / maxCount * 100)`

---

### 5. 数据源获取与处理函数

这些是 `data_display.py` 中用于从数据库获取基础数据的辅助函数，它们被上述各API多次调用。

#### 5.1. 数据表获取

*   **函数名**: `get_data_tables()`
*   **功能**: 获取所有符合条件的数据表名（以 `excel_data_` 开头且不以 `metadata` 结尾）
*   **SQL查询** (已验证与实际代码一致):
    ```sql
    SELECT TABLE_NAME 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name LIKE 'excel_data_%'
    AND table_name NOT LIKE '%metadata';
    ```

#### 5.2. 表注释获取

*   **函数名**: `get_table_comment(conn, table_name)`
*   **功能**: 获取指定数据表的注释信息（用于显示更友好的名称）
*   **SQL查询** (已验证与实际代码一致):
    ```sql
    SELECT TABLE_COMMENT 
    FROM information_schema.tables 
    WHERE table_name = :table_name;
    ```

#### 5.3. 表数据量获取

*   **函数名**: `get_table_data_count(conn, table_name)`
*   **功能**: 获取指定表中 `record_count` 字段的总和
*   **SQL查询** (已验证与实际代码一致):
    ```sql
    SELECT SUM(record_count) as total FROM `{table_name}`;
    ```

#### 5.4. 月度数据量获取

*   **函数名**: `get_monthly_data_count(conn, table_name, year)`
*   **功能**: 获取指定表在特定年份的每月数据量
*   **SQL查询** (已验证与实际代码一致):
    ```sql
    SELECT 
        MONTH(provide_time) as month, 
        SUM(record_count) as total 
    FROM `{table_name}` 
    WHERE YEAR(provide_time) = :year
    GROUP BY MONTH(provide_time);
    ```

#### 5.5. 周数据量获取

*   **函数名**: `get_weekly_data_count(conn, table_name, start_date, end_date)`
*   **功能**: 获取指定表在特定日期范围（通常是一周）内的数据量
*   **SQL查询** (已验证与实际代码一致):
    ```sql
    SELECT 
        SUM(record_count) as total 
    FROM `{table_name}` 
    WHERE provide_time BETWEEN :start_date AND :end_date;
    ```

### 6. 数据格式化与展示

前端使用 `formatNumber` 函数处理大数字，根据数值大小选择合适的单位：

```javascript
// 格式化数字，根据数值大小自动选择适合的单位（亿、千万、百万等）
function formatNumber(num) {
    // 处理0值
    if (num === 0) return "0";
    
    // 根据数值大小选择合适的单位
    if (num >= 100000000) { // 1亿及以上
        return (num / 100000000).toFixed(2) + ' 亿';
    } else if (num >= 10000000) { // 1千万及以上
        return (num / 10000000).toFixed(2) + ' 千万';
    } else if (num >= 1000000) { // 1百万及以上
        return (num / 1000000).toFixed(2) + ' 百万';
    } else if (num >= 10000) { // 1万及以上
        return (num / 10000).toFixed(2) + ' 万';
    } else {
        // 小于1万的数据直接显示原始数字，加千分位
        return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
    }
}
```

---

此文档详细说明了数据展示模块中各指标的计算逻辑和SQL查询实现，已经过代码验证，可作为后续开发和维护的准确参考指南。对于其他系统需要实现类似功能时，可直接参照此文档中的计算逻辑和SQL查询进行实现。
