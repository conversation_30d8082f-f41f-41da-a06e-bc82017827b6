/* 左侧导航栏样式 */
.sidebar-layout {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background-color: #343a40;
    color: white;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    transition: all 0.3s;
    z-index: 1000;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.sidebar-header {
    padding: 20px 15px;
    background-color: #212529;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header .navbar-brand {
    color: white;
    font-size: 1.3rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    text-decoration: none;
}

.sidebar-nav {
    padding: 15px 0;
}

.sidebar-nav .nav-item {
    width: 100%;
    margin-bottom: 5px;
}

.sidebar-nav .nav-link {
    color: rgba(255, 255, 255, 0.8);
    padding: 12px 20px;
    display: flex;
    align-items: center;
    transition: all 0.3s;
    border-left: 3px solid transparent;
    border-radius: 0 4px 4px 0;
}

.sidebar-nav .nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-left: 3px solid #0d6efd;
}

.sidebar-nav .nav-link.active {
    color: white;
    background-color: rgba(13, 110, 253, 0.25);
    border-left: 3px solid #0d6efd;
}

.sidebar-nav .nav-link i {
    margin-right: 10px;
    font-size: 1.1rem;
}

.content-wrapper {
    flex: 1;
    margin-left: 250px;
    padding: 20px;
    transition: all 0.3s;
    background-color: #f8f9fa;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sidebar {
        margin-left: -250px;
    }
    
    .sidebar.active {
        margin-left: 0;
    }
    
    .content-wrapper {
        margin-left: 0;
        width: 100%;
    }
    
    .content-wrapper.active {
        margin-left: 250px;
        transform: translateX(250px);
    }
    
    .sidebar-toggle {
        display: block;
    }
}

/* 统计卡片调整 */
.stats-container {
    margin-bottom: 1.5rem;
}

.stats-card {
    background-color: white;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    text-align: center;
    transition: transform 0.3s;
    height: 100%;
}

.stats-card:hover {
    transform: translateY(-3px);
}

.stats-icon {
    font-size: 1.5rem;
    color: #0d6efd;
    margin-bottom: 8px;
}

.stats-value {
    font-size: 1.8rem;
    font-weight: bold;
    margin-bottom: 3px;
    line-height: 1.2;
}

.stats-label {
    color: #6c757d;
    font-size: 0.85rem;
}

/* 移动设备上的导航切换按钮 */
.sidebar-toggle {
    position: fixed;
    top: 10px;
    left: 10px;
    z-index: 1050;
    display: none;
    background-color: #343a40;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 8px 12px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    transition: all 0.3s;
}

.sidebar-toggle:hover {
    background-color: #212529;
    transform: scale(1.05);
}

/* 导航栏样式继承 */
.navbar-dark {
    background-color: #343a40;
}

/* 滚动条美化 */
.sidebar::-webkit-scrollbar {
    width: 5px;
}

.sidebar::-webkit-scrollbar-track {
    background: #343a40;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
}

/* 内容区域的section样式 */
.section {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.section-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 15px;
}

.section-header h2 {
    font-size: 1.5rem;
    margin: 0;
    display: flex;
    align-items: center;
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.sidebar, .content-wrapper {
    animation: fadeIn 0.5s ease-in-out;
}
