<!-- 通用页脚组件 -->
<footer class="mt-5 text-center text-muted">
    <p><small>2025 数据管理系统 | 版本 <span id="appVersion">v4</span>
        <a href="javascript:void(0)" class="btn btn-link p-0 ms-2" id="changelogBtn" style="font-size: 1em;vertical-align: baseline;">更新日志</a>
    </small></p>
</footer>

<!-- 引入通用版本更新日志模态框 -->
{% include 'changelog_modal.html' %}

<!-- 页脚版本号更新脚本 -->
<script>
  $(function(){
    // 获取并更新版本号
    fetch('/api/version')
      .then(response => response.json())
      .then(data => {
        $('#appVersion').text(data.version);
      })
      .catch(error => {
        console.error('获取版本信息失败:', error);
      });
  });
</script> 