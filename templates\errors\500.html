<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器错误 - 500</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON><PERSON>, sans-serif;
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .error-container {
            text-align: center;
            background: white;
            padding: 3rem;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 2rem;
        }
        .error-code {
            font-size: 8rem;
            font-weight: bold;
            color: #ff6b6b;
            margin: 0;
            line-height: 1;
        }
        .error-title {
            font-size: 2rem;
            color: #333;
            margin: 1rem 0;
        }
        .error-message {
            font-size: 1.1rem;
            color: #666;
            margin: 1.5rem 0;
            line-height: 1.6;
        }
        .error-actions {
            margin-top: 2rem;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 0 10px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn-primary {
            background: #ff6b6b;
            color: white;
        }
        .btn-primary:hover {
            background: #ff5252;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }
        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }
        .error-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        .error-details {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            margin: 1rem 0;
            font-size: 0.9rem;
            color: #666;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="error-container">
        <div class="error-icon">⚠️</div>
        <h1 class="error-code">500</h1>
        <h2 class="error-title">服务器内部错误</h2>
        <p class="error-message">
            抱歉，服务器遇到了一个内部错误，无法完成您的请求。<br>
            我们的技术团队已经收到通知，正在处理这个问题。
        </p>
        <div class="error-details">
            <strong>可能的原因：</strong><br>
            • 服务器临时过载<br>
            • 数据库连接问题<br>
            • 应用程序配置错误<br>
            • 系统维护中
        </div>
        <div class="error-actions">
            <a href="/" class="btn btn-primary">返回首页</a>
            <a href="javascript:location.reload()" class="btn btn-secondary">刷新页面</a>
        </div>
    </div>
</body>
</html>
