"""
创建服务器管理模块所需的数据库表
"""

from sqlalchemy import text
import traceback

def create_server_tables():
    try:
        # 导入数据库连接函数
        from src.utils.db_manager import DatabaseManager
        engine = DatabaseManager.get_main_db()
        
        # 创建服务器基本信息表
        with engine.connect() as conn:
            # 创建服务器基本信息表
            create_server_table = text("""
                CREATE TABLE IF NOT EXISTS `server_information_metadata` (
  `ip` varchar(255) DEFAULT NULL COMMENT '服务器ip',
  `deploy_component` varchar(255) DEFAULT NULL COMMENT '服务',
  `app_port` varchar(255) DEFAULT NULL COMMENT '服务端口',
  `dubbo_server_port` varchar(255) DEFAULT NULL COMMENT 'dubbo服务的端口',
  `xxl_job_port` int(11) DEFAULT NULL COMMENT '定时任务的端口'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
            """)
            conn.execute(create_server_table)
            
            # 创建服务端口信息表
            create_port_table = text("""
                CREATE TABLE IF NOT EXISTS `server_port_metadata` (
  `ip` varchar(255) DEFAULT NULL COMMENT '服务器ip',
  `hostname` varchar(255) DEFAULT NULL COMMENT '主机名',
  `platform` varchar(255) DEFAULT NULL COMMENT '所属平台',
  `cpu` int(11) DEFAULT NULL COMMENT '服务器cpu',
  `memory` int(11) DEFAULT NULL COMMENT '服务器内存',
  `disk` int(11) DEFAULT NULL COMMENT '服务器存储',
  `os_system` varchar(255) DEFAULT NULL COMMENT '服务器系统',
  `deploy_components` text COMMENT '服务器部署的服务'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='平台、服务器内容';
            """)
            conn.execute(create_port_table)
            
            conn.commit()
            
        print("服务器管理模块数据库表创建成功！")
        return True
    
    except Exception as e:
        print(f"创建数据库表时出错: {str(e)}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    create_server_tables()
