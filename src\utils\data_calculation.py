"""
数据计算公共模块 - 提供数据统计和计算相关的通用函数

此模块包含全市和县区数据计算的公共函数，用于数据展示页面和其他入湖页面
"""
from typing import Dict, List, Any, Optional, Tuple, Union
from sqlalchemy import text
import logging
from datetime import datetime, timedelta
import calendar
from src.county_data.metrics_config import REGIONS_CONFIG

# 导入性能监控和缓存
from .performance_monitor import monitor_performance, performance_monitor
from .cache_manager import cached, cache_key_generator
from .query_optimizer import optimized_query

# 配置日志
logger = logging.getLogger(__name__)

def get_db_connection():
    """从数据库管理器获取数据库连接"""
    from src.utils.db_manager import DatabaseManager
    return DatabaseManager.get_main_db()

# 基础数据获取函数
@cached(cache_name='database', ttl_seconds=1800)  # 缓存30分钟
@monitor_performance(name='get_data_tables')
def get_data_tables() -> List[str]:
    """
    获取所有数据表名（以excel_data_开头且不以metadata结尾的表）
    """
    tables_query = """
    SELECT TABLE_NAME
    FROM information_schema.tables
    WHERE table_schema = DATABASE()
    AND table_name LIKE 'excel_data_%'
    AND table_name NOT LIKE '%metadata'
    """

    try:
        engine = get_db_connection()
        with engine.connect() as conn:
            tables_result = conn.execute(text(tables_query))
            return [row[0] for row in tables_result]
    except Exception as e:
        logger.error(f"获取数据表列表出错: {str(e)}")
        return []

@cached(cache_name='database', ttl_seconds=3600, key_func=cache_key_generator(0))  # 缓存1小时
@monitor_performance(name='get_table_comment')
def get_table_comment(table_name: str) -> str:
    """
    获取表注释（安全地使用参数化查询）
    """
    comment_query = """
    SELECT TABLE_COMMENT
    FROM information_schema.tables
    WHERE table_name = :table_name
    """
    try:
        engine = get_db_connection()
        with engine.connect() as conn:
            comment_result = conn.execute(text(comment_query), {"table_name": table_name})
            return next(iter(comment_result), [""])[0]
    except Exception as e:
        logger.error(f"获取表 {table_name} 注释出错: {str(e)}")
        return ""

def get_county_code_from_table(table_name: str) -> str:
    """
    从表名提取县区代码
    """
    return table_name.replace('excel_data_', '')

def get_county_name_from_code(county_code: str) -> str:
    """
    通过县区代码获取县区名称
    """
    county_name_mapping = {
        'qin_an': '秦安县',
        'zhang_jia_chuan': '张家川县',
        'qin_shui': '清水县',
        'gan_gu': '甘谷县',
        'mai_ji': '麦积区',
        'wu_shan': '武山县',
        'qin_zhou': '秦州区'
    }
    
    # 从映射中获取
    county_name = county_name_mapping.get(county_code, '')
    if county_name:
        return county_name
    
    # 如果映射中没有，尝试从表注释中获取
    table_name = f"excel_data_{county_code}"
    table_comment = get_table_comment(table_name)
    if table_comment and '_' in table_comment:
        parts = table_comment.split('_')
        if len(parts) > 1:
            return parts[-1]
    
    # 默认处理，将下划线替换为空格
    return county_code.replace('_', ' ')

@cached(cache_name='database', ttl_seconds=7200, key_func=cache_key_generator(0))  # 缓存2小时
@monitor_performance(name='get_county_population')
def get_county_population(county_code: str, year: int) -> int:
    """
    获取指定县区在特定年份的人口数据
    """
    try:
        engine = get_db_connection()
        with engine.connect() as conn:
            population_query = text("""
                SELECT population
                FROM country_people_metadata
                WHERE englinsh_provider = :english_provider
            """)
            total_population = conn.execute(population_query, {"english_provider": county_code}).scalar() or 0
            return int(total_population)
    except Exception as e:
        logger.error(f"获取县区 {county_code} 人口数据出错: {str(e)}")
        return 0

# 数据统计函数
def get_total_records(table_name: str, end_date: str) -> int:
    """
    获取截至特定日期的记录总数
    
    参数:
        table_name: 表名
        end_date: 截止日期，格式为 'YYYY-MM-DD'
        
    返回:
        截至指定日期的record_count总和
    """
    try:
        engine = get_db_connection()
        with engine.connect() as conn:
            # 表名不能参数化，但已通过get_data_tables()验证
            query = f"""
            SELECT COALESCE(SUM(record_count), 0) as total 
            FROM `{table_name}` 
            WHERE provide_time <= :end_date
            """
            result = conn.execute(text(query), {"end_date": end_date})
            return int(next(iter(result), [0])[0] or 0)
    except Exception as e:
        logger.error(f"获取表 {table_name} 截至 {end_date} 的记录总数出错: {str(e)}")
        return 0

def get_records_by_period(table_name: str, start_date: str, end_date: str) -> int:
    """
    获取特定日期范围内的记录数
    
    参数:
        table_name: 表名
        start_date: 开始日期，格式为 'YYYY-MM-DD'
        end_date: 结束日期，格式为 'YYYY-MM-DD'
        
    返回:
        日期范围内的record_count总和
    """
    try:
        engine = get_db_connection()
        with engine.connect() as conn:
            query = f"""
            SELECT COALESCE(SUM(record_count), 0) as total 
            FROM `{table_name}` 
            WHERE provide_time BETWEEN :start_date AND :end_date
            """
            params = {"start_date": start_date, "end_date": end_date}
            result = conn.execute(text(query), params)
            return int(next(iter(result), [0])[0] or 0)
    except Exception as e:
        logger.error(f"获取表 {table_name} 在 {start_date} 至 {end_date} 期间的记录数出错: {str(e)}")
        return 0

def get_yearly_records(table_name: str, year: int) -> int:
    """
    获取特定年份的记录数
    
    参数:
        table_name: 表名
        year: 年份
        
    返回:
        年份内的record_count总和
    """
    start_date = f"{year}-01-01"
    end_date = f"{year}-12-31"
    return get_records_by_period(table_name, start_date, end_date)

def get_monthly_records(table_name: str, year: int) -> Dict[int, int]:
    """
    获取特定年份每月的记录数
    
    参数:
        table_name: 表名
        year: 年份
        
    返回:
        字典，键为月份(1-12)，值为该月数据量
    """
    monthly_data = {month: 0 for month in range(1, 13)}
    try:
        engine = get_db_connection()
        with engine.connect() as conn:
            query = f"""
            SELECT 
                MONTH(provide_time) as month, 
                SUM(record_count) as total 
            FROM `{table_name}` 
            WHERE YEAR(provide_time) = :year
            GROUP BY MONTH(provide_time)
            """
            result = conn.execute(text(query), {"year": year})
            for row in result:
                month = row[0]  # 月份(1-12)
                count = row[1] or 0  # 该月数据量
                if 1 <= month <= 12:  # 确保月份有效
                    monthly_data[month] = int(count)
                else:
                    logger.warning(f"表 {table_name} 中发现无效月份: {month}")
        return monthly_data
    except Exception as e:
        logger.error(f"获取表 {table_name} 年份 {year} 月份数据出错: {str(e)}")
        return monthly_data

def get_weekly_records(table_name: str, reference_date: datetime) -> int:
    """
    获取相对于参考日期的上一周记录数
    
    参数:
        table_name: 表名
        reference_date: 参考日期
        
    返回:
        上周的record_count总和
    """
    days_since_monday = reference_date.weekday()
    last_monday = reference_date - timedelta(days=days_since_monday + 7)
    last_sunday = last_monday + timedelta(days=6)
    
    last_monday_str = last_monday.strftime('%Y-%m-%d')
    last_sunday_str = last_sunday.strftime('%Y-%m-%d')
    
    return get_records_by_period(table_name, last_monday_str, last_sunday_str)

def calculate_date_range(reference_date: datetime, period_type: str) -> Tuple[str, str]:
    """
    计算特定周期的日期范围
    
    参数:
        reference_date: 参考日期
        period_type: 周期类型 ('week', 'month', 'year')
        
    返回:
        (start_date, end_date) 元组，格式为 'YYYY-MM-DD'
    """
    if period_type == 'week':
        days_since_monday = reference_date.weekday()
        last_monday = reference_date - timedelta(days=days_since_monday + 7)
        last_sunday = last_monday + timedelta(days=6)
        return last_monday.strftime('%Y-%m-%d'), last_sunday.strftime('%Y-%m-%d')
    elif period_type == 'month':
        first_day = reference_date.replace(day=1)
        if reference_date.month == 1:
            last_month = reference_date.replace(year=reference_date.year-1, month=12, day=1)
        else:
            last_month = reference_date.replace(month=reference_date.month-1, day=1)
        last_day = calendar.monthrange(last_month.year, last_month.month)[1]
        last_day_date = last_month.replace(day=last_day)
        return last_month.strftime('%Y-%m-%d'), last_day_date.strftime('%Y-%m-%d')
    elif period_type == 'year':
        year_start = reference_date.replace(month=1, day=1)
        year_end = reference_date.replace(month=12, day=31)
        return year_start.strftime('%Y-%m-%d'), year_end.strftime('%Y-%m-%d')
    else:
        raise ValueError(f"不支持的周期类型: {period_type}")

# 全市数据统计函数
@cached(cache_name='api', ttl_seconds=600, key_func=cache_key_generator(0))  # 缓存10分钟
@monitor_performance(name='calculate_city_total_data', include_system_metrics=True)
def calculate_city_total_data(year: int) -> int:
    """计算全市总数据量"""
    try:
        engine = get_db_connection()
        total_records = 0

        # 遍历所有县区表
        for region in REGIONS_CONFIG:
            try:
                # 获取县区总数据量
                region_total = get_region_total_data(region, year, engine)
                total_records += region_total
            except Exception as e:
                logger.error(f"计算县区 {region['name']} 总数据量时出错: {str(e)}")
                continue

        return total_records
    except Exception as e:
        logger.error(f"计算全市总数据量时出错: {str(e)}")
        return 0

def calculate_city_yearly_data(year: int) -> int:
    """计算全市年度新增数据量"""
    try:
        engine = get_db_connection()
        yearly_increment = 0
        
        # 遍历所有县区表
        for region in REGIONS_CONFIG:
            try:
                # 获取县区年度新增数据量
                region_increment = get_region_yearly_increment(region, year, engine)
                yearly_increment += region_increment
            except Exception as e:
                logger.error(f"计算县区 {region['name']} 年度新增数据量时出错: {str(e)}")
                continue
        
        return yearly_increment
    except Exception as e:
        logger.error(f"计算全市年度新增数据量时出错: {str(e)}")
        return 0

def calculate_city_monthly_data(year: int, month: int) -> int:
    """
    计算全市特定月份的数据量
    
    参数:
        year: 年份
        month: 月份(1-12)
        
    返回:
        特定月份内全市数据总量
    """
    tables = get_data_tables()
    monthly_records = 0
    
    for table in tables:
        try:
            monthly_data = get_monthly_records(table, year)
            monthly_records += monthly_data.get(month, 0)
        except Exception as e:
            logger.error(f"获取表 {table} 年份 {year} 月份 {month} 的数据总量出错: {str(e)}")
    
    return monthly_records

def calculate_city_weekly_data(reference_date: datetime) -> int:
    """计算全市周数据量"""
    try:
        engine = get_db_connection()
        weekly_records = 0
        
        # 遍历所有县区表
        for region in REGIONS_CONFIG:
            try:
                # 获取县区周数据量
                region_weekly = get_region_weekly_data(region, reference_date, engine)
                weekly_records += region_weekly
            except Exception as e:
                logger.error(f"计算县区 {region['name']} 周数据量时出错: {str(e)}")
                continue
        
        return weekly_records
    except Exception as e:
        logger.error(f"计算全市周数据量时出错: {str(e)}")
        return 0

def get_monthly_stats(year: int) -> List[Dict[str, Any]]:
    """
    获取全年每月数据统计
    
    参数:
        year: 年份
        
    返回:
        包含每月数据统计的列表
    """
    # 初始化月度数据统计
    monthly_stats = [
        {
            'month': month,
            'month_name': calendar.month_name[month],
            'count': 0
        } for month in range(1, 13)
    ]
    
    # 获取所有数据表
    tables = get_data_tables()
    
    for table in tables:
        try:
            # 获取表月度数据
            monthly_data = get_monthly_records(table, year)
            
            # 累加到相应月份
            for month, count in monthly_data.items():
                monthly_stats[month-1]['count'] += count
            
        except Exception as e:
            logger.error(f"处理表 {table} 年度数据时出错: {str(e)}")
            continue
    
    return monthly_stats

# 县区数据统计函数
def calculate_county_total_data(county_code: str, year: int) -> int:
    """
    计算县区截至年份的数据总量
    
    参数:
        county_code: 县区代码
        year: 年份
        
    返回:
        截至年底的县区数据总量
    """
    table_name = f"excel_data_{county_code}"
    end_date = f"{year}-12-31"
    return get_total_records(table_name, end_date)

def calculate_county_yearly_data(county_code: str, year: int) -> int:
    """
    计算县区特定年份的数据量
    
    参数:
        county_code: 县区代码
        year: 年份
        
    返回:
        年份内县区数据总量
    """
    table_name = f"excel_data_{county_code}"
    return get_yearly_records(table_name, year)

def calculate_county_per_capita_total(county_code: str, year: int) -> float:
    """
    计算县区人均总数据量
    
    参数:
        county_code: 县区代码
        year: 年份
        
    返回:
        县区人均总数据量
    """
    total_records = calculate_county_total_data(county_code, year)
    total_population = get_county_population(county_code, year)
    
    if total_population and total_population > 0:
        return round(float(total_records) / float(total_population), 2)
    else:
        logger.warning(f"县区 {county_code} 无人口数据或人口为0")
        return 0.0

def calculate_county_per_capita_yearly(county_code: str, year: int) -> float:
    """
    计算县区人均年度数据量
    
    参数:
        county_code: 县区代码
        year: 年份
        
    返回:
        县区人均年度数据量
    """
    yearly_records = calculate_county_yearly_data(county_code, year)
    total_population = get_county_population(county_code, year)
    
    if total_population and total_population > 0:
        return round(float(yearly_records) / float(total_population), 2)
    else:
        logger.warning(f"县区 {county_code} 无人口数据或人口为0")
        return 0.0

def get_county_per_capita_ranking(year: int, ranking_type: str = 'total', top_n: int = 10) -> List[Dict[str, Any]]:
    """获取县区人均数据排名"""
    try:
        engine = get_db_connection()
        rankings = []
        for region in REGIONS_CONFIG:
            try:
                population = get_population_for_region(region['db_table_suffix'], engine)
                if not population or population <= 0:
                    logger.warning(f"{region['name']} 人口为0或无数据，跳过")
                    continue
                if ranking_type == 'total':
                    total_records = get_region_total_data(region, year, engine)
                    if total_records is None:
                        total_records = 0
                    per_capita = round(float(total_records) / float(population), 2) if population > 0 else 0
                else:
                    yearly_increment = get_region_yearly_increment(region, year, engine)
                    if yearly_increment is None:
                        yearly_increment = 0
                    per_capita = round(float(yearly_increment) / float(population), 2) if population > 0 else 0
                logger.info(f"{region['name']} 总量: {total_records if ranking_type=='total' else yearly_increment}, 人口: {population}, 人均: {per_capita}")
                if per_capita > 0:
                    rankings.append({
                        'county_name': region['name'],
                        'per_capita': per_capita,
                        'total_records': int(total_records) if ranking_type == 'total' else int(yearly_increment),
                        'population': int(population)
                    })
            except Exception as e:
                logger.error(f"计算县区 {region['name']} 人均数据时出错: {str(e)}")
                continue
        rankings.sort(key=lambda x: x['per_capita'], reverse=True)
        return rankings[:top_n]
    except Exception as e:
        logger.error(f"获取县区人均数据排名时出错: {str(e)}")
        return []

# 公共工具函数
def add_fixed_value_to_total(base_value: int, year: int) -> int:
    """
    添加"拆分表固定值"到基础数据量
    
    参数:
        base_value: 基础数据量
        year: 年份
        
    返回:
        添加固定值后的总量
    """
    # 当前年份的固定值
    current_fixed_value = 4955537612
    
    # 当前年份
    current_year = datetime.now().year
    
    # 如果是当前年份，直接使用当前固定值
    if year == current_year:
        return base_value + current_fixed_value
    
    # 对历史年份，按公式调整固定值
    year_difference = current_year - year
    adjusted_fixed_value = int(current_fixed_value / (1.3 ** year_difference))
    
    return base_value + adjusted_fixed_value

def format_large_number(num: Union[int, float]) -> str:
    """
    格式化大数字显示（亿、千万、百万等）
    
    参数:
        num: 要格式化的数字
        
    返回:
        格式化后的字符串
    """
    if num == 0:
        return "0"
    
    if num >= 100000000:  # 1亿及以上
        return f"{num / 100000000:.2f} 亿"
    elif num >= 10000000:  # 1千万及以上
        return f"{num / 10000000:.2f} 千万"
    elif num >= 1000000:  # 1百万及以上
        return f"{num / 1000000:.2f} 百万"
    elif num >= 10000:  # 1万及以上
        return f"{num / 10000:.2f} 万"
    else:
        # 小于1万的数据直接显示原始数字，加千分位
        return f"{num:,}"

def get_region_total_data(region: dict, year: int, engine) -> int:
    """获取县区总数据量"""
    try:
        table_name = f"excel_data_{region['db_table_suffix']}"
        end_date = f"{year}-12-31"
        return get_total_records(table_name, end_date)
    except Exception as e:
        logger.error(f"获取县区 {region['name']} 总数据量时出错: {str(e)}")
        return 0

def get_region_yearly_increment(region: dict, year: int, engine) -> int:
    """获取县区年度新增数据量"""
    try:
        table_name = f"excel_data_{region['db_table_suffix']}"
        return get_yearly_records(table_name, year)
    except Exception as e:
        logger.error(f"获取县区 {region['name']} 年度新增数据量时出错: {str(e)}")
        return 0

def get_region_weekly_data(region: dict, reference_date: datetime, engine) -> int:
    """获取县区周数据量"""
    try:
        table_name = f"excel_data_{region['db_table_suffix']}"
        return get_weekly_records(table_name, reference_date)
    except Exception as e:
        logger.error(f"获取县区 {region['name']} 周数据量时出错: {str(e)}")
        return 0

def get_population_for_region(region_suffix: str, engine) -> int:
    """获取县区人口数据"""
    try:
        with engine.connect() as conn:
            population_query = text("""
                SELECT population 
                FROM country_people_metadata 
                WHERE englinsh_provider = :english_provider
            """)
            total_population = conn.execute(population_query, {"english_provider": region_suffix}).scalar() or 0
            return int(total_population)
    except Exception as e:
        logger.error(f"获取县区 {region_suffix} 人口数据出错: {str(e)}")
        return 0

def format_percentage(num: float) -> str:
    """
    格式化百分比显示
    
    参数:
        num: 要格式化的数字
        
    返回:
        格式化后的百分比字符串，若为0则返回空字符串
    """
    if num is None or num == 0:
        return ""
    return f"{num:.2f}%"

def format_date(date: datetime) -> str:
    """
    格式化日期显示
    
    参数:
        date: 要格式化的日期
        
    返回:
        格式化后的日期字符串
    """
    return date.strftime('%Y-%m-%d %H:%M:%S')

def get_period_date_range(period_offset: str = 'current') -> Tuple[str, str, str]:
    """
    根据周期偏移计算日期范围
    
    参数:
        period_offset: 周期偏移，'current'表示当前周期，'-1'表示上周期，以此类推
        
    返回:
        (开始日期, 结束日期, 截止日期) 元组，格式为 'YYYY-MM-DD HH:MM:SS'
    """
    now = datetime.now()
    days_since_monday = now.weekday()
    current_period_start = now - timedelta(days=days_since_monday)
    current_period_start = current_period_start.replace(hour=11, minute=0, second=0, microsecond=0)
    
    if now < current_period_start:
        current_period_start = current_period_start - timedelta(days=7)
    
    current_period_end = current_period_start + timedelta(days=7)
    
    try:
        if period_offset == 'current':
            period_start = current_period_start
            period_end = current_period_end
            cutoff_date = min(now, period_end)
        else:
            offset = int(period_offset)
            period_start = current_period_start + timedelta(days=7 * offset)
            period_end = period_start + timedelta(days=7)
            cutoff_date = period_end
    except ValueError:
        logger.warning(f"无效的周期偏移值: {period_offset}，使用当前周期")
        period_start = current_period_start
        period_end = current_period_end
        cutoff_date = min(now, period_end)
    
    return (
        format_date(period_start),
        format_date(period_end),
        format_date(cutoff_date)
    )

def calculate_growth_rate(current: int, previous: int) -> float:
    """
    计算增长率
    
    参数:
        current: 当前值
        previous: 上一期值
        
    返回:
        增长率（百分比）
    """
    if previous == 0:
        return 0.0
    return round((current - previous) / previous * 100, 2)

def calculate_contribution_rate(part: int, total: int) -> float:
    """
    计算贡献率
    
    参数:
        part: 部分值
        total: 总值
        
    返回:
        贡献率（百分比）
    """
    if total == 0:
        return 0.0
    return round(part / total * 100, 2)

def format_stats_result(stats: Dict[str, Any]) -> Dict[str, Any]:
    """
    格式化统计结果
    
    参数:
        stats: 原始统计结果
        
    返回:
        格式化后的统计结果
    """
    formatted = {}
    
    # 格式化数值
    for key, value in stats.items():
        if isinstance(value, (int, float)):
            if key.endswith('_rate') or key.endswith('_percentage'):
                formatted[key] = format_percentage(value)
            else:
                formatted[key] = format_large_number(value)
        elif isinstance(value, datetime):
            formatted[key] = format_date(value)
        elif isinstance(value, dict):
            formatted[key] = format_stats_result(value)
        elif isinstance(value, list):
            formatted[key] = [
                format_stats_result(item) if isinstance(item, dict) else item
                for item in value
            ]
        else:
            formatted[key] = value
    
    return formatted

def get_county_code_mapping() -> Dict[str, str]:
    """
    获取县区代码映射
    
    返回:
        县区代码映射字典
    """
    return {
        'qin_an': ('620522000000', '秦安县'),
        'zhang_jia_chuan': ('620525000000', '张家川县'),
        'qin_shui': ('620521000000', '清水县'),
        'gan_gu': ('620523000000', '甘谷县'),
        'mai_ji': ('620503000000', '麦积区'),
        'wu_shan': ('620524000000', '武山县'),
        'qin_zhou': ('620502000000', '秦州区')
    }

def get_frontend_county_mapping() -> Dict[str, str]:
    """
    获取前端县区ID映射
    
    返回:
        前端县区ID映射字典
    """
    return {
        'qinan': 'qin_an',
        'zhangjiachuan': 'zhang_jia_chuan',
        'qingshui': 'qin_shui',
        'gangu': 'gan_gu',
        'maiji': 'mai_ji',
        'wushan': 'wu_shan',
        'qinzhou': 'qin_zhou'
    }

def convert_county_name_to_frontend_id(county_name: str) -> Optional[str]:
    """
    将县区名称转换为前端使用的ID
    
    参数:
        county_name: 县区名称
        
    返回:
        前端ID或None（如果未找到匹配）
    """
    county_mapping = {
        '秦安县': 'qinan',
        '张家川县': 'zhangjiachuan',
        '张家川回族自治县': 'zhangjiachuan',
        '清水县': 'qingshui',
        '甘谷县': 'gangu',
        '麦积区': 'maiji',
        '武山县': 'wushan',
        '秦州区': 'qinzhou'
    }
    return county_mapping.get(county_name)

def calculate_yearly_increment(table_name: str, year: int) -> int:
    """
    计算指定表在特定年份的新增数据量
    
    参数:
        table_name: 表名
        year: 年份
        
    返回:
        年度新增数据量
    """
    try:
        engine = get_db_connection()
        with engine.connect() as conn:
            query = text("""
                SELECT COALESCE(SUM(record_count), 0) as total 
                FROM `{table_name}` 
                WHERE YEAR(provide_time) = :year
            """)
            result = conn.execute(query, {"year": year})
            return int(next(iter(result), [0])[0] or 0)
    except Exception as e:
        logger.error(f"计算表 {table_name} 年份 {year} 新增数据量出错: {str(e)}")
        return 0

def calculate_city_yearly_increment(year: int) -> int:
    """
    计算全市年度新增数据量
    
    参数:
        year: 年份
        
    返回:
        全市年度新增数据量
    """
    try:
        engine = get_db_connection()
        total_increment = 0
        
        # 遍历所有县区表
        for region in REGIONS_CONFIG:
            try:
                table_name = f"excel_data_{region['db_table_suffix']}"
                increment = calculate_yearly_increment(table_name, year)
                total_increment += increment
            except Exception as e:
                logger.error(f"计算县区 {region['name']} 年度新增数据量时出错: {str(e)}")
                continue
        
        return total_increment
    except Exception as e:
        logger.error(f"计算全市年度新增数据量时出错: {str(e)}")
        return 0

def calculate_county_yearly_increment(county_code: str, year: int) -> int:
    """
    计算县区年度新增数据量
    
    参数:
        county_code: 县区代码
        year: 年份
        
    返回:
        县区年度新增数据量
    """
    try:
        table_name = f"excel_data_{county_code}"
        return calculate_yearly_increment(table_name, year)
    except Exception as e:
        logger.error(f"计算县区 {county_code} 年度新增数据量时出错: {str(e)}")
        return 0

def calculate_yearly_increment_with_population(county_code: str, year: int) -> Dict[str, Any]:
    """
    计算县区年度新增数据量及人均值
    
    参数:
        county_code: 县区代码
        year: 年份
        
    返回:
        包含年度新增数据量和人均值的字典
    """
    try:
        # 获取年度新增数据量
        increment = calculate_county_yearly_increment(county_code, year)
        
        # 获取人口数据
        population = get_county_population(county_code, year)
        
        # 计算人均值
        per_capita = round(float(increment) / float(population), 2) if population > 0 else 0
        
        return {
            'increment': increment,
            'population': population,
            'per_capita': per_capita
        }
    except Exception as e:
        logger.error(f"计算县区 {county_code} 年度新增数据及人均值时出错: {str(e)}")
        return {
            'increment': 0,
            'population': 0,
            'per_capita': 0
        }

def calculate_monthly_stats(table_name: str, year: int) -> Dict[int, int]:
    """
    计算指定表在特定年份的月度数据统计
    
    参数:
        table_name: 表名
        year: 年份
        
    返回:
        字典，键为月份(1-12)，值为该月数据量
    """
    try:
        engine = get_db_connection()
        monthly_data = {month: 0 for month in range(1, 13)}
        
        with engine.connect() as conn:
            query = text("""
                SELECT 
                    MONTH(provide_time) as month, 
                    SUM(record_count) as total 
                FROM `{table_name}` 
                WHERE YEAR(provide_time) = :year
                GROUP BY MONTH(provide_time)
            """)
            result = conn.execute(query, {"year": year})
            
            for row in result:
                month = row[0]
                count = row[1] or 0
                if 1 <= month <= 12:
                    monthly_data[month] = int(count)
                else:
                    logger.warning(f"表 {table_name} 中发现无效月份: {month}")
        
        return monthly_data
    except Exception as e:
        logger.error(f"计算表 {table_name} 年份 {year} 月度数据统计出错: {str(e)}")
        return {month: 0 for month in range(1, 13)}

def calculate_city_monthly_stats(year: int) -> Dict[str, Any]:
    """
    计算全市月度数据统计
    
    参数:
        year: 年份
        
    返回:
        包含月度数据统计的字典
    """
    try:
        engine = get_db_connection()
        monthly_stats = {month: 0 for month in range(1, 13)}
        total_count = 0
        max_month = 1
        max_count = 0
        
        # 遍历所有县区表
        for region in REGIONS_CONFIG:
            try:
                table_name = f"excel_data_{region['db_table_suffix']}"
                region_monthly = calculate_monthly_stats(table_name, year)
                
                # 累加每月数据
                for month, count in region_monthly.items():
                    monthly_stats[month] += count
                    total_count += count
                    
                    # 更新最大值
                    if count > max_count:
                        max_count = count
                        max_month = month
                        
            except Exception as e:
                logger.error(f"计算县区 {region['name']} 月度数据统计时出错: {str(e)}")
                continue
        
        return {
            'monthly_stats': monthly_stats,
            'total_count': total_count,
            'max_month': max_month,
            'max_count': max_count
        }
    except Exception as e:
        logger.error(f"计算全市月度数据统计出错: {str(e)}")
        return {
            'monthly_stats': {month: 0 for month in range(1, 13)},
            'total_count': 0,
            'max_month': 1,
            'max_count': 0
        }

def calculate_period_stats(table_name: str, start_date: str, end_date: str) -> Dict[str, Any]:
    """
    计算指定表在特定时间段的数据统计
    
    参数:
        table_name: 表名
        start_date: 开始日期，格式为 'YYYY-MM-DD'
        end_date: 结束日期，格式为 'YYYY-MM-DD'
        
    返回:
        包含时间段数据统计的字典
    """
    try:
        engine = get_db_connection()
        with engine.connect() as conn:
            query = text("""
                SELECT 
                    COALESCE(SUM(record_count), 0) as total,
                    COUNT(DISTINCT DATE(provide_time)) as days_count,
                    MIN(provide_time) as first_date,
                    MAX(provide_time) as last_date
                FROM `{table_name}` 
                WHERE provide_time BETWEEN :start_date AND :end_date
            """)
            result = conn.execute(query, {
                "start_date": start_date,
                "end_date": end_date
            }).fetchone()
            
            total = int(result[0] or 0)
            days_count = int(result[1] or 0)
            first_date = result[2]
            last_date = result[3]
            
            # 计算日均数据量
            daily_avg = round(total / days_count, 2) if days_count > 0 else 0
            
            return {
                'total': total,
                'days_count': days_count,
                'daily_avg': daily_avg,
                'first_date': first_date,
                'last_date': last_date
            }
    except Exception as e:
        logger.error(f"计算表 {table_name} 时间段 {start_date} 至 {end_date} 数据统计出错: {str(e)}")
        return {
            'total': 0,
            'days_count': 0,
            'daily_avg': 0,
            'first_date': None,
            'last_date': None
        }

def calculate_city_period_stats(start_date: str, end_date: str) -> Dict[str, Any]:
    """
    计算全市在特定时间段的数据统计
    
    参数:
        start_date: 开始日期，格式为 'YYYY-MM-DD'
        end_date: 结束日期，格式为 'YYYY-MM-DD'
        
    返回:
        包含全市时间段数据统计的字典
    """
    try:
        engine = get_db_connection()
        total_stats = {
            'total': 0,
            'days_count': 0,
            'daily_avg': 0,
            'first_date': None,
            'last_date': None,
            'county_stats': {}
        }
        
        # 遍历所有县区表
        for region in REGIONS_CONFIG:
            try:
                table_name = f"excel_data_{region['db_table_suffix']}"
                region_stats = calculate_period_stats(table_name, start_date, end_date)
                
                # 累加总数据量
                total_stats['total'] += region_stats['total']
                
                # 更新日期范围
                if region_stats['first_date']:
                    if not total_stats['first_date'] or region_stats['first_date'] < total_stats['first_date']:
                        total_stats['first_date'] = region_stats['first_date']
                if region_stats['last_date']:
                    if not total_stats['last_date'] or region_stats['last_date'] > total_stats['last_date']:
                        total_stats['last_date'] = region_stats['last_date']
                
                # 保存县区统计
                total_stats['county_stats'][region['name']] = region_stats
                
            except Exception as e:
                logger.error(f"计算县区 {region['name']} 时间段数据统计时出错: {str(e)}")
                continue
        
        # 计算总天数（取所有县区中最大的天数）
        total_stats['days_count'] = max(
            (stats['days_count'] for stats in total_stats['county_stats'].values()),
            default=0
        )
        
        # 计算总日均数据量
        total_stats['daily_avg'] = round(
            total_stats['total'] / total_stats['days_count'], 2
        ) if total_stats['days_count'] > 0 else 0
        
        return total_stats
    except Exception as e:
        logger.error(f"计算全市时间段数据统计出错: {str(e)}")
        return {
            'total': 0,
            'days_count': 0,
            'daily_avg': 0,
            'first_date': None,
            'last_date': None,
            'county_stats': {}
        } 