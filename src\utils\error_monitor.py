#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
错误监控和报告模块
提供错误统计、监控、报告功能
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from collections import defaultdict, Counter
from dataclasses import dataclass, asdict
import threading
import time

@dataclass
class ErrorStats:
    """错误统计数据"""
    total_errors: int = 0
    errors_by_category: Dict[str, int] = None
    errors_by_level: Dict[str, int] = None
    errors_by_hour: Dict[str, int] = None
    top_error_messages: List[Dict[str, Any]] = None
    last_updated: str = None
    
    def __post_init__(self):
        if self.errors_by_category is None:
            self.errors_by_category = {}
        if self.errors_by_level is None:
            self.errors_by_level = {}
        if self.errors_by_hour is None:
            self.errors_by_hour = {}
        if self.top_error_messages is None:
            self.top_error_messages = []
        if self.last_updated is None:
            self.last_updated = datetime.now().isoformat()

class ErrorMonitor:
    """错误监控器"""
    
    def __init__(self, storage_path: str = "logs/error_monitor.json"):
        self.storage_path = storage_path
        self.errors_buffer = []
        self.stats = ErrorStats()
        self.lock = threading.Lock()
        self._ensure_storage_dir()
        self._load_existing_data()
    
    def _ensure_storage_dir(self):
        """确保存储目录存在"""
        os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
    
    def _load_existing_data(self):
        """加载现有数据"""
        try:
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.stats = ErrorStats(**data.get('stats', {}))
                    self.errors_buffer = data.get('recent_errors', [])
        except Exception as e:
            print(f"加载错误监控数据失败: {e}")
    
    def _save_data(self):
        """保存数据到文件"""
        try:
            data = {
                'stats': asdict(self.stats),
                'recent_errors': self.errors_buffer[-1000:]  # 只保留最近1000条错误
            }
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存错误监控数据失败: {e}")
    
    def record_error(self, error_data: Dict[str, Any]):
        """记录错误"""
        with self.lock:
            # 添加到缓冲区
            error_record = {
                'timestamp': datetime.now().isoformat(),
                'error_code': error_data.get('error_code', 'UNKNOWN'),
                'message': error_data.get('message', ''),
                'category': error_data.get('category', 'system'),
                'level': error_data.get('level', 'medium'),
                'details': error_data.get('details', {})
            }
            self.errors_buffer.append(error_record)
            
            # 更新统计
            self._update_stats(error_record)
            
            # 定期保存数据
            if len(self.errors_buffer) % 10 == 0:
                self._save_data()
    
    def _update_stats(self, error_record: Dict[str, Any]):
        """更新统计数据"""
        self.stats.total_errors += 1
        
        # 按分类统计
        category = error_record.get('category', 'unknown')
        self.stats.errors_by_category[category] = self.stats.errors_by_category.get(category, 0) + 1
        
        # 按级别统计
        level = error_record.get('level', 'unknown')
        self.stats.errors_by_level[level] = self.stats.errors_by_level.get(level, 0) + 1
        
        # 按小时统计
        hour = datetime.now().strftime('%Y-%m-%d %H:00')
        self.stats.errors_by_hour[hour] = self.stats.errors_by_hour.get(hour, 0) + 1
        
        # 更新时间
        self.stats.last_updated = datetime.now().isoformat()
    
    def get_stats(self, hours: int = 24) -> Dict[str, Any]:
        """获取统计数据"""
        with self.lock:
            # 计算时间范围内的错误
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_errors = [
                error for error in self.errors_buffer
                if datetime.fromisoformat(error['timestamp']) > cutoff_time
            ]
            
            # 生成统计报告
            stats = {
                'total_errors': len(recent_errors),
                'time_range_hours': hours,
                'errors_by_category': Counter(error['category'] for error in recent_errors),
                'errors_by_level': Counter(error['level'] for error in recent_errors),
                'top_error_messages': self._get_top_error_messages(recent_errors),
                'error_trend': self._get_error_trend(recent_errors),
                'last_updated': datetime.now().isoformat()
            }
            
            return stats
    
    def _get_top_error_messages(self, errors: List[Dict[str, Any]], limit: int = 10) -> List[Dict[str, Any]]:
        """获取最常见的错误消息"""
        message_counter = Counter(error['message'] for error in errors)
        return [
            {'message': message, 'count': count}
            for message, count in message_counter.most_common(limit)
        ]
    
    def _get_error_trend(self, errors: List[Dict[str, Any]]) -> Dict[str, int]:
        """获取错误趋势（按小时）"""
        trend = defaultdict(int)
        for error in errors:
            hour = datetime.fromisoformat(error['timestamp']).strftime('%H:00')
            trend[hour] += 1
        return dict(trend)
    
    def get_error_details(self, error_code: str = None, 
                         category: str = None, 
                         limit: int = 100) -> List[Dict[str, Any]]:
        """获取错误详情"""
        with self.lock:
            filtered_errors = self.errors_buffer
            
            if error_code:
                filtered_errors = [e for e in filtered_errors if e.get('error_code') == error_code]
            
            if category:
                filtered_errors = [e for e in filtered_errors if e.get('category') == category]
            
            return filtered_errors[-limit:]
    
    def clear_old_errors(self, days: int = 7):
        """清理旧错误记录"""
        with self.lock:
            cutoff_time = datetime.now() - timedelta(days=days)
            self.errors_buffer = [
                error for error in self.errors_buffer
                if datetime.fromisoformat(error['timestamp']) > cutoff_time
            ]
            self._save_data()
    
    def generate_report(self, hours: int = 24) -> str:
        """生成错误报告"""
        stats = self.get_stats(hours)
        
        report = f"""
错误监控报告 ({hours}小时内)
{'='*50}
总错误数: {stats['total_errors']}
最后更新: {stats['last_updated']}

按分类统计:
{'-'*20}
"""
        for category, count in stats['errors_by_category'].items():
            report += f"{category}: {count}\n"
        
        report += f"""
按级别统计:
{'-'*20}
"""
        for level, count in stats['errors_by_level'].items():
            report += f"{level}: {count}\n"
        
        report += f"""
最常见错误:
{'-'*20}
"""
        for error in stats['top_error_messages'][:5]:
            report += f"{error['message'][:50]}... ({error['count']}次)\n"
        
        return report

# 全局错误监控器实例
error_monitor = ErrorMonitor()

def monitor_error(error_data: Dict[str, Any]):
    """记录错误到监控器"""
    error_monitor.record_error(error_data)

def get_error_stats(hours: int = 24) -> Dict[str, Any]:
    """获取错误统计"""
    return error_monitor.get_stats(hours)

def get_error_report(hours: int = 24) -> str:
    """获取错误报告"""
    return error_monitor.generate_report(hours)
