"""
服务器管理系统路由模块
处理服务器信息和服务端口信息的CRUD操作
"""

from flask import Blueprint, jsonify, request, render_template
from sqlalchemy import text
import traceback
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

# 创建蓝图
server_bp = Blueprint('server', __name__)

# 数据库连接函数，避免循环导入
def get_connection():
    from src.utils.db_manager import DatabaseManager
    return DatabaseManager.get_main_db()

@server_bp.route('/server_management')
def server_management():
    """服务器管理页面"""
    try:
        # 获取数据库连接
        engine = get_connection()
        
        with engine.connect() as conn:
            # 获取所有平台信息
            platform_query = text("""
                SELECT DISTINCT platform 
                FROM server_port_metadata 
                WHERE platform IS NOT NULL
            """)
            platforms = [row[0] for row in conn.execute(platform_query).fetchall()]
            
            # 获取所有服务器信息
            server_query = text("""
                SELECT ip, hostname, platform, cpu, memory, disk, os_system, deploy_components
                FROM server_port_metadata
            """)
            server_result = conn.execute(server_query).fetchall()
            
            server_info = []
            for row in server_result:
                server_info.append({
                    'ip': row[0],
                    'hostname': row[1],
                    'platform': row[2],
                    'cpu': row[3],
                    'memory': row[4],
                    'disk': row[5],
                    'os_system': row[6],
                    'deploy_components': row[7]
                })
            
            # 获取所有端口信息
            port_query = text("""
                SELECT ip, deploy_component, app_port, dubbo_server_port, xxl_job_port
                FROM server_information_metadata
                WHERE deploy_component IS NOT NULL
            """)
            port_result = conn.execute(port_query)
            
            server_ports = []
            for row in port_result:
                server_ports.append({
                    'ip': row.ip,
                    'deploy_component': row.deploy_component,
                    'app_port': str(row.app_port) if row.app_port is not None else None,
                    'dubbo_server_port': str(row.dubbo_server_port) if row.dubbo_server_port is not None else None,
                    'xxl_job_port': str(row.xxl_job_port) if row.xxl_job_port is not None else None
                })
            
            return render_template('server_management.html', 
                                platforms=platforms,
                                server_info=server_info,
                                server_ports=server_ports)
                                
    except Exception as e:
        print(f"Error in server_management: {str(e)}")
        return str(e), 500

@server_bp.route('/get_server_info')
def get_server_info():
    """获取服务器信息"""
    try:
        # 获取数据库连接
        engine = get_connection()
        
        with engine.connect() as conn:
            server_query = text("""
                SELECT ip, hostname, platform, cpu, memory, disk, os_system, deploy_components
                FROM server_port_metadata
            """)
            server_result = conn.execute(server_query).fetchall()
            
            server_info = []
            for row in server_result:
                server_info.append({
                    'ip': row[0],
                    'hostname': row[1],
                    'platform': row[2],
                    'cpu': row[3],
                    'memory': row[4],
                    'disk': row[5],
                    'os_system': row[6],
                    'deploy_components': row[7]
                })
            
            return jsonify({'server_info': server_info})
    
    except Exception as e:
        print(f"Error in get_server_info: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@server_bp.route('/get_server_ports', methods=['GET'])
def get_server_ports():
    """获取所有服务器的端口信息"""
    try:
        # 获取数据库连接
        engine = get_connection()
        
        with engine.connect() as conn:
            query = text("""
                SELECT ip, deploy_component, app_port, dubbo_server_port, xxl_job_port
                FROM server_information_metadata
                WHERE deploy_component IS NOT NULL
            """)
            
            result = conn.execute(query)
            server_ports = []
            
            for row in result:
                server_ports.append({
                    'ip': row.ip,
                    'deploy_component': row.deploy_component,
                    'app_port': str(row.app_port) if row.app_port is not None else None,
                    'dubbo_server_port': str(row.dubbo_server_port) if row.dubbo_server_port is not None else None,
                    'xxl_job_port': str(row.xxl_job_port) if row.xxl_job_port is not None else None
                })
            
            return jsonify({
                'success': True,
                'server_ports': server_ports
            })
            
    except Exception as e:
        print(f"Error in get_server_ports: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e)
        }), 500

@server_bp.route('/api/platforms')
def get_platforms():
    """获取平台列表"""
    try:
        # 获取数据库连接
        engine = get_connection()
        
        with engine.connect() as conn:
            # 查询平台列表
            platform_query = text("""
                SELECT DISTINCT platform FROM server_port_metadata
            """)
            platform_result = conn.execute(platform_query).fetchall()
            
            platforms = [row[0] for row in platform_result]
            
            return jsonify({
                'success': True,
                'data': platforms
            })
    
    except Exception as e:
        print(f"Error in get_platforms: {str(e)}")
        return jsonify({
            'success': False,
            'message': str(e)
        })

@server_bp.route('/api/server', methods=['POST'])
def add_server():
    """添加服务器信息"""
    try:
        data = request.json
        
        # 验证必填字段
        required_fields = ['ip', 'hostname', 'platform', 'cpu', 'memory', 'disk', 'os_system']
        for field in required_fields:
            if field not in data or not data[field]:
                return jsonify({'success': False, 'error': f'缺少必填字段: {field}'}), 400
        
        # 获取数据库连接
        engine = get_connection()
        
        with engine.connect() as conn:
            # 检查IP是否已存在
            check_query = text("""
                SELECT COUNT(*) FROM server_port_metadata WHERE ip = :ip
            """)
            result = conn.execute(check_query, {'ip': data['ip']}).fetchone()
            
            if result[0] > 0:
                return jsonify({'success': False, 'error': f'IP {data["ip"]} 已存在'}), 400
            
            # 插入数据
            insert_query = text("""
                INSERT INTO server_port_metadata (
                    ip, hostname, platform, cpu, memory, disk, os_system
                ) VALUES (
                    :ip, :hostname, :platform, :cpu, :memory, :disk, :os_system
                )
            """)
            
            conn.execute(insert_query, {
                'ip': data['ip'],
                'hostname': data['hostname'],
                'platform': data['platform'],
                'cpu': data['cpu'],
                'memory': data['memory'],
                'disk': data['disk'],
                'os_system': data['os_system']
            })
            
            conn.commit()
            
            return jsonify({'success': True, 'message': '添加成功'})
    
    except Exception as e:
        print(f"Error in add_server: {str(e)}")
        return jsonify({'success': False, 'error': str(e)}), 500

@server_bp.route('/api/server', methods=['DELETE'])
def delete_server():
    try:
        data = request.get_json()
        ip = data.get('ip')

        if not ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'}), 400

        # 连接数据库
        engine = get_connection()
        
        with engine.connect() as conn:
            # 删除服务器信息
            delete_query = text("""
                DELETE FROM server_port_metadata
                WHERE ip = :ip
            """)
            
            conn.execute(delete_query, {'ip': ip})
            conn.commit()

            return jsonify({'success': True})
    except Exception as e:
        print(f"Error deleting server: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@server_bp.route('/api/server', methods=['PUT'])
def update_server():
    try:
        data = request.get_json()
        ip = data.get('ip')
        hostname = data.get('hostname')
        cpu = data.get('cpu')
        memory = data.get('memory')
        disk = data.get('disk')
        os_system = data.get('os_system')
        deploy_components = data.get('deploy_components')

        if not ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'}), 400

        # 连接数据库
        engine = get_connection()
        
        with engine.connect() as conn:
            # 更新服务器信息
            update_query = text("""
                UPDATE server_port_metadata 
                SET hostname = :hostname,
                    cpu = :cpu,
                    memory = :memory,
                    disk = :disk,
                    os_system = :os_system,
                    deploy_components = :deploy_components
                WHERE ip = :ip
            """)
            
            conn.execute(update_query, {
                'ip': ip,
                'hostname': hostname,
                'cpu': cpu,
                'memory': memory,
                'disk': disk,
                'os_system': os_system,
                'deploy_components': deploy_components
            })
            conn.commit()

            return jsonify({'success': True})
    except Exception as e:
        print(f"Error updating server: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@server_bp.route('/api/port', methods=['PUT'])
def update_port():
    try:
        data = request.get_json()
        
        # 检查必要字段
        if not data.get('ip'):
            return jsonify({'success': False, 'message': '缺少IP地址'}), 400
        
        # 处理空值，但保持字符串类型
        for key in ['app_port', 'dubbo_server_port', 'xxl_job_port']:
            if data.get(key) == 'null' or data.get(key) == '' or data.get(key) is None:
                data[key] = None
            elif data.get(key) is not None:
                # 确保是字符串类型
                data[key] = str(data[key]).strip()

        update_query = text("""
                UPDATE server_information_metadata
                SET deploy_component = :deploy_component,
                    app_port = :app_port,
                    dubbo_server_port = :dubbo_server_port,
                    xxl_job_port = :xxl_job_port
                WHERE ip = :ip
            """)
        
        engine = get_connection()
        
        with engine.connect() as conn:
            result = conn.execute(update_query, {
                'deploy_component': data.get('deploy_component'),
                'app_port': data.get('app_port'),
                'dubbo_server_port': data.get('dubbo_server_port'),
                'xxl_job_port': data.get('xxl_job_port'),
                'ip': data.get('ip')
            })
            conn.commit()
            
            if result.rowcount == 0:
                return jsonify({'success': False, 'message': '未找到要更新的记录'}), 404
            
        return jsonify({'success': True, 'message': '更新成功'})
    except Exception as e:
        print(f"Error updating port: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@server_bp.route('/api/port', methods=['DELETE'])
def delete_port():
    try:
        data = request.get_json()
        ip = data.get('ip')
        
        if not ip:
            return jsonify({'success': False, 'message': '缺少IP地址'}), 400
            
        update_query = text("""
            UPDATE server_information_metadata
            SET deploy_component = NULL,
                app_port = NULL,
                dubbo_server_port = NULL,
                xxl_job_port = NULL
            WHERE ip = :ip
        """)
        
        engine = get_connection()
        
        with engine.connect() as conn:
            conn.execute(update_query, {'ip': ip})
            conn.commit()
            
        return jsonify({'success': True, 'message': '删除成功'})
    except Exception as e:
        print(f"Error deleting port: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@server_bp.route('/api/server/list', methods=['GET'])
def get_servers():
    try:
        # 连接数据库
        engine = get_connection()
        
        with engine.connect() as conn:
            # 查询所有服务器信息
            server_query = text("""
                SELECT ip, hostname, platform, cpu, memory, disk, os_system, deploy_components
                FROM server_port_metadata
                ORDER BY ip
            """)
            servers = conn.execute(server_query).fetchall()
            
            # 将查询结果转换为字典列表
            server_list = []
            for server in servers:
                server_list.append({
                    'ip': server[0],
                    'hostname': server[1],
                    'platform': server[2],
                    'cpu': server[3],
                    'memory': server[4],
                    'disk': server[5],
                    'os_system': server[6],
                    'deploy_components': server[7]
                })
            
            return jsonify({'success': True, 'servers': server_list})
    except Exception as e:
        print(f"Error getting servers: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@server_bp.route('/api/server/usage', methods=['GET'])
def get_server_usage():
    """获取服务器利用率数据"""
    try:
        # 获取请求参数
        ip = request.args.get('ip')
        if not ip:
            return jsonify({'success': False, 'message': '缺少IP参数'}), 400
        
        # 连接数据库
        engine = get_connection()
        
        with engine.connect() as conn:
            # 查询服务器利用率数据
            usage_query = text("""
                SELECT cpu_usage, mem_usage, disk_usage, timestamp
                FROM server_stats_metadata
                WHERE ip = :ip
                ORDER BY timestamp DESC
                LIMIT 1
            """)
            usage = conn.execute(usage_query, {'ip': ip}).fetchone()
            
            if not usage:
                return jsonify({'success': False, 'message': '未找到服务器利用率数据'}), 404
            
            # 将查询结果转换为字典
            usage_data = {
                'cpu_usage': float(usage[0]) if usage[0] is not None else 0,
                'mem_usage': float(usage[1]) if usage[1] is not None else 0,
                'disk_usage': float(usage[2]) if usage[2] is not None else 0,
                'timestamp': usage[3].isoformat() if usage[3] else None
            }
            
            return jsonify({'success': True, 'data': usage_data})
    except Exception as e:
        print(f"Error getting server usage: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@server_bp.route('/api/server/directory', methods=['GET'])
def get_server_directory():
    """获取服务器目录树数据"""
    try:
        # 获取请求参数
        ip = request.args.get('ip')
        if not ip:
            return jsonify({'success': False, 'message': '缺少IP参数'}), 400
        
        # 连接数据库
        engine = get_connection()
        
        with engine.connect() as conn:
            # 查询服务器目录树数据
            directory_query = text("""
                SELECT tree_json
                FROM directory_tree_json_metadata
                WHERE ip = :ip
            """)
            directory = conn.execute(directory_query, {'ip': ip}).fetchone()
            
            if not directory:
                return jsonify({'success': False, 'message': '未找到服务器目录树数据'}), 404
            
            # 获取JSON数据
            directory_data = directory[0]
            
            # 检查数据类型
            import json
            if isinstance(directory_data, str):
                try:
                    directory_data = json.loads(directory_data)
                except Exception as e:
                    pass
            
            return jsonify({'success': True, 'data': directory_data})
    except Exception as e:
        print(f"Error getting server directory: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@server_bp.route('/api/server/search', methods=['GET'])
def search_servers():
    """搜索服务器信息"""
    try:
        # 获取搜索关键字
        query = request.args.get('query', '')
        
        if not query:
            return jsonify({'success': False, 'message': '搜索关键字不能为空'}), 400
        
        # 获取数据库连接
        engine = get_connection()
        
        with engine.connect() as conn:
            # 使用LIKE进行模糊搜索
            search_query = text("""
                SELECT ip, hostname, platform, cpu, memory, disk, os_system, deploy_components
                FROM server_port_metadata
                WHERE ip LIKE :query
                ORDER BY ip
            """)
            
            # 添加通配符进行模糊匹配
            search_param = f"%{query}%"
            servers = conn.execute(search_query, {"query": search_param}).fetchall()
            
            # 将查询结果转换为字典列表
            server_list = []
            for server in servers:
                server_list.append({
                    'ip': server[0],
                    'hostname': server[1],
                    'platform': server[2],
                    'cpu': server[3],
                    'memory': server[4],
                    'disk': server[5],
                    'os_system': server[6],
                    'deploy_components': server[7]
                })
            
            return jsonify({'success': True, 'servers': server_list})
    except Exception as e:
        print(f"Error searching servers: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@server_bp.route('/api/port/search', methods=['GET'])
def search_port():
    try:
        query = request.args.get('query', '')
        
        if not query:
            return jsonify({'success': False, 'message': '搜索关键词不能为空'}), 400
        
        # 获取数据库连接
        engine = get_connection()
        
        with engine.connect() as conn:
            # 构建模糊查询
            search_query = text("""
                SELECT ip, deploy_component, app_port, dubbo_server_port, xxl_job_port
                FROM server_information_metadata
                WHERE ip LIKE :query
                   OR deploy_component LIKE :query
                   OR app_port LIKE :query
                   OR dubbo_server_port LIKE :query
                   OR xxl_job_port LIKE :query
            """)
            
            search_result = conn.execute(search_query, {'query': f'%{query}%'}).fetchall()
            
            ports = []
            for row in search_result:
                ports.append({
                    'ip': row[0],
                    'deploy_component': row[1],
                    'app_port': row[2],
                    'dubbo_server_port': row[3],
                    'xxl_job_port': row[4]
                })
            
            return jsonify({'success': True, 'ports': ports})
    
    except Exception as e:
        print(f"Error searching ports: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@server_bp.route('/api/server/detail', methods=['GET'])
def get_server_detail():
    try:
        ip = request.args.get('ip')
        
        if not ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'}), 400
        
        # 获取数据库连接
        engine = get_connection()
        
        with engine.connect() as conn:
            server_query = text("""
                SELECT ip, hostname, platform, cpu, memory, disk, os_system, deploy_components
                FROM server_port_metadata
                WHERE ip = :ip
            """)
            
            server_result = conn.execute(server_query, {'ip': ip}).fetchone()
            
            if not server_result:
                return jsonify({'success': False, 'message': '未找到该服务器'}), 404
            
            server = {
                'ip': server_result[0],
                'hostname': server_result[1],
                'platform': server_result[2],
                'cpu': server_result[3],
                'memory': server_result[4],
                'disk': server_result[5],
                'os_system': server_result[6],
                'deploy_components': server_result[7]
            }
            
            return jsonify({'success': True, 'server': server})
    
    except Exception as e:
        print(f"Error getting server detail: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500

@server_bp.route('/api/port/detail', methods=['GET'])
def get_port_detail():
    try:
        ip = request.args.get('ip')
        
        if not ip:
            return jsonify({'success': False, 'message': 'IP地址不能为空'}), 400
        
        # 获取数据库连接
        engine = get_connection()
        
        with engine.connect() as conn:
            port_query = text("""
                SELECT ip, deploy_component, app_port, dubbo_server_port, xxl_job_port
                FROM server_information_metadata
                WHERE ip = :ip
            """)
            
            port_result = conn.execute(port_query, {'ip': ip}).fetchone()
            
            if not port_result:
                return jsonify({'success': False, 'message': '未找到该端口信息'}), 404
            
            port = {
                'ip': port_result[0],
                'deploy_component': port_result[1],
                'app_port': port_result[2],
                'dubbo_server_port': port_result[3],
                'xxl_job_port': port_result[4]
            }
            
            return jsonify({'success': True, 'port': port})
    
    except Exception as e:
        print(f"Error getting port detail: {str(e)}")
        return jsonify({'success': False, 'message': str(e)}), 500
