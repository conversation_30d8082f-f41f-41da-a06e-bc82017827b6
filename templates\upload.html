<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel数据导入</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f7fa;
            color: #333;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
        }
        h1 {
            color: #2c3e50;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .card {
            background-color: #fff;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 15px;
            box-shadow: 0 1px 5px rgba(0,0,0,0.05);
        }
        .file-dropzone {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            background-color: #fafbfc;
            cursor: pointer;
            transition: all 0.3s;
        }
        .file-dropzone:hover, .file-dropzone.dragover {
            border-color: #4c84ff;
            background-color: #f0f5ff;
        }
        #file-input {
            display: none;
        }
        .file-info {
            margin-bottom: 15px;
        }
        .btn {
            background-color: #4c84ff;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #3a75e0;
        }
        .btn:disabled {
            background-color: #b0c2e8;
            cursor: not-allowed;
        }
        .log-container {
            background-color: #1e1e1e;
            color: #dcdcdc;
            padding: 15px;
            border-radius: 4px;
            font-family: Consolas, Monaco, 'Courier New', monospace;
            white-space: pre-wrap;
            height: 400px;
            overflow-y: auto;
            margin-top: 20px;
        }
        .log-container:empty::before {
            content: "等待处理日志...";
            color: #777;
            font-style: italic;
        }
        .status {
            margin-top: 15px;
            font-weight: bold;
        }
        .status.success {
            color: #27ae60;
        }
        .status.error {
            color: #e74c3c;
        }
        .status.processing {
            color: #3498db;
        }
        .hidden {
            display: none;
        }
        .task-list {
            list-style-type: none;
            padding: 0;
        }
        .task-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .task-name {
            flex-grow: 1;
        }
        .task-status {
            padding: 3px 8px;
            border-radius: 4px;
            font-size: 12px;
            margin-left: 10px;
        }
        .task-status.completed {
            background-color: #e6f7e6;
            color: #27ae60;
        }
        .task-status.running {
            background-color: #e6f0f7;
            color: #3498db;
        }
        .task-status.failed {
            background-color: #fbeaea;
            color: #e74c3c;
        }
        .task-status.pending {
            background-color: #fcf8e3;
            color: #f39c12;
        }
        .timestamp {
            color: #7f8c8d;
            font-size: 12px;
            margin-left: 10px;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            margin-right: 10px;
        }
        .tab.active {
            border-bottom: 2px solid #4c84ff;
            color: #4c84ff;
            font-weight: bold;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .summary {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .summary h3 {
            margin-top: 0;
        }
        .summary ul {
            list-style-type: none;
            padding: 0;
        }
        .summary li {
            padding: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Excel 数据导入</h1>
        
        <div class="tabs">
            <div class="tab active" data-tab="upload">上传文件</div>
            <div class="tab" data-tab="history">历史记录</div>
        </div>
        
        <div id="upload-tab" class="tab-content active">
            <div class="card">
                <div id="file-dropzone" class="file-dropzone">
                    <p>拖拽Excel文件到此处或点击选择文件</p>
                    <input type="file" id="file-input" accept=".xlsx, .xls">
                </div>
                
                <div id="file-info" class="file-info hidden">
                    <p>已选择文件：<span id="file-name"></span></p>
                </div>
                
                <button id="upload-btn" class="btn" disabled>上传并处理</button>
            </div>
            
            <div id="processing-section" class="hidden">
                <div class="status processing">正在处理，请勿关闭此页面...</div>
                <div id="log-container" class="log-container"></div>
                
                <div id="summary-section" class="summary hidden">
                    <h3>处理完成</h3>
                    <ul id="summary-list">
                        <!-- 处理结果将显示在这里 -->
                    </ul>
                </div>
            </div>
        </div>
        
        <div id="history-tab" class="tab-content">
            <div class="card">
                <h2>任务历史</h2>
                <ul id="task-list" class="task-list">
                    <!-- 任务历史将显示在这里 -->
                </ul>
            </div>
        </div>
    </div>
    
    <script>
        // DOM元素
        const fileDropzone = document.getElementById('file-dropzone');
        const fileInput = document.getElementById('file-input');
        const fileInfo = document.getElementById('file-info');
        const fileName = document.getElementById('file-name');
        const uploadBtn = document.getElementById('upload-btn');
        const processingSection = document.getElementById('processing-section');
        const logContainer = document.getElementById('log-container');
        const summarySection = document.getElementById('summary-section');
        const summaryList = document.getElementById('summary-list');
        const taskList = document.getElementById('task-list');
        const tabs = document.querySelectorAll('.tab');
        const tabContents = document.querySelectorAll('.tab-content');
        
        // 当前任务ID
        let currentTaskId = null;
        let eventSource = null;
        let logOffset = 0;
        
        // 初始化
        window.addEventListener('load', () => {
            loadTasks();
        });
        
        // 标签页切换
        tabs.forEach(tab => {
            tab.addEventListener('click', () => {
                const tabId = tab.getAttribute('data-tab');
                
                // 激活当前标签
                tabs.forEach(t => t.classList.remove('active'));
                tab.classList.add('active');
                
                // 显示当前内容
                tabContents.forEach(content => {
                    content.classList.remove('active');
                    if (content.id === `${tabId}-tab`) {
                        content.classList.add('active');
                    }
                });
                
                // 如果切换到历史标签，刷新任务列表
                if (tabId === 'history') {
                    loadTasks();
                }
            });
        });
        
        // 文件拖放区域事件
        fileDropzone.addEventListener('click', () => {
            fileInput.click();
        });
        
        fileDropzone.addEventListener('dragover', (e) => {
            e.preventDefault();
            fileDropzone.classList.add('dragover');
        });
        
        fileDropzone.addEventListener('dragleave', () => {
            fileDropzone.classList.remove('dragover');
        });
        
        fileDropzone.addEventListener('drop', (e) => {
            e.preventDefault();
            fileDropzone.classList.remove('dragover');
            
            if (e.dataTransfer.files.length) {
                handleFileSelect(e.dataTransfer.files[0]);
            }
        });
        
        fileInput.addEventListener('change', () => {
            if (fileInput.files.length) {
                handleFileSelect(fileInput.files[0]);
            }
        });
        
        // 文件选择处理
        function handleFileSelect(file) {
            if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
                alert('请选择Excel文件（.xlsx 或 .xls）');
                return;
            }
            
            fileName.textContent = file.name;
            fileInfo.classList.remove('hidden');
            uploadBtn.disabled = false;
        }
        
        // 上传按钮点击事件
        uploadBtn.addEventListener('click', () => {
            if (!fileInput.files.length) return;
            
            const file = fileInput.files[0];
            const formData = new FormData();
            formData.append('file', file);
            
            // 禁用上传按钮
            uploadBtn.disabled = true;
            uploadBtn.textContent = '正在上传...';
            
            // 显示处理区域
            processingSection.classList.remove('hidden');
            logContainer.textContent = '正在上传文件...\n';
            summarySection.classList.add('hidden');
            
            // 发送上传请求
            fetch('/county/data/data_import/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentTaskId = data.task_id;
                    logContainer.textContent += '文件上传成功，正在处理...\n';
                    startLogStream(currentTaskId);
                } else {
                    logContainer.textContent += `上传失败：${data.message}\n`;
                    uploadBtn.disabled = false;
                    uploadBtn.textContent = '上传并处理';
                }
            })
            .catch(error => {
                logContainer.textContent += `上传出错：${error.message}\n`;
                uploadBtn.disabled = false;
                uploadBtn.textContent = '上传并处理';
            });
        });
        
        // 开始日志流
        function startLogStream(taskId) {
            // 关闭现有的事件源
            if (eventSource) {
                eventSource.close();
            }
            
            // 使用轮询方式获取日志更新
            logOffset = 0;
            pollTaskLogs(taskId);
        }
        
        // 轮询任务日志
        function pollTaskLogs(taskId) {
            fetch('/county/data/data_import/logs/' + taskId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // 更新日志显示
                        logContainer.textContent = data.logs;
                        logContainer.scrollTop = logContainer.scrollHeight;
                        
                        // 如果任务仍在运行，继续轮询
                        if (data.status === 'running' || data.status === 'pending') {
                            setTimeout(() => pollTaskLogs(taskId), 1000);
                        } else {
                            // 任务已完成或失败，获取最终状态
                            fetchTaskStatus(taskId);
                        }
                    }
                })
                .catch(error => {
                    logContainer.textContent += `\n获取日志失败：${error.message}`;
                    setTimeout(() => pollTaskLogs(taskId), 2000);
                });
        }
        
        // 获取任务状态
        function fetchTaskStatus(taskId) {
            fetch('/county/data/data_import/status/' + taskId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const task = data.task;
                        
                        // 更新UI状态
                        uploadBtn.textContent = '上传并处理';
                        uploadBtn.disabled = false;
                        
                        // 如果任务完成，显示摘要
                        if (task.status === 'completed' && task.result) {
                            showTaskSummary(task.result);
                        } else if (task.status === 'failed') {
                            logContainer.textContent += `\n处理失败：${task.error || '未知错误'}`;
                        }
                        
                        // 刷新任务列表
                        loadTasks();
                    }
                })
                .catch(error => {
                    console.error('获取任务状态失败', error);
                });
        }
        
        // 显示任务摘要
        function showTaskSummary(result) {
            summarySection.classList.remove('hidden');
            summaryList.innerHTML = `
                <li>总工作表数：${result.total_sheets}</li>
                <li>成功处理：${result.success_count} 个表</li>
                <li>处理失败：${result.error_count} 个表</li>
                <li>总数据条数：${result.total_records} 条</li>
                <li>总耗时：${result.duration} 秒</li>
            `;
        }
        
        // 加载任务列表
        function loadTasks() {
            fetch('/county/data/data_import/list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        taskList.innerHTML = '';
                        
                        if (data.tasks.length === 0) {
                            taskList.innerHTML = '<li class="task-item">暂无任务记录</li>';
                            return;
                        }
                        
                        data.tasks.forEach(task => {
                            const li = document.createElement('li');
                            li.className = 'task-item';
                            
                            const statusClass = {
                                pending: 'pending',
                                running: 'running',
                                completed: 'completed',
                                failed: 'failed'
                            }[task.status] || '';
                            
                            li.innerHTML = `
                                <div class="task-name">${task.file_name}</div>
                                <span class="timestamp">${task.start_time}</span>
                                <span class="task-status ${statusClass}">${getStatusText(task.status)}</span>
                            `;
                            
                            li.addEventListener('click', () => {
                                viewTaskDetails(task.id);
                            });
                            
                            taskList.appendChild(li);
                        });
                    }
                })
                .catch(error => {
                    console.error('加载任务列表失败', error);
                    taskList.innerHTML = '<li class="task-item">加载任务列表失败</li>';
                });
        }
        
        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                pending: '等待中',
                running: '处理中',
                completed: '已完成',
                failed: '失败'
            };
            return statusMap[status] || status;
        }
        
        // 查看任务详情
        function viewTaskDetails(taskId) {
            // 切换到上传标签页
            tabs[0].click();
            
            // 显示处理部分
            processingSection.classList.remove('hidden');
            summarySection.classList.add('hidden');
            
            // 获取任务日志
            currentTaskId = taskId;
            logContainer.textContent = '正在加载日志...';
            
            fetch('/county/data/data_import/logs/' + taskId)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        logContainer.textContent = data.logs;
                        logContainer.scrollTop = logContainer.scrollHeight;
                        
                        // 获取任务状态
                        fetchTaskStatus(taskId);
                    } else {
                        logContainer.textContent = '获取日志失败';
                    }
                })
                .catch(error => {
                    logContainer.textContent = `获取日志失败：${error.message}`;
                });
        }
    </script>
</body>
</html> 