<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>库表挂接率 - 数据管理系统</title>
    <!-- 本地Bootstrap CSS -->
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="/static/css/all.min.css">
    <style>
        :root {
            --card-bg-color: rgba(255, 255, 255, 0.8);
            --card-hover-bg-color: rgba(255, 255, 255, 0.9);
            --card-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
            --card-hover-shadow: 0 8px 24px rgba(0, 0, 0, 0.16);
            --card-radius: 12px;
            --card-padding: 20px;
            --accent-color: #007AFF;
            --grid-gap: 24px;
            --page-padding: 32px;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
            background-image: 
                radial-gradient(circle at 85% 15%, rgba(0, 122, 255, 0.08) 0%, transparent 25%),
                radial-gradient(circle at 15% 85%, rgba(88, 86, 214, 0.08) 0%, transparent 30%);
            min-height: 100vh;
            padding: var(--page-padding);
            color: #1d1d1f;
        }
        
        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            padding-top: 20px;
        }
        
        .blur-bg {
            position: absolute;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));
            filter: blur(80px);
            z-index: -1;
        }
        
        .blur-bg.top-right {
            top: -50px;
            right: -50px;
        }
        
        .blur-bg.bottom-left {
            bottom: -50px;
            left: -50px;
        }
        
        .breadcrumb-nav {
            display: flex;
            align-items: center;
            margin-bottom: 32px;
            opacity: 0;
            transform: translateY(-10px);
            animation: fadeIn 0.35s ease forwards;
        }
        
        .back-button {
            display: inline-flex;
            align-items: center;
            color: var(--accent-color);
            font-size: 18px;
            font-weight: 500;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }
        
        .back-button:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }
        
        .back-button i {
            margin-right: 8px;
        }
        
        .card {
            background-color: var(--card-bg-color);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: var(--card-radius);
            box-shadow: var(--card-shadow);
            padding: var(--card-padding);
            margin-bottom: 24px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeIn 0.35s ease forwards;
        }
        
        .card:hover {
            background-color: var(--card-hover-bg-color);
            box-shadow: var(--card-hover-shadow);
        }
        
        .city-stats-card {
            animation-delay: 0.1s;
        }
        
        .county-stats-card {
            animation-delay: 0.2s;
        }
        
        .connection-rate-card {
            animation-delay: 0.3s;
        }
        
        .page-title {
            text-align: center;
            margin-bottom: 40px;
            font-size: 32px;
            font-weight: 600;
            color: #1d1d1f;
            opacity: 0;
            animation: fadeIn 0.35s ease forwards;
            position: relative;
        }
        
        .page-title::after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: -10px;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #007AFF, #5856D6);
            transform: translateX(-50%);
            border-radius: 3px;
        }
        
        .stats-value {
            font-size: 36px;
            font-weight: 700;
            margin: 20px 0;
            color: #007AFF;
            background: linear-gradient(135deg, #007AFF, #5856D6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .stats-label {
            font-size: 18px;
            color: #6c757d;
            margin-bottom: 10px;
        }
        
        .county-card {
            border-radius: var(--card-radius);
            background-color: var(--card-bg-color);
            padding: 15px;
            margin-bottom: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .county-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        }
        
        .county-name {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .county-stats {
            font-size: 16px;
            margin-bottom: 5px;
            color: #555;
        }
        
        .chart-container {
            width: 100%;
            height: 400px;
            margin-top: 20px;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .connection-rate-label {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .connection-rate-value {
            font-size: 32px;
            font-weight: 700;
            color: #007AFF;
            margin-bottom: 15px;
        }
        
        .progress {
            height: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .progress-bar {
            background: linear-gradient(90deg, #007AFF, #5856D6);
            transition: width 0.3s ease-in-out;
        }
        
        .progress-bar[aria-valuenow] {
            width: var(--progress-width);
        }
        
        .high-rate {
            color: #28a745;
        }
        
        .medium-rate {
            color: #ffc107;
        }
        
        .low-rate {
            color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="blur-bg top-right"></div>
        <div class="blur-bg bottom-left"></div>
        
        <div class="breadcrumb-nav">
            <a href="/" class="back-button">
                <i class="fas fa-chevron-left"></i>返回首页
            </a>
        </div>
        
        <!-- 错误信息提示 -->
        {% if stats.error or error %}
        <div class="alert alert-danger mb-4">
            <h4 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> 数据获取异常</h4>
            <p>{{ stats.error or error }}</p>
            <p class="mb-0">目前显示的是默认数据，请检查数据库连接配置。</p>
            
            {% if stats.setup_guide %}
            <hr>
            <div class="mt-3">
                <h5>配置指南</h5>
                <p>您需要创建数据库配置表并添加连接信息，步骤如下：</p>
                <ol>
                    <li>找到项目根目录中的 <code>{{ stats.setup_sql_file }}</code> 文件</li>
                    <li>修改SQL文件中的数据库连接信息（用户名、密码等）</li>
                    <li>将SQL文件导入您的数据库：<code>mysql -u [用户名] -p [数据库名] < {{ stats.setup_sql_file }}</code></li>
                </ol>
                <p>完成后刷新此页面即可正常访问数据</p>
            </div>
            {% endif %}
        </div>
        {% endif %}
        
        <!-- 第一行：全市总览数据 -->
        <div class="row">
            <div class="col-md-12">
                <div class="card city-stats-card">
                    <h3 class="card-title text-center mb-4">全市目录挂接率概览</h3>
                    <div class="row">
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h5 class="card-title">资源目录挂接情况</h5>
                                    <div class="row text-center mt-3">
                                        <div class="col-md-4">
                                            <div class="stats-label">总目录数</div>
                                            <div class="stats-value" id="totalCatalogs">{{ stats.city.total_catalogs }}</div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="stats-label">已挂接资源数</div>
                                            <div class="stats-value" id="connectedCatalogs">{{ stats.city.connected_catalogs }}</div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="stats-label">挂接率</div>
                                            <div class="stats-value" id="connectionRate">{{ stats.city.connection_rate }}%</div>
                                        </div>
                                    </div>
                                    <div class="progress mt-3">
                                        <div class="progress-bar" 
                                            role="progressbar"
                                            style="--progress-width: {{ stats.city.connection_rate }}%"
                                            aria-valuenow="{{ stats.city.connection_rate }}" 
                                            aria-valuemin="0" 
                                            aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <h5 class="card-title">库表接口挂接情况</h5>
                                    <div class="row text-center mt-3">
                                        <div class="col-md-4">
                                            <div class="stats-label">总目录数</div>
                                            <div class="stats-value">{{ stats.city.total_catalogs }}</div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="stats-label">已挂接库表接口</div>
                                            <div class="stats-value" id="tableApiConnected">{{ stats.city.table_api_connected }}</div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="stats-label">挂接率</div>
                                            <div class="stats-value" id="tableApiConnectionRate">{{ stats.city.table_api_connection_rate }}%</div>
                                        </div>
                                    </div>
                                    <div class="progress mt-3">
                                        <div class="progress-bar" 
                                            role="progressbar"
                                            style="--progress-width: {{ stats.city.table_api_connection_rate }}%"
                                            aria-valuenow="{{ stats.city.table_api_connection_rate }}" 
                                            aria-valuemin="0" 
                                            aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 县区目录总数 -->
        <div class="row mt-4">
            {% for county_name, county_info in stats.counties.items() %}
            <div class="col-md-3 mb-4">
                <div class="card h-100">
                    <div class="card-body text-center">
                        <h5 class="card-title county-name">{{ county_name }}</h5>
                        <div class="stats-value">{{ county_info.total }}</div>
                        <div class="stats-label">目录总数</div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- 县区库表接口挂接率 -->
        <div class="row connection-rate-card">
            <div class="col-md-12">
                <div class="card">
                    <h3 class="card-title text-center mb-4">县区库表接口挂接率</h3>
                    <div class="row">
                        {% for county_name, rate in stats.connection_rates.items() %}
                        <div class="col-md-3 mb-4">
                            <div class="county-card">
                                <div class="county-name">{{ county_name }}</div>
                                <div class="connection-rate-value {% if rate >= 70 %}high-rate{% elif rate >= 40 %}medium-rate{% else %}low-rate{% endif %}">
                                    {{ rate }}%
                                </div>
                                <div class="progress">
                                    <div class="progress-bar
                                              {% if rate >= 70 %}bg-success{% elif rate >= 40 %}bg-warning{% else %}bg-danger{% endif %}"
                                         role="progressbar"
                                         style="--progress-width: {{ rate }}%"
                                         aria-valuenow="{{ rate }}" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 本地 jQuery -->
    <script src="/static/js/lib/jquery-3.6.0.min.js"></script>
    <!-- 本地 Bootstrap JS -->
    <script src="/static/js/lib/bootstrap.bundle.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // 自动刷新数据，每10分钟刷新一次
            setInterval(refreshData, 600000);

            // 添加更新日志按钮点击事件
            $('#changelogBtn').on('click', function() {
                var changelogModal = new bootstrap.Modal(document.getElementById('changelogModal'));
                changelogModal.show();
            });
        });
        
        function refreshData() {
            $.ajax({
                url: '/table_connection_rate/api/stats',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    // 更新全市数据
                    $('#totalCatalogs').text(data.city.total_catalogs);
                    $('#connectedCatalogs').text(data.city.connected_catalogs);
                    $('#connectionRate').text(data.city.connection_rate + '%');
                    
                    // 更新库表接口挂接率数据
                    $('#tableApiConnected').text(data.city.table_api_connected);
                    $('#tableApiConnectionRate').text(data.city.table_api_connection_rate + '%');
                    
                    // 更新进度条宽度
                    var cityRate = Math.round(data.city.connection_rate);
                    $('.city-stats-card .card:first .progress-bar')
                        .css('--progress-width', cityRate + '%')
                        .attr('aria-valuenow', data.city.connection_rate);
                        
                    var tableApiRate = Math.round(data.city.table_api_connection_rate);
                    $('.city-stats-card .card:last .progress-bar')
                        .css('--progress-width', tableApiRate + '%')
                        .attr('aria-valuenow', data.city.table_api_connection_rate);
                },
                error: function(xhr, status, error) {
                    console.error('获取数据失败:', error);
                }
            });
        }
    </script>
    
    <!-- 包含页脚组件 -->
    {% include 'footer_component.html' %}
</body>
</html> 