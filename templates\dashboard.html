<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理看板 - 数据管理系统</title>
    <!-- 本地Bootstrap CSS -->
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="/static/css/all.min.css">
    <style>
        :root {
            --card-bg-color: rgba(255, 255, 255, 0.7);
            --card-hover-bg-color: rgba(255, 255, 255, 0.8);
            --card-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
            --card-hover-shadow: 0 8px 24px rgba(0, 0, 0, 0.16);
            --card-radius: 12px;
            --card-padding: 20px;
            --accent-color: #007AFF;
            --grid-gap: 24px;
            --page-padding: 32px;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
            background-image: 
                radial-gradient(circle at 85% 15%, rgba(0, 122, 255, 0.08) 0%, transparent 25%),
                radial-gradient(circle at 15% 85%, rgba(88, 86, 214, 0.08) 0%, transparent 30%);
            min-height: 100vh;
            padding: var(--page-padding);
            color: #1d1d1f;
        }
        
        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
            position: relative;
            padding-top: 40px;
        }
        
        .blur-bg {
            position: absolute;
            width: 300px;
            height: 300px;
            border-radius: 50%;
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.1), rgba(88, 86, 214, 0.1));
            filter: blur(80px);
            z-index: -1;
        }
        
        .blur-bg.top-right {
            top: -50px;
            right: -50px;
        }
        
        .blur-bg.bottom-left {
            bottom: -50px;
            left: -50px;
        }
        
        .breadcrumb-nav {
            display: flex;
            align-items: center;
            margin-bottom: 32px;
            opacity: 0;
            transform: translateY(-10px);
            animation: fadeIn 0.35s ease forwards;
        }
        
        .back-button {
            display: inline-flex;
            align-items: center;
            color: var(--accent-color);
            font-size: 18px;
            font-weight: 500;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }
        
        .back-button:hover {
            background-color: rgba(0, 122, 255, 0.1);
        }
        
        .back-button i {
            margin-right: 8px;
        }
        
        .card-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 32px 28px;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .card {
            min-height: 170px;
            background-color: var(--card-bg-color);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 18px;
            box-shadow: 0 6px 24px rgba(0, 122, 255, 0.08), 0 1.5px 6px rgba(88, 86, 214, 0.06);
            padding: 28px 20px 20px 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(.4,2,.6,1);
            opacity: 0;
            transform: translateY(20px);
            text-decoration: none;
            color: #1d1d1f;
            position: relative;
            overflow: hidden;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card:hover {
            background-color: #f4f8ff;
            box-shadow: 0 12px 32px rgba(0, 122, 255, 0.13), 0 2px 8px rgba(88, 86, 214, 0.09);
        }
        
        .card:active {
            transform: scale(0.98);
            transition: transform 0.1s;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        }
        
        .card-icon {
            color: var(--accent-color);
            font-size: 38px;
            margin-bottom: 14px;
            background: linear-gradient(135deg, #007AFF, #5856D6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }
        
        .card-title {
            font-size: 20px;
            font-weight: 700;
            margin: 0 0 2px 0;
        }
        
        .card-description {
            font-size: 15px;
            color: #6c757d;
            margin-top: 10px;
            display: block;
            line-height: 1.6;
            min-height: 36px;
        }
        
        .level2 .card-description, .level3 .card-description {
            display: block;
        }
        
        /* 动画效果 */
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .page-title {
            text-align: center;
            margin-bottom: 40px;
            font-size: 32px;
            font-weight: 600;
            color: #1d1d1f;
            opacity: 0;
            animation: fadeIn 0.35s ease forwards;
            position: relative;
        }
        
        .page-title::after {
            content: '';
            position: absolute;
            left: 50%;
            bottom: -10px;
            width: 60px;
            height: 3px;
            background: linear-gradient(90deg, #007AFF, #5856D6);
            transform: translateX(-50%);
            border-radius: 3px;
        }
        
        .page-subtitle {
            text-align: center;
            margin: -30px auto 40px;
            font-size: 16px;
            font-weight: 400;
            color: #666;
            opacity: 0;
            animation: fadeIn 0.35s ease 0.1s forwards;
            max-width: 600px;
        }
        
        /* 响应式设计 */
        @media (max-width: 1200px) {
            .card-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        
        @media (max-width: 900px) {
            .card-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .card {
                min-height: 150px;
                padding: 18px 10px 14px 10px;
            }
            .card-title {
                font-size: 17px;
            }
            .card-description {
                font-size: 13px;
                min-height: 28px;
            }
        }
        
        @media (max-width: 480px) {
            .card-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* 层级卡片视图 */
        .level2, .level3 {
            display: none;
        }
        
        /* 顶部导航样式 */
        .top-nav {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 20px;
            opacity: 0;
            animation: fadeIn 0.35s ease 0.1s forwards;
        }
        
        /* 密码验证模态框样式 */
        .modal-backdrop {
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .modal-header {
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
            padding: 20px 25px;
        }
        
        .modal-body {
            padding: 25px;
        }
        
        .modal-footer {
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 15px 25px;
        }
        
        .form-control {
            border-radius: 8px;
            padding: 12px 15px;
            border: 1px solid rgba(0, 0, 0, 0.1);
            background-color: rgba(255, 255, 255, 0.5);
            backdrop-filter: blur(5px);
            transition: all 0.3s;
        }
        
        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.25);
        }
        
        .btn {
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }
        
        .btn-primary:hover {
            background-color: #0062cc;
            border-color: #0062cc;
        }
        
        .btn-secondary {
            background-color: #6c757d;
            border-color: #6c757d;
        }
        
        .edit-required {
            position: relative;
        }
        
        .edit-badge {
            position: absolute;
            top: -10px;
            right: -10px;
            background-color: var(--accent-color);
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.2);
                opacity: 0.7;
            }
            100% {
                transform: scale(1);
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="blur-bg top-right"></div>
        <div class="blur-bg bottom-left"></div>
        <!-- 首页所有功能卡片 -->
        <h1 class="page-title">数据管理系统</h1>
        <p class="page-subtitle">简洁高效的数据管理平台，轻松掌握数据资产</p>
        <div class="card-grid">
            <a href="javascript:void(0)" class="card" id="data-entry-link" style="animation-delay: 0.1s; animation: fadeIn 0.35s ease forwards 0.1s;">
                <div class="card-icon">
                    <i class="fas fa-list"></i>
                </div>
                <h2 class="card-title">入湖记录</h2>
                <span class="card-description">查看和管理所有已入湖的数据记录</span>
            </a>
            
            <a href="javascript:void(0)" class="card" id="visualization-link" style="animation-delay: 0.2s; animation: fadeIn 0.35s ease forwards 0.2s;">
                <div class="card-icon">
                    <i class="fas fa-chart-bar"></i>
                </div>
                <h2 class="card-title">数据可视化</h2>
                <span class="card-description">以图表方式直观展示数据分布和趋势</span>
            </a>
            
            <a href="javascript:void(0)" class="card" id="data-center-link" style="animation-delay: 0.3s; animation: fadeIn 0.35s ease forwards 0.3s;">
                <div class="card-icon">
                    <i class="fas fa-database"></i>
                </div>
                <h2 class="card-title">数据中台</h2>
                <span class="card-description">统一管理和调度各类数据资源</span>
            </a>
            
            <a href="javascript:void(0)" class="card" id="mysql-audit-link" style="animation-delay: 0.4s; animation: fadeIn 0.35s ease forwards 0.4s;">
                <div class="card-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <h2 class="card-title">MySQL 日志审计</h2>
                <span class="card-description">审计和分析 MySQL 数据库的操作日志</span>
            </a>
            
            <a href="javascript:void(0)" class="card" id="ansible-link" style="animation-delay: 0.5s; animation: fadeIn 0.35s ease forwards 0.5s;">
                <div class="card-icon">
                    <i class="fas fa-server"></i>
                </div>
                <h2 class="card-title">Ansible 管理</h2>
                <span class="card-description">自动化运维与批量任务管理</span>
            </a>
            
            <a href="javascript:void(0)" class="card" id="data-display-link" style="animation-delay: 0.6s; animation: fadeIn 0.35s ease forwards 0.6s;">
                <div class="card-icon">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <h2 class="card-title">数据展示</h2>
                <span class="card-description">多维度展示数据详情与统计结果</span>
            </a>
            
            <a href="javascript:void(0)" class="card" id="table-connection-rate-link" style="animation-delay: 0.7s; animation: fadeIn 0.35s ease forwards 0.7s;">
                <div class="card-icon">
                    <i class="fas fa-link"></i>
                </div>
                <h2 class="card-title">库表挂接率</h2>
                <span class="card-description">统计目录与库表挂接的挂接情况</span>
            </a>
            
            <a href="javascript:void(0)" class="card" id="metrics-stats-link" style="animation-delay: 0.8s; animation: fadeIn 0.35s ease forwards 0.8s;">
                <div class="card-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <h2 class="card-title">指标统计</h2>
                <span class="card-description">查看和分析各项指标的统计数据</span>
            </a>
            
            <!-- 网络小工具卡片 -->
            <a href="/tools/" class="card" id="network-tools-link" style="animation-delay: 0.9s; animation: fadeIn 0.35s ease forwards 0.9s;">
                <div class="card-icon">
                    <i class="fas fa-network-wired"></i>
                </div>
                <h2 class="card-title">网络小工具</h2>
                <span class="card-description">提供 Ping 和 Telnet 等常用网络诊断工具</span>
            </a>
        </div>
    </div>
    
    <!-- 密码验证模态框已删除，系统默认授予所有用户编辑权限 -->

    <!-- 本地 jQuery -->
    <script src="/static/js/lib/jquery-3.6.0.min.js"></script>
    <!-- 本地 Bootstrap JS -->
    <script src="/static/js/lib/bootstrap.bundle.min.js"></script>
    <script>
        $(document).ready(function() {
            // 记录当前操作，用于密码验证后的跳转
            let currentAction = '';
            
            // 路由权限配置
            const routesRequiringEditPermission = [
                '/exports/current',
                '/exports/all',
                '/county/data/entry/add_record_page',
                '/data/records',
                '/data_center',
                '/mysql_audit',
                '/ansible'
            ];
            
            // 卡片链接配置
            const cardLinks = {
                'visualization-link': '/visualization',
                'data-center-link': '/data_center',
                'mysql-audit-link': '/mysql_audit',
                'ansible-link': '/ansible',
                'data-display-link': '/data_display',
                'data-entry-link': '/county/data/entry',
                'table-connection-rate-link': '/table_connection_rate',
                'metrics-stats-link': '/metrics_stats',
                'network-tools-link': '/tools/'
            };
            
            // 检查路由是否需要编辑权限
            function routeRequiresPermission(route) {
                return routesRequiringEditPermission.some(r => route.startsWith(r));
            }
            
            // 绑定卡片点击事件
            Object.keys(cardLinks).forEach(id => {
                $(`#${id}`).click(function(e) {
                    e.preventDefault();
                    const targetUrl = cardLinks[id];
                    
                    if (routeRequiresPermission(targetUrl)) {
                        // 需要编辑权限但系统已默认授予，直接跳转
                        window.location.href = targetUrl;
                    } else {
                        // 不需要权限，直接跳转
                        window.location.href = targetUrl;
                    }
                });
            });
            
            // 设置卡片出现动画
            $('.card').each(function(i) {
                setTimeout(() => {
                    $(this).css({
                        'animation': 'fadeIn 0.35s ease forwards',
                        'animation-delay': i * 0.05 + 's'
                    });
                }, 100);
            });
            
            // 点击一级卡片时的处理
            $('[data-target]').click(function() {
                const target = $(this).data('target');
                if (target === 'export-data') {
                    // 如果点击的是"导出数据"，跳转到三级菜单
                    $('.level2').fadeOut(350, function() {
                        $(`#${target}-level3`).fadeIn(350);
                    });
                } else if (target.includes('level3')) {
                    // 如果是三级菜单
                    $('.level2').fadeOut(350, function() {
                        $(`#${target}`).fadeIn(350);
                    });
                } else {
                    // 如果是二级菜单
                    $('.level1').fadeOut(350, function() {
                        $(`#${target}-level2`).fadeIn(350);
                    });
                }
            });
            
            // 返回到一级菜单
            $('#back-to-level1').click(function(e) {
                e.preventDefault();
                $('.level2').fadeOut(350, function() {
                    $('.level1').fadeIn(350);
                });
            });
            
            // 返回到二级菜单
            $('#back-to-level2').click(function(e) {
                e.preventDefault();
                $('.level3').fadeOut(350, function() {
                    $('#entry-data-level2').fadeIn(350);
                });
            });
        });
    </script>

    <!-- 版本信息页脚 -->
    <footer class="mt-5 text-center text-muted">
        <p><small>2025 数据管理系统 | 版本 <span id="appVersion">v4</span>
            <a href="javascript:void(0)" class="btn btn-link p-0 ms-2" id="changelogBtn" style="font-size: 1em;vertical-align: baseline;">更新日志</a>
        </small></p>
    </footer>

    <!-- 版本更新日志模态框 -->
    {% include 'changelog_modal.html' %}

    <!-- 加载版本号脚本 -->
    <script>
      $(function(){
        // 获取并更新版本号
        fetch('/api/version')
          .then(response => response.json())
          .then(data => {
            $('#appVersion').text(data.version);
          })
          .catch(error => {
            console.error('获取版本信息失败:', error);
          });
        
        // 更新日志按钮点击事件
        $('#changelogBtn').on('click', function(){
          var modal = new bootstrap.Modal(document.getElementById('changelogModal'));
          modal.show();
        });
      });
    </script>
</body>
</html> 