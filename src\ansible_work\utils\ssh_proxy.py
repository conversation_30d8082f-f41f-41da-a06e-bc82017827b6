import paramiko
from ..config import Config
import logging

# 配置日志
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class SSHProxy:
    def __init__(self):
        self.jump_host = Config.JUMP_HOST
        self.jump_port = Config.JUMP_PORT
        self.ansible_host = Config.ANSIBLE_HOST
        self.client = None

    def create_proxy_connection(self, username, password):
        """创建到跳板机的SSH连接"""
        try:
            logger.debug(f"尝试连接跳板机 {self.jump_host}:{self.jump_port} 使用用户名 {username}")
            
            # 创建跳板机SSH连接
            jump_client = paramiko.SSHClient()
            jump_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            
            # 连接跳板机
            jump_client.connect(
                hostname=self.jump_host,
                port=self.jump_port,
                username=username,
                password=password,
                allow_agent=False,
                look_for_keys=False
            )
            
            logger.debug("成功连接到跳板机")
            
            # 测试连接
            stdin, stdout, stderr = jump_client.exec_command('echo "Connection to jump host successful"')
            out = stdout.read().decode()
            err = stderr.read().decode()
            
            if err:
                logger.error(f"跳板机连接测试失败: {err}")
                raise Exception(f"跳板机连接测试失败: {err}")
            
            logger.debug(f"跳板机连接测试成功: {out}")
            
            # 保存客户端连接
            self.client = jump_client
            return jump_client
            
        except Exception as e:
            logger.error(f"SSH连接失败: {str(e)}")
            raise Exception(f"SSH连接失败: {str(e)}")

    def execute_command(self, client, command):
        """在Ansible控制节点上执行命令"""
        try:
            # 如果client为None，则使用self.client
            if client is None:
                if self.client is None:
                    raise Exception("SSH连接未建立")
                client = self.client
                
            logger.debug(f"通过跳板机在Ansible控制节点上执行命令: {command}")
            
            # 构建通过跳板机执行的命令
            # 使用ssh命令连接到Ansible服务器并执行命令
            # 添加-o StrictHostKeyChecking=no 选项以避免主机密钥检查
            # 添加-t选项使用交互式shell，并在执行命令前加载环境变量
            ansible_command = f"ssh -o StrictHostKeyChecking=no -t {self.ansible_host} 'source /etc/profile && source ~/.bashrc && {command}'"
            logger.debug(f"完整命令: {ansible_command}")
            
            # 执行命令
            stdin, stdout, stderr = client.exec_command(ansible_command)
            out = stdout.read().decode()
            err = stderr.read().decode()
            
            logger.debug(f"命令执行结果 - 输出: {out}")
            if err:
                logger.debug(f"命令执行结果 - 标准错误输出: {err}")
                
                # 忽略欢迎信息和警告信息
                if "You have logged onto a secured server" in err:
                    logger.debug("忽略欢迎信息")
                    err = ""
            
            # 获取返回码
            rc = stdout.channel.recv_exit_status()
            
            # 返回字典格式的结果
            return {
                'stdout': out,
                'stderr': err,
                'rc': rc
            }
            
        except Exception as e:
            logger.error(f"执行命令失败: {str(e)}")
            return {
                'stdout': '',
                'stderr': str(e),
                'rc': 1
            }