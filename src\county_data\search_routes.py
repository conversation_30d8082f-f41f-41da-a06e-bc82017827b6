from flask import Blueprint, request, jsonify, session
from sqlalchemy import text
import time
from datetime import datetime

# 假设这些辅助函数能被正确导入
from src.utils.database_helpers import (
    get_db_connection, execute_query, get_all_tables, get_table_comments, 
    get_table_display_name
)

search_bp = Blueprint('search', __name__, url_prefix='/lookup') # Use /lookup as prefix

@search_bp.route('/org_units') # URL: /lookup/org_units
def search_org_units():
    """搜索组织机构"""
    try:
        start_time = time.time()
        keyword = request.args.get('term', '')
        
        if not keyword or len(keyword) < 1:
            return jsonify([])
            
        engine = get_db_connection()
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT org_code, org_name
                FROM test_org_code_metadata
                WHERE org_name LIKE :keyword
                ORDER BY 
                    CASE 
                        WHEN org_name = :exact_keyword THEN 0
                        WHEN org_name LIKE :start_keyword THEN 1
                        ELSE 2
                    END,
                    org_name
                LIMIT 10
            """), {
                "keyword": f"%{keyword}%", 
                "exact_keyword": keyword,
                "start_keyword": f"{keyword}%"
            })
            
            units = [{'value': row[1], 'org_code': row[0]} for row in result]
            print(f"search_org_units took {time.time() - start_time:.2f} seconds")
            return jsonify(units)
    except Exception as e:
        print(f"Error in search_org_units: {str(e)}")
        return str(e), 500

@search_bp.route('/common_units') # URL: /lookup/common_units
def preload_common_units():
    """预加载常用单位数据"""
    try:
        engine = get_db_connection()
        with engine.connect() as conn:
            result = conn.execute(text("""
                SELECT DISTINCT m.org_code, m.org_name
                FROM test_org_code_metadata m
                JOIN (
                    SELECT org_code, COUNT(*) as usage_count
                    FROM (
                        SELECT org_code FROM excel_data_qin_zhou
                        UNION ALL
                        SELECT org_code FROM excel_data_mai_ji
                        UNION ALL
                        SELECT org_code FROM excel_data_qin_an
                        UNION ALL
                        SELECT org_code FROM excel_data_gan_gu
                        UNION ALL
                        SELECT org_code FROM excel_data_qin_shui
                        UNION ALL
                        SELECT org_code FROM excel_data_wu_shan
                        UNION ALL
                        SELECT org_code FROM excel_data_zhang_jia_chuan
                        UNION ALL
                        SELECT org_code FROM excel_data_tss_sz
                        # Add other relevant tables here if needed for common units
                    ) t
                    WHERE org_code IS NOT NULL
                    GROUP BY org_code
                    ORDER BY usage_count DESC
                    LIMIT 50
                ) usage ON m.org_code = usage.org_code
                ORDER BY usage.usage_count DESC
            """))
            
            units = [{'value': row[1], 'org_code': row[0]} for row in result]
            return jsonify(units)
    except Exception as e:
        print(f"Error in preload_common_units: {str(e)}")
        return jsonify([])

@search_bp.route('/table_fields/<table_name>') # URL: /lookup/table_fields/<table_name>
def get_table_fields_route(table_name): # Renamed function
    """获取表的字段信息"""
    try:
        required_fields = [
            'data_provider', 'chinese_name', 'table_name', 'org_code',
            'record_count', 'table_description', 'provide_time',
            'is_pushed', 'is_imported'
        ]
        
        engine = get_db_connection()
        with engine.connect() as conn:
            fields_str = "'" + "','".join(required_fields) + "'"
            query = text(f"""
                SELECT 
                    COLUMN_NAME,
                    COLUMN_COMMENT,
                    IS_NULLABLE
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = DATABASE() # Use DATABASE() instead of hardcoded 'excel'
                AND TABLE_NAME = :table_name
                AND COLUMN_NAME IN ({fields_str})
                ORDER BY FIELD(COLUMN_NAME, {fields_str})
            """)
            
            result = conn.execute(query, {"table_name": table_name})
            fields = [{
                'name': row[0],
                'comment': row[1] if row[1] else row[0],
                'required': True
            } for row in result]
            return jsonify(fields)
    except Exception as e:
        print(f"Error in get_table_fields_route: {str(e)}")
        return str(e), 500

@search_bp.route('/org_suggestions') # URL: /lookup/org_suggestions
def get_org_suggestions_route(): # Renamed function
    try:
        search_term = request.args.get('term', '')
        engine = get_db_connection()
        query = text("""
            SELECT org_name, org_code 
            FROM test_org_code_metadata 
            WHERE org_name LIKE :search_term
            LIMIT 10
        """)
        with engine.connect() as conn:
            results = conn.execute(query, {"search_term": f"%{search_term}%"})
            suggestions = [{
                'label': row[0],
                'value': row[0],
                'org_code': row[1]
            } for row in results]
            print(f"Suggestions for '{search_term}': {suggestions}")
            return jsonify(suggestions)
    except Exception as e:
        print(f"Error in get_org_suggestions_route: {str(e)}")
        return jsonify([])

# 搜索数据表 (Table Search)
@search_bp.route('/tables', methods=['GET']) # URL: /lookup/tables
def search_tables_route(): # Renamed function
    try:
        keyword = request.args.get('keyword', '')
        if not keyword: return jsonify([])
        
        session['last_search_keyword'] = keyword
        engine = get_db_connection()
        tables = get_all_tables(engine)
        table_comments = get_table_comments(engine)
        results = []
        
        for table in tables:
            comment = table_comments.get(table, '')
            display_name = get_table_display_name(table)
            
            match_found = False
            # 1. Check table name/comment/display name
            if keyword.lower() in table.lower() or keyword.lower() in comment.lower() or keyword.lower() in display_name.lower():
                results.append({
                    'table': table, 'comment': comment, 'display_name': display_name,
                    'match_type': 'table', 'keyword': keyword
                })
                match_found = True
                continue # Prioritize table match

            # 2. Check content (table_name, chinese_name) if table match not found
            if not match_found:
                try:
                    # Query for table_name first
                    query_tn = text(f"SELECT id FROM `{table}` WHERE table_name LIKE :kw LIMIT 1")
                    with engine.connect() as conn:
                        if conn.execute(query_tn, {"kw": f"%{keyword}%"}).fetchone():
                            results.append({
                                'table': table, 'comment': comment, 'display_name': display_name,
                                'match_type': 'content', 'keyword': keyword
                            })
                            continue # Found a content match
                    
                    # Query for chinese_name if table_name didn't match
                    query_cn = text(f"SELECT id FROM `{table}` WHERE chinese_name LIKE :kw LIMIT 1")
                    with engine.connect() as conn:
                         if conn.execute(query_cn, {"kw": f"%{keyword}%"}).fetchone():
                            results.append({
                                'table': table, 'comment': comment, 'display_name': display_name,
                                'match_type': 'content', 'keyword': keyword
                            })
                            continue # Found a content match
                except Exception as e:
                    # Ignore tables that don't have these specific columns
                    # print(f"Skipping content search for table {table} due to error: {e}")
                    continue
        
        return jsonify(results)
    except Exception as e:
        print(f"Error in search_tables_route: {str(e)}")
        return jsonify({'error': str(e)}), 500

# 获取表数据（用于搜索结果）(Get Table Data)
@search_bp.route('/table_data') # URL: /lookup/table_data
def get_table_data_route(): # Renamed function
    try:
        table = request.args.get('table')
        page = int(request.args.get('page', 1))
        per_page = 10
        keyword = request.args.get('keyword', session.get('last_search_keyword', ''))
        
        # Logic to not use keyword if not coming from search might be needed here?
        # Currently, it defaults to last search keyword from session.

        if not table: return jsonify({'error': '未提供表名'}), 400
        
        engine = get_db_connection()
        table_comment = ""
        try:
            table_comment_query = text("SELECT TABLE_COMMENT FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = :table_name")
            with engine.connect() as conn:
                 comment_result = conn.execute(table_comment_query, {"table_name": table}).scalar_one_or_none()
                 table_comment = comment_result if comment_result else ""
        except Exception as e:
            print(f"获取表注释信息时出错 for {table}: {str(e)}")
            table_comment = ""

        # Base query
        base_query_str = f"""
            SELECT 
                t.*,
                m.org_name as unit_name
            FROM `{table}` t
            LEFT JOIN test_org_code_metadata m ON t.org_code = m.org_code
        """
        params = {}
        where_clauses = []
        
        # Keyword search logic
        if keyword:
            try:
                columns_query = text("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = :table_name")
                with engine.connect() as conn:
                    columns_result = conn.execute(columns_query, {"table_name": table}).fetchall()
                    columns = [row[0] for row in columns_result]
                
                keyword_conditions = []
                if 'table_name' in columns:
                    keyword_conditions.append(f"t.table_name LIKE :keyword")
                    params['keyword'] = f"%{keyword}%"
                elif 'chinese_name' in columns:
                     keyword_conditions.append(f"t.chinese_name LIKE :keyword")
                     params['keyword'] = f"%{keyword}%"
                else: # Fallback to search likely text columns if specific ones aren't present
                    for col in columns:
                         # Basic heuristic: avoid searching id/count/bool columns
                         if col.lower() not in ['id', 'record_count', 'is_pushed', 'is_imported'] and 'id' not in col.lower():
                            keyword_conditions.append(f"t.`{col}` LIKE :keyword") # Use backticks for column names
                    if keyword_conditions: 
                        params['keyword'] = f"%{keyword}%"
                
                if keyword_conditions:
                    where_clauses.append(f"({' OR '.join(keyword_conditions)})")

            except Exception as e:
                print(f"构建搜索条件时出错 for table {table}: {str(e)}")
        
        # Construct final WHERE clause
        if where_clauses:
            final_where_clause = f"WHERE {' AND '.join(where_clauses)}"
        else:
             final_where_clause = ""

        # Count total rows
        count_query_str = f"SELECT COUNT(*) FROM `{table}` t LEFT JOIN test_org_code_metadata m ON t.org_code = m.org_code {final_where_clause}"
        total_count = 0
        try:
             with engine.connect() as conn:
                total_count_res = conn.execute(text(count_query_str), params).scalar_one_or_none()
                total_count = total_count_res if total_count_res is not None else 0
        except Exception as e:
             print(f"计算总记录数时出错 for table {table}: {str(e)}")
             total_count = 0
        
        total_pages = (total_count + per_page - 1) // per_page if total_count > 0 else 1
        page = max(1, min(page, total_pages if total_pages > 0 else 1))
        offset = (page - 1) * per_page
        
        # Fetch data with limit and offset
        data_query_str = f"{base_query_str} {final_where_clause} LIMIT {per_page} OFFSET {offset}"
        data = []
        db_column_names = []
        try:
            with engine.connect() as conn:
                result = conn.execute(text(data_query_str), params)
                db_column_names = list(result.keys())
                data_rows = result.fetchall()
                data = [list(row) for row in data_rows]
        except Exception as e:
            print(f"执行查询时出错 for table {table}: {str(e)}")
            return jsonify({'error': f'搜索表数据失败: {str(e)}'}), 500
        
        # Prepare display data
        column_display_names = ['单位名称', '中文表名', '表名', '数据条数', '提供时间', '是否推送', '是否入湖']
        db_columns_map = {
            '单位名称': 'unit_name', '中文表名': 'chinese_name', '表名': 'table_name',
            '数据条数': 'record_count', '提供时间': 'provide_time', 
            '是否推送': 'is_pushed', '是否入湖': 'is_imported'
        }
        
        filtered_data = []
        for row_data in data:
            row_dict = dict(zip(db_column_names, row_data))
            filtered_row = []
            for display_name in column_display_names:
                db_col = db_columns_map.get(display_name)
                value = row_dict.get(db_col)
                
                if display_name in ['是否推送', '是否入湖']:
                    value = "是" if value == 1 or value == '1' else "否"
                elif display_name == '提供时间' and isinstance(value, datetime):
                     value = value.strftime('%Y-%m-%d') # Format date
                elif display_name == '提供时间' and isinstance(value, str) and len(value) > 10:
                     value = value[:10] # Truncate if already string

                filtered_row.append(value)
            filtered_data.append(filtered_row)
        
        return jsonify({
            'display_name': get_table_display_name(table),
            'comment': table_comment,
            'columns': list(db_columns_map.values()), # Send the db column names expected by frontend
            'column_comments': {}, # This was empty before, keep it or fetch if needed
            'column_display_names': column_display_names, # Send the display names for header
            'data': filtered_data,
            'page': page,
            'per_page': per_page,
            'total_count': total_count,
            'total_pages': total_pages
        })
    except Exception as e:
        print(f"获取表数据错误 for {table}: {str(e)}")
        return jsonify({'error': f'获取表数据错误: {str(e)}'}), 500

# 获取多表数据 (Get Multiple Tables Data)
@search_bp.route('/multiple_table_data') # URL: /lookup/multiple_table_data
def get_multiple_tables_data_route(): # Renamed function
    try:
        tables = request.args.getlist('tables[]')
        keyword = request.args.get('keyword', '')
        page = int(request.args.get('page', 1))
        per_page = 10
        
        if not tables: return jsonify({'error': '未提供表名'}), 400
        if not keyword: return jsonify({'error': '未提供关键词'}), 400
        
        engine = get_db_connection()
        all_rows_data = []
        db_column_names_set = set() # To store all possible column names
        
        # Define the columns we absolutely need for display
        required_db_columns = ['id', 'unit_name', 'chinese_name', 'table_name', 'record_count', 'provide_time', 'is_pushed', 'is_imported']
        column_display_names = ['单位名称', '中文表名', '表名', '数据条数', '提供时间', '是否推送', '是否入湖']
        db_columns_map = {
             '单位名称': 'unit_name', '中文表名': 'chinese_name', '表名': 'table_name',
             '数据条数': 'record_count', '提供时间': 'provide_time',
             '是否推送': 'is_pushed', '是否入湖': 'is_imported'
         }

        # Collect data from all tables
        for table_name in tables:
            try:
                # Get actual columns for the table
                cols_query = text("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = :table_name")
                with engine.connect() as conn:
                    cols_result = conn.execute(cols_query, {"table_name": table_name}).fetchall()
                    actual_columns = [row[0] for row in cols_result]
                    db_column_names_set.update(actual_columns) # Keep track of all columns encountered

                # Build search conditions for this table
                search_conditions = []
                params = {}
                for col in actual_columns:
                     if col.lower() not in ['id', 'record_count', 'is_pushed', 'is_imported'] and 'id' not in col.lower(): # Basic heuristic
                         search_conditions.append(f"t.`{col}` LIKE :keyword")
                if search_conditions:
                     params['keyword'] = f"%{keyword}%"
                     where_clause = f"WHERE {' OR '.join(search_conditions)}"
                else:
                     where_clause = "" # No searchable columns?
                     params = {}
                
                # Build query for this table
                select_cols = [f"t.`{col}`" for col in actual_columns if col in required_db_columns or col == 'org_code'] # Select required + org_code
                select_cols.append("m.org_name as unit_name")
                select_str = ", ".join(select_cols)
                
                query_str = f"""
                    SELECT {select_str}
                    FROM `{table_name}` t
                    LEFT JOIN test_org_code_metadata m ON t.org_code = m.org_code
                    {where_clause}
                """
                
                with engine.connect() as conn:
                    result = conn.execute(text(query_str), params)
                    current_keys = list(result.keys())
                    for row in result.fetchall():
                        all_rows_data.append(dict(zip(current_keys, row)))
            except Exception as e:
                print(f"Error querying table {table_name} for multi-search: {e}")
                continue # Skip table on error

        # Paginate collected data
        total_rows = len(all_rows_data)
        total_pages = (total_rows + per_page - 1) // per_page if total_rows > 0 else 1
        page = max(1, min(page, total_pages if total_pages > 0 else 1))
        start_idx = (page - 1) * per_page
        end_idx = min(start_idx + per_page, total_rows)
        paged_data = all_rows_data[start_idx:end_idx]
        
        # Prepare display data
        filtered_data = []
        for row_dict in paged_data:
            filtered_row = []
            for display_name in column_display_names:
                db_col = db_columns_map.get(display_name)
                value = row_dict.get(db_col)
                
                if display_name in ['是否推送', '是否入湖']:
                     value = "是" if value == 1 or value == '1' else "否"
                elif display_name == '提供时间' and isinstance(value, datetime):
                     value = value.strftime('%Y-%m-%d')
                elif display_name == '提供时间' and isinstance(value, str) and len(value) > 10:
                    value = value[:10]
                
                filtered_row.append(value)
            filtered_data.append(filtered_row)

        return jsonify({
            'columns': list(db_columns_map.values()), # Consistent column order
            'column_comments': {},
            'column_display_names': column_display_names,
            'data': filtered_data,
            'page': page,
            'per_page': per_page,
            'total_rows': total_rows,
            'total_pages': total_pages
        })
    except Exception as e:
        print(f"获取多表数据错误: {str(e)}")
        return jsonify({'error': f'获取多表数据错误: {str(e)}'}), 500 