# 系统 API 文档

## 目录
1. [核心路由](#核心路由)
2. [数据中台模块](#数据中台模块)
3. [区县数据模块](#区县数据模块)
4. [MySQL审计模块](#mysql审计模块)
5. [Ansible自动化模块](#ansible自动化模块)
6. [服务器管理模块](#服务器管理模块)
7. [数据展示模块](#数据展示模块)

## 通用说明

### 基础URL
所有API的基础URL为：`http://your-domain:5100`

### 认证方式
- 需要编辑权限的接口使用密码验证
- 密码验证通过后会在session中保存验证状态
- 验证状态有效期为会话期间

### 响应格式
所有API响应均为JSON格式，基本结构如下：
```json
{
    "code": 200,           // 状态码
    "message": "success",  // 状态信息
    "data": {}            // 响应数据
}
```

### 错误码说明
| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 核心路由

### 版本信息
- **GET** `/api/version`
  - 描述：获取当前系统版本信息
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "version": "v4.7",
            "show_notification": false,
            "release_notes": [
                "1. 新增数据治理模块，支持对数据库进行全面质量检查",
                "2. 优化数据中台功能，提升数据加载速度",
                "3. 新增数据显示模块，支持对数据进行可视化展示",
                "4. 修复已知问题，提高系统稳定性",
                "5. 新增ansible模块，支持对服务器进行批量管理",
                "6. 新增入湖数据统计功能，支持对入湖数据进行统计",
                "7. 新增库表挂接率功能，支持对目录挂接情况进行统计分析"
            ]
        }
    }
    ```
  - 错误响应：
    ```json
    {
        "code": 500,
        "message": "获取版本信息失败",
        "data": null
    }
    ```

### 数据中台页面
- **GET** `/data_center_page`
  - 描述：数据中台页面路由
  - 请求头：
    ```
    Accept: text/html
    ```
  - 响应：渲染 data_center.html 模板
  - 模板变量：
    - `version`: 当前版本号
    - `show_version_notification`: 是否显示版本通知
    - `version_release_notes`: 版本更新说明

## 数据中台模块

### 表行数统计
- **POST** `/api/data_center/get_table_count`
  - 描述：获取单个表的记录数、注释、更新时间以及缓存状态。该接口会优先尝试从缓存中读取数据，如果缓存有效，则直接返回缓存数据；否则会实时统计并更新缓存。
  - 请求头：
    ```
    Content-Type: application/json
    Accept: application/json
    ```
  - 请求参数：
    ```json
    {
        "ip": "string",        // 数据库服务器IP
        "port": "number",      // 数据库端口
        "account": "string",   // 数据库账号
        "password": "string",  // 数据库密码
        "database": "string",  // 数据库名
        "table": "string"      // 表名
    }
    ```
  - 响应示例 (`data` 部分)：
    ```json
    {
        "count": 12345,
        "comment": "示例表注释",
        "update_time": "2025-01-15 10:30:00", // 源数据表的最后更新时间 (来自INFORMATION_SCHEMA.TABLES的UPDATE_TIME)
        "cache_status": "有效缓存", // 可能的值: "有效缓存", "已更新", "未缓存", "状态变更", "处理错误"
        "cache_time": "2025-01-15 10:25:00",  // 本条缓存记录的最后计算或更新时间
        "cache_details": {
            "is_cached": true,
            "cache_age": "5分钟",
            "source": "cache" // 可能的值: "cache", "live", "error"
        }
    }
    ```
  - 错误响应：
    ```json
    {
        "code": 400, // 或其他相关错误码
        "message": "请选择一个具体的表", // 或其他错误信息
        "data": null
    }
    ```

- **POST** `/api/data_center/get_all_tables_count`
  - 描述：高性能获取指定数据库中所有表的记录数、注释、更新时间及缓存状态。接口会自动利用缓存机制，对未发生变更的表直接使用缓存数据，从而显著提升大规模数据库的统计速度。
  - 请求头：
    ```
    Content-Type: application/json
    Accept: application/json
    ```
  - 请求参数：
    ```json
    {
        "ip": "string",        // 数据库服务器IP
        "port": "number",      // 数据库端口
        "account": "string",   // 数据库账号
        "password": "string",  // 数据库密码
        "database": "string"   // 数据库名
    }
    ```
  - 响应示例 (`data` 部分)：
    ```json
    {
        "tables": [
            {
                "name": "table1",
                "comment": "表1的注释",
                "count": 1000,
                "update_time": "2025-01-10 08:00:00", // 源表更新时间
                "cache_status": "有效缓存",
                "cache_time": "2025-01-15 09:00:00",  // 缓存的last_calculated_at
                "cache_details": {
                    "is_cached": true,
                    "cache_age": "6小时",
                    "source": "cache"
                }
            },
            {
                "name": "table2",
                "comment": "表2的注释",
                "count": 20000,
                "update_time": "2025-01-14 12:00:00", // 源表更新时间
                "cache_status": "已更新", // 表示该表重新计算了
                "cache_time": "2025-01-15 09:05:00",  // 缓存的last_calculated_at
                "cache_details": {
                    "is_cached": true,
                    "cache_age": "5分钟",
                    "source": "live"
                }
            }
        ],
        "total_count": 21000, // 所有表的总行数
        "execution_time": "0.85秒", // 本次API执行耗时
        "cache_hits": 1, // 命中缓存的表数量
        "total_tables": 2, // 本次查询的总表数量
        "cache_summary": {
            "hit_rate": "50%", // 缓存命中率
            "avg_response_time": "0.42秒", // 平均响应时间
            "cache_efficiency": "高" // 缓存效率评估：高/中/低
        }
    }
    ```

### 缓存管理
- **GET** `/api/data_center/cache_stats`
  - 描述：获取表行数缓存的统计信息
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "total_cached_tables": 100,
            "updated_last_24h": 50,
            "database_distribution": [
                {
                    "database": "db1",
                    "table_count": 30,
                    "cache_hit_rate": "85%",
                    "avg_cache_age": "2.5小时"
                },
                {
                    "database": "db2",
                    "table_count": 70,
                    "cache_hit_rate": "92%",
                    "avg_cache_age": "1.8小时"
                }
            ],
            "oldest_cache": "2024-03-19T10:00:00Z",
            "newest_cache": "2024-03-20T10:00:00Z",
            "estimated_space": "1.5MB",
            "performance_metrics": {
                "avg_response_time": "0.3秒",
                "cache_hit_rate": "88%",
                "memory_usage": "45MB",
                "cache_efficiency_score": 0.92
            }
        }
    }
    ```

- **POST** `/api/data_center/clear_cache`
  - 描述：清理表行数缓存
  - 请求头：
    ```
    Content-Type: application/json
    Accept: application/json
    ```
  - 请求参数：
    ```json
    {
        "database": "string",        // 可选，指定清理特定数据库的缓存
        "older_than_days": "number", // 可选，清理特定天数以前的缓存
        "force": false              // 可选，是否强制清理所有缓存，默认false
    }
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "缓存清理成功",
        "data": {
            "cleared_entries": 25,
            "affected_databases": ["db1", "db2"],
            "space_freed": "500KB",
            "execution_time": "0.2秒"
        }
    }
    ```

## 区县数据模块

### 入湖数据管理
- **GET** `/county/data/entry`
  - 描述：数据入湖主页面
  - 请求头：
    ```
    Accept: text/html
    ```
  - 响应：渲染数据入湖主页面
  - 模板变量：
    - `counties`: 区县列表
    - `providers`: 数据提供方列表
    - `tables`: 数据表列表
    - `filters`: 筛选条件

- **POST** `/county/data/entry_data_add_record`
  - 描述：添加入湖数据记录
  - 权限：需要编辑权限
  - 请求头：
    ```
    Content-Type: application/json
    Accept: application/json
    ```
  - 请求参数：
    ```json
    {
        "county": "string",           // 区县名称
        "provider": "string",         // 数据提供方
        "table_name": "string",       // 表名
        "data": {                     // 数据记录
            "field1": "value1",
            "field2": "value2"
        },
        "metadata": {                 // 元数据
            "source": "string",       // 数据来源
            "update_frequency": "string", // 更新频率
            "description": "string"   // 数据描述
        }
    }
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "record_id": 123,
            "created_at": "2024-03-20T10:00:00Z",
            "validation_result": {
                "is_valid": true,
                "warnings": []
            }
        }
    }
    ```

- **POST** `/county/data/data_import`
  - 描述：Excel批量导入数据
  - 权限：需要编辑权限
  - 请求头：
    ```
    Content-Type: multipart/form-data
    Accept: application/json
    ```
  - 请求参数：
    ```
    file: File              // Excel文件
    county: string          // 区县名称
    provider: string        // 数据提供方
    table_name: string      // 表名
    metadata: object        // 元数据（可选）
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "task_id": "uuid",
            "total_records": 1000,
            "valid_records": 950,
            "invalid_records": 50,
            "warnings": [
                {
                    "row": 10,
                    "message": "数据格式不正确"
                }
            ],
            "estimated_time": "5分钟"
        }
    }
    ```

- **GET** `/county/data/import_status/<task_id>`
  - 描述：获取导入任务状态
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "task_id": "uuid",
            "status": "processing",  // processing, completed, failed
            "progress": 75,          // 进度百分比
            "processed_records": 750,
            "total_records": 1000,
            "success_count": 700,
            "error_count": 50,
            "start_time": "2024-03-20T10:00:00Z",
            "estimated_end_time": "2024-03-20T10:05:00Z"
        }
    }
    ```

- **GET** `/county/data/import_history`
  - 描述：获取导入历史记录
  - 请求头：
    ```
    Accept: application/json
    ```
  - 请求参数：
    ```
    start_date: string    // 开始日期（可选）
    end_date: string      // 结束日期（可选）
    county: string        // 区县（可选）
    provider: string      // 数据提供方（可选）
    page: number         // 页码（可选，默认1）
    per_page: number     // 每页记录数（可选，默认20）
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "history": [
                {
                    "task_id": "uuid",
                    "county": "秦安县",
                    "provider": "统计局",
                    "table_name": "人口数据",
                    "total_records": 1000,
                    "success_count": 950,
                    "error_count": 50,
                    "start_time": "2024-03-20T10:00:00Z",
                    "end_time": "2024-03-20T10:05:00Z",
                    "status": "completed",
                    "operator": "user1"
                }
            ],
            "pagination": {
                "total": 100,
                "page": 1,
                "per_page": 20,
                "total_pages": 5
            }
        }
    }
    ```

- **GET** `/county/data/validation_rules`
  - 描述：获取数据验证规则
  - 请求头：
    ```
    Accept: application/json
    ```
  - 请求参数：
    ```
    table_name: string    // 表名
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "rules": [
                {
                    "field": "population",
                    "type": "number",
                    "required": true,
                    "min": 0,
                    "max": 1000000
                },
                {
                    "field": "name",
                    "type": "string",
                    "required": true,
                    "max_length": 50
                }
            ]
        }
    }
    ```

- **POST** `/county/data/validate`
  - 描述：验证数据
  - 请求头：
    ```
    Content-Type: application/json
    Accept: application/json
    ```
  - 请求参数：
    ```json
    {
        "table_name": "string",
        "data": {
            "field1": "value1",
            "field2": "value2"
        }
    }
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "is_valid": true,
            "errors": [],
            "warnings": [
                {
                    "field": "population",
                    "message": "数值超出正常范围"
                }
            ]
        }
    }
    ```

### 数据管理
- **POST** `/data/records`
  - 描述：添加新记录
  - 权限：需要编辑权限
  - 请求头：
    ```
    Content-Type: application/json
    Accept: application/json
    ```
  - 请求参数：
    ```json
    {
        "table_name": "string",    // 表名
        "data": {                  // 记录数据
            "field1": "value1",
            "field2": "value2"
        }
    }
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "record_id": 123,
            "created_at": "2024-03-20T10:00:00Z"
        }
    }
    ```

- **PUT** `/data/records/<record_id>`
  - 描述：更新记录
  - 权限：需要编辑权限
  - 请求头：
    ```
    Content-Type: application/json
    Accept: application/json
    ```
  - 请求参数：
    ```json
    {
        "table_name": "string",    // 表名
        "data": {                  // 更新数据
            "field1": "new_value1",
            "field2": "new_value2"
        }
    }
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "updated_at": "2024-03-20T10:00:00Z"
        }
    }
    ```

- **DELETE** `/data/records/<record_id>`
  - 描述：删除记录
  - 权限：需要编辑权限
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "deleted_at": "2024-03-20T10:00:00Z"
        }
    }
    ```

- **GET** `/data/records/<record_id>`
  - 描述：获取单条记录
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "id": 123,
            "field1": "value1",
            "field2": "value2",
            "created_at": "2024-03-20T10:00:00Z",
            "updated_at": "2024-03-20T10:00:00Z"
        }
    }
    ```

### 数据导出
- **GET** `/exports/all`
  - 描述：导出所有数据
  - 权限：需要编辑权限
  - 请求头：
    ```
    Accept: application/vnd.ms-excel
    ```
  - 响应：Excel文件下载
  - 响应头：
    ```
    Content-Type: application/vnd.ms-excel
    Content-Disposition: attachment; filename=export_all.xlsx
    ```

- **GET** `/exports/current`
  - 描述：导出当前筛选/表格数据
  - 权限：需要编辑权限
  - 请求头：
    ```
    Accept: application/vnd.ms-excel
    ```
  - 请求参数：
    ```
    table_name: string    // 表名
    filters: object      // 筛选条件
    ```
  - 响应：Excel文件下载
  - 响应头：
    ```
    Content-Type: application/vnd.ms-excel
    Content-Disposition: attachment; filename=export_current.xlsx
    ```

### 区县统计
- **GET** `/api/county_summary/counties`
  - 描述：获取所有区县列表（表名和显示名称）
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "counties": [
                {
                    "table_name": "qin_an",
                    "display_name": "秦安县",
                    "record_count": 1000
                },
                {
                    "table_name": "zhang_jia_chuan",
                    "display_name": "张家川县",
                    "record_count": 2000
                }
            ]
        }
    }
    ```

- **GET** `/api/county_summary/resource_usage`
  - 描述：获取数据资源申请利用率数据
  - 请求头：
    ```
    Accept: application/json
    ```
  - 请求参数：
    ```
    periodOffset: string  // 周期偏移（默认：'current'）
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "usage_stats": {
                "total_applications": 100,
                "approved_applications": 80,
                "rejected_applications": 20,
                "pending_applications": 0,
                "approval_rate": 0.8
            },
            "period": {
                "start": "2024-03-01",
                "end": "2024-03-20"
            }
        }
    }
    ```

## MySQL审计模块

### 用户活动
- **GET** `/api/user_activities`
  - 描述：获取用户活动记录
  - 请求头：
    ```
    Accept: application/json
    ```
  - 请求参数：
    ```
    start_date: string    // 开始日期（可选）
    end_date: string      // 结束日期（可选）
    page: number         // 页码（可选，默认1）
    per_page: number     // 每页记录数（可选，默认20）
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "activities": [
                {
                    "id": 1,
                    "user_name": "user1",
                    "operation_type": "SELECT",
                    "table_name": "table1",
                    "timestamp": "2024-03-20T10:00:00Z",
                    "risk_level": "LOW"
                }
            ],
            "pagination": {
                "total": 100,
                "page": 1,
                "per_page": 20,
                "total_pages": 5
            }
        }
    }
    ```

- **GET** `/api/operation_stats`
  - 描述：获取操作统计信息
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "total_operations": 1000,
            "operation_types": {
                "SELECT": 800,
                "INSERT": 100,
                "UPDATE": 50,
                "DELETE": 50
            },
            "risk_levels": {
                "HIGH": 10,
                "MEDIUM": 40,
                "LOW": 950
            },
            "time_distribution": {
                "last_24h": 100,
                "last_7d": 500,
                "last_30d": 1000
            }
        }
    }
    ```

### 服务器管理
- **GET** `/api/servers`
  - 描述：获取所有服务器列表
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "servers": [
                {
                    "id": 1,
                    "hostname": "server1",
                    "ip_address": "***********",
                    "status": "active",
                    "last_audit": "2024-03-20T10:00:00Z"
                }
            ]
        }
    }
    ```

- **GET** `/api/servers/<server_id>`
  - 描述：获取单个服务器信息
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "id": 1,
            "hostname": "server1",
            "ip_address": "***********",
            "status": "active",
            "config": {
                "mysql_version": "8.0",
                "max_connections": 1000,
                "audit_log_enabled": true
            },
            "last_audit": "2024-03-20T10:00:00Z"
        }
    }
    ```

- **POST** `/api/servers`
  - 描述：添加新服务器
  - 请求头：
    ```
    Content-Type: application/json
    Accept: application/json
    ```
  - 请求参数：
    ```json
    {
        "hostname": "string",
        "ip_address": "string",
        "mysql_version": "string",
        "max_connections": "number",
        "audit_log_enabled": "boolean"
    }
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "id": 1,
            "created_at": "2024-03-20T10:00:00Z"
        }
    }
    ```

- **PUT** `/api/servers/<server_id>`
  - 描述：更新服务器信息
  - 请求头：
    ```
    Content-Type: application/json
    Accept: application/json
    ```
  - 请求参数：
    ```json
    {
        "hostname": "string",
        "ip_address": "string",
        "mysql_version": "string",
        "max_connections": "number",
        "audit_log_enabled": "boolean"
    }
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "updated_at": "2024-03-20T10:00:00Z"
        }
    }
    ```

- **DELETE** `/api/servers/<server_id>`
  - 描述：删除服务器
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "deleted_at": "2024-03-20T10:00:00Z"
        }
    }
    ```

## Ansible自动化模块

### 服务器管理
- **GET** `/api/servers/<server_id>`
  - 描述：获取单个服务器信息
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "id": "number",
            "hostname": "string",
            "ip_address": "string",
            "ssh_port": "number",
            "description": "string",
            "group_name": "string",
            "group_id": "number",
            "status": "string",
            "last_connection": "datetime"
        }
    }
    ```

- **PUT** `/api/servers/<server_id>`
  - 描述：更新服务器信息
  - 请求头：
    ```
    Content-Type: application/json
    Accept: application/json
    ```
  - 请求参数：
    ```json
    {
        "hostname": "string",
        "ip_address": "string",
        "ssh_port": "number",
        "group_id": "number",
        "description": "string"
    }
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "updated_at": "2024-03-20T10:00:00Z"
        }
    }
    ```

- **DELETE** `/api/servers/<server_id>`
  - 描述：删除服务器
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "deleted_at": "2024-03-20T10:00:00Z"
        }
    }
    ```

## 服务器管理模块

### 服务器组管理
- **GET** `/api/server_groups`
  - 描述：获取所有服务器组
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "groups": [
                {
                    "id": 1,
                    "name": "group1",
                    "description": "description1",
                    "server_count": 5
                }
            ]
        }
    }
    ```

- **POST** `/api/server_groups`
  - 描述：创建新的服务器组
  - 请求头：
    ```
    Content-Type: application/json
    Accept: application/json
    ```
  - 请求参数：
    ```json
    {
        "name": "string",
        "description": "string"
    }
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "id": 1,
            "created_at": "2024-03-20T10:00:00Z"
        }
    }
    ```

## 数据展示模块

### 数据可视化
- **GET** `/visualization`
  - 描述：数据可视化页面
  - 请求头：
    ```
    Accept: text/html
    ```
  - 响应：渲染 visualization.html 模板
  - 模板变量：
    - `charts`: 图表配置
    - `data`: 图表数据

- **GET** `/api/visualization/charts`
  - 描述：获取图表配置和数据
  - 请求头：
    ```
    Accept: application/json
    ```
  - 请求参数：
    ```
    chart_type: string     // 图表类型（line, bar, pie等）
    data_type: string     // 数据类型
    time_range: string    // 时间范围
    filters: object       // 筛选条件
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "chart_config": {
                "type": "line",
                "title": "人口增长趋势",
                "x_axis": {
                    "type": "time",
                    "label": "时间"
                },
                "y_axis": {
                    "type": "value",
                    "label": "人口数"
                }
            },
            "series": [
                {
                    "name": "秦安县",
                    "data": [
                        ["2024-01", 1000],
                        ["2024-02", 1200],
                        ["2024-03", 1500]
                    ]
                }
            ]
        }
    }
    ```

### 数据展示
- **GET** `/data_display`
  - 描述：数据展示页面
  - 请求头：
    ```
    Accept: text/html
    ```
  - 响应：渲染 data_display.html 模板
  - 模板变量：
    - `tables`: 表格配置
    - `data`: 表格数据

- **GET** `/api/data_display/tables`
  - 描述：获取表格数据
  - 请求头：
    ```
    Accept: application/json
    ```
  - 请求参数：
    ```
    table_name: string    // 表名
    page: number         // 页码
    per_page: number     // 每页记录数
    sort_by: string      // 排序字段
    sort_order: string   // 排序方向（asc/desc）
    filters: object      // 筛选条件
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "columns": [
                {
                    "field": "name",
                    "title": "名称",
                    "type": "string"
                },
                {
                    "field": "value",
                    "title": "数值",
                    "type": "number"
                }
            ],
            "rows": [
                {
                    "id": 1,
                    "name": "指标1",
                    "value": 100
                }
            ],
            "pagination": {
                "total": 100,
                "page": 1,
                "per_page": 20,
                "total_pages": 5
            }
        }
    }
    ```

- **GET** `/api/data_display/statistics`
  - 描述：获取统计数据
  - 请求头：
    ```
    Accept: application/json
    ```
  - 请求参数：
    ```
    metric: string       // 统计指标
    time_range: string   // 时间范围
    group_by: string     // 分组字段
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "total": 1000,
            "average": 100,
            "max": 200,
            "min": 50,
            "distribution": [
                {
                    "group": "组1",
                    "value": 300
                },
                {
                    "group": "组2",
                    "value": 700
                }
            ]
        }
    }
    ```

### 库表挂接率
- **GET** `/table_connection_rate`
  - 描述：库表挂接率统计页面
  - 请求头：
    ```
    Accept: text/html
    ```
  - 响应：渲染 table_connection_rate.html 模板
  - 模板变量：
    - `stats`: 挂接率统计数据
    - `tables`: 表信息

- **GET** `/api/table_connection_rate/stats`
  - 描述：获取挂接率统计数据
  - 请求头：
    ```
    Accept: application/json
    ```
  - 请求参数：
    ```
    time_range: string   // 时间范围
    group_by: string     // 分组字段（可选）
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "total_tables": 100,
            "connected_tables": 80,
            "connection_rate": 0.8,
            "by_database": [
                {
                    "database": "db1",
                    "total": 50,
                    "connected": 40,
                    "rate": 0.8
                }
            ],
            "by_type": [
                {
                    "type": "基础数据",
                    "total": 30,
                    "connected": 25,
                    "rate": 0.83
                }
            ],
            "trend": [
                {
                    "date": "2024-01",
                    "rate": 0.7
                },
                {
                    "date": "2024-02",
                    "rate": 0.75
                },
                {
                    "date": "2024-03",
                    "rate": 0.8
                }
            ]
        }
    }
    ```

### 指标统计
- **GET** `/metrics_stats`
  - 描述：指标统计分析页面
  - 请求头：
    ```
    Accept: text/html
    ```
  - 响应：渲染 metrics_stats.html 模板
  - 模板变量：
    - `metrics`: 指标配置
    - `data`: 统计数据

- **GET** `/api/metrics_stats/indicators`
  - 描述：获取指标列表
  - 请求头：
    ```
    Accept: application/json
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "indicators": [
                {
                    "id": "pop_growth",
                    "name": "人口增长率",
                    "description": "年度人口增长百分比",
                    "unit": "%",
                    "category": "人口指标",
                    "formula": "((current_year - last_year) / last_year) * 100",
                    "weight": 0.3
                }
            ]
        }
    }
    ```

- **GET** `/api/metrics_stats/calculate`
  - 描述：计算指标值
  - 请求头：
    ```
    Accept: application/json
    ```
  - 请求参数：
    ```
    indicator_id: string  // 指标ID
    time_range: string   // 时间范围
    region: string       // 地区
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "indicator": {
                "id": "pop_growth",
                "name": "人口增长率"
            },
            "value": 2.5,
            "unit": "%",
            "time_series": [
                {
                    "date": "2024-01",
                    "value": 2.3
                },
                {
                    "date": "2024-02",
                    "value": 2.4
                },
                {
                    "date": "2024-03",
                    "value": 2.5
                }
            ],
            "comparison": {
                "average": 2.4,
                "max": 2.8,
                "min": 2.1,
                "rank": 3
            }
        }
    }
    ```

- **GET** `/api/metrics_stats/rankings`
  - 描述：获取指标排名
  - 请求头：
    ```
    Accept: application/json
    ```
  - 请求参数：
    ```
    indicator_id: string  // 指标ID
    time_range: string   // 时间范围
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "indicator": {
                "id": "pop_growth",
                "name": "人口增长率"
            },
            "rankings": [
                {
                    "region": "秦安县",
                    "value": 2.5,
                    "rank": 1,
                    "change": 1
                },
                {
                    "region": "张家川县",
                    "value": 2.3,
                    "rank": 2,
                    "change": -1
                }
            ],
            "time_range": {
                "start": "2024-01",
                "end": "2024-03"
            }
        }
    }
    ```

- **GET** `/api/metrics_stats/comparison`
  - 描述：获取指标对比数据
  - 请求头：
    ```
    Accept: application/json
    ```
  - 请求参数：
    ```
    indicator_ids: string[]  // 指标ID列表
    regions: string[]       // 地区列表
    time_range: string      // 时间范围
    ```
  - 响应示例：
    ```json
    {
        "code": 200,
        "message": "success",
        "data": {
            "indicators": [
                {
                    "id": "pop_growth",
                    "name": "人口增长率"
                },
                {
                    "id": "gdp_growth",
                    "name": "GDP增长率"
                }
            ],
            "regions": ["秦安县", "张家川县"],
            "comparison": [
                {
                    "region": "秦安县",
                    "values": {
                        "pop_growth": 2.5,
                        "gdp_growth": 5.2
                    }
                },
                {
                    "region": "张家川县",
                    "values": {
                        "pop_growth": 2.3,
                        "gdp_growth": 4.8
                    }
                }
            ],
            "time_range": {
                "start": "2024-01",
                "end": "2024-03"
            }
        }
    }
    ```

## 权限说明

以下路由需要编辑权限：
- `/exports/current`
- `/exports/all`
- `/county/data/entry/add_record_page`
- `/data/records`
- `/data_center`
- `/mysql_audit`
- `/ansible`

权限验证流程：
1. 访问需要权限的接口时，会先检查session中是否有验证状态
2. 如果没有验证状态，会重定向到密码验证页面
3. 验证成功后，会在session中保存验证状态
4. 验证状态在会话期间有效

## 注意事项

1. 所有需要编辑权限的接口在访问前会进行密码验证
2. 文件上传大小限制为 10GB
3. 数据库连接池配置：
   - 池大小：10
   - 最大溢出：20
   - 超时时间：30秒
   - 回收时间：1800秒
4. 所有时间戳使用ISO 8601格式
5. 所有API响应均为JSON格式，除非特别说明（如文件下载）
6. 分页接口默认每页20条记录
7. 所有错误响应都包含错误码和错误信息 