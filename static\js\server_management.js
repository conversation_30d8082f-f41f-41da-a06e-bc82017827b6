/**
 * 服务器管理系统前端JavaScript
 */

$(document).ready(function() {
    // 加载平台列表
    loadPlatforms();
    
    // 加载服务器数据
    loadServerData();
    
    // 加载端口数据
    loadPortData();
    
    // 左侧平台菜单点击事件
    $(document).on('click', '#platformList a', function(e) {
        e.preventDefault();
        
        // 更新选中状态
        $('#platformList a').removeClass('active');
        $(this).addClass('active');
        
        // 获取选中的平台
        const platform = $(this).data('platform');
        
        // 筛选服务器列表
        filterServersByPlatform(platform);
    });
    
    // 导航切换
    $('.nav-link').click(function(e) {
        e.preventDefault();
        const type = $(this).data('type');
        
        // 更新导航状态
        $('.nav-link').removeClass('active');
        $(this).addClass('active');
        
        // 显示对应的内容
        if (type === 'server') {
            $('#portManagement').hide();
            $('#serverManagement').show();
            $('.platform-nav').show();
        } else if (type === 'port') {
            $('#serverManagement').hide();
            $('#portManagement').show();
            $('.platform-nav').hide();
        }
    });

    // 服务详情按钮点击事件
    $(document).on('click', '.view-details-btn', function() {
        const components = $(this).data('components');
        const ip = $(this).data('ip');
        
        let detailsHtml = `<h5>服务器 ${ip} 部署的服务</h5>`;
        if (components) {
            const componentList = components.split(',');
            detailsHtml += '<ul>';
            componentList.forEach(comp => {
                if (comp.trim()) {
                    detailsHtml += `<li>${comp.trim()}</li>`;
                }
            });
            detailsHtml += '</ul>';
        } else {
            detailsHtml += '<p>没有部署服务信息</p>';
        }
        
        $('#detailsContent').html(detailsHtml);
        const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
        modal.show();
    });

    // 保存服务器编辑
    $('#saveServerEdit').click(function() {
        const serverData = {
            ip: $('#edit_server_ip').val(),
            hostname: $('#edit_hostname').val(),
            cpu: $('#edit_cpu').val(),
            memory: $('#edit_memory').val(),
            disk: $('#edit_disk').val(),
            os_system: $('#edit_os_system').val(),
            deploy_components: $('#edit_deploy_components').val(),
        };

        // 验证必填字段
        if (!serverData.ip) {
            alert('IP地址不能为空');
            return;
        }

        // 发送更新请求
        $.ajax({
            url: '/api/server',
            method: 'PUT',
            data: JSON.stringify(serverData),
            contentType: 'application/json',
            success: function(response) {
                if (response.success) {
                    $('#editServerModal').modal('hide');
                    loadServerData();
                } else {
                    alert('更新失败：' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error updating server:', error);
                alert('更新失败，请重试');
            }
        });
    });

    // 添加服务器按钮点击事件
    $('#saveServerBtn').click(function() {
        const serverData = {
            ip: $('#ip').val(),
            hostname: $('#hostname').val(),
            cpu: $('#cpu').val(),
            memory: $('#memory').val(),
            disk: $('#disk').val(),
            platform: $('#platform').val(),
            os_system: $('#os_system').val(),
            deploy_components: $('#deploy_components').val()
        };

        // 验证必填字段
        if (!serverData.ip) {
            alert('IP地址不能为空');
            return;
        }

        // 发送添加请求
        $.ajax({
            url: '/api/server',
            method: 'POST',
            data: JSON.stringify(serverData),
            contentType: 'application/json',
            success: function(response) {
                if (response.success) {
                    // 关闭模态框
                    $('#addServerModal').modal('hide');
                    
                    // 清空表单
                    $('#addServerForm')[0].reset();
                    
                    // 重新加载服务器数据
                    loadServerData();
                } else {
                    alert('添加失败：' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error adding server:', error);
                alert('添加失败，请重试');
            }
        });
    });

    // 添加端口按钮点击事件
    $('#savePortBtn').click(function() {
        const portData = {
            ip: $('#port_ip').val(),
            app_port: $('#app_port').val(),
            dubbo_port: $('#dubbo_port').val(),
            xxl_job_port: $('#xxl_job_port').val()
        };

        // 验证必填字段
        if (!portData.ip) {
            alert('IP地址不能为空');
            return;
        }

        // 发送添加请求
        $.ajax({
            url: '/api/port',
            method: 'POST',
            data: JSON.stringify(portData),
            contentType: 'application/json',
            success: function(response) {
                if (response.success) {
                    // 关闭模态框
                    $('#addPortModal').modal('hide');
                    
                    // 清空表单
                    $('#addPortForm')[0].reset();
                    
                    // 重新加载端口数据
                    loadPortData();
                } else {
                    alert('添加失败：' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error adding port:', error);
                alert('添加失败，请重试');
            }
        });
    });

    // 服务详情按钮点击事件
    $(document).on('click', '.view-details-btn', function() {
        const components = $(this).data('components');
        const ip = $(this).data('ip');
        
        let detailsHtml = `<h5>服务器 ${ip} 部署的服务</h5>`;
        if (components) {
            const componentList = components.split(',');
            detailsHtml += '<ul>';
            componentList.forEach(comp => {
                if (comp.trim()) {
                    detailsHtml += `<li>${comp.trim()}</li>`;
                }
            });
            detailsHtml += '</ul>';
        } else {
            detailsHtml += '<p>没有部署服务信息</p>';
        }
        
        $('#detailsContent').html(detailsHtml);
        const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
        modal.show();
    });

    // 保存服务器编辑
    $('#saveServerEdit').click(function() {
        const serverData = {
            ip: $('#edit_server_ip').val(),
            hostname: $('#edit_hostname').val(),
            cpu: $('#edit_cpu').val(),
            memory: $('#edit_memory').val(),
            disk: $('#edit_disk').val(),
            os_system: $('#edit_os_system').val(),
            deploy_components: $('#edit_deploy_components').val(),
        };

        // 验证必填字段
        if (!serverData.ip) {
            alert('IP地址不能为空');
            return;
        }

        // 发送更新请求
        $.ajax({
            url: '/api/server',
            method: 'PUT',
            data: JSON.stringify(serverData),
            contentType: 'application/json',
            success: function(response) {
                if (response.success) {
                    $('#editServerModal').modal('hide');
                    loadServerData();
                } else {
                    alert('更新失败：' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Error updating server:', error);
                alert('更新失败，请重试');
            }
        });
    });
});

// 加载平台列表
function loadPlatforms() {
    $.get('/api/platforms', function(response) {
        if (response.success) {
            let platformsHtml = '<a href="#" class="list-group-item list-group-item-action active" data-platform="all">全部平台</a>';
            
            response.data.forEach(platform => {
                if (platform) {
                    platformsHtml += `<a href="#" class="list-group-item list-group-item-action" data-platform="${platform}">${platform}</a>`;
                }
            });
            
            $('#platformList').html(platformsHtml);
            
            // 确保默认显示全部平台
            filterServersByPlatform('all');
            
            // 确保平台导航在服务器管理页面显示
            $('.platform-nav').show();
            
            // 默认选中服务器管理
            $('.nav-link[data-type="server"]').addClass('active');
            $('#serverManagement').show();
            $('#portManagement').hide();
        }
    });
}

// 根据平台筛选服务器
function filterServersByPlatform(platform) {
    if (platform === 'all') {
        $('#serverTableBody tr').show();
    } else {
        $('#serverTableBody tr').hide();
        $('#serverTableBody tr').each(function() {
            const rowPlatform = $(this).data('platform');
            if (rowPlatform === platform) {
                $(this).show();
            }
        });
    }
}

// 加载服务器数据
function loadServerData() {
    $.get('/api/server/list', function(response) {
        const servers = response.servers;
        let html = '';
        
        servers.forEach(server => {
            html += `
                <tr data-platform="${server.platform || '未分类'}">
                    <td class="text-center">${server.ip || '-'}</td>
                    <td class="text-center">${server.hostname || '-'}</td>
                    <td class="text-center">${server.cpu || '-'}</td>
                    <td class="text-center">${server.memory || '-'}</td>
                    <td class="text-center">${server.disk || '-'}</td>
                    <td class="text-center">
                        <span class="action-link view-usage" 
                              data-ip="${server.ip}" 
                              style="color: blue; cursor: pointer;">
                            详情
                        </span>
                    </td>
                    <td class="text-center">
                        <span class="action-link view-directory" 
                              data-ip="${server.ip}" 
                              style="color: blue; cursor: pointer;">
                            详情
                        </span>
                    </td>
                    <td class="text-center">${server.os_system || '-'}</td>
                    <td class="text-center">
                        <span class="action-link view-components" 
                              data-components="${server.deploy_components || ''}" 
                              data-ip="${server.ip}" 
                              style="color: blue; cursor: pointer;">
                            详情
                        </span>
                    </td>
                    <td class="text-center">
                        <span class="action-link edit-server" data-ip="${server.ip}" style="color: blue; cursor: pointer;">编辑</span>&nbsp;
                        <span class="action-link delete-server" data-ip="${server.ip}" style="color: red; cursor: pointer;">删除</span>
                    </td>
                </tr>
            `;
        });
        
        $('#serverTableBody').html(html);
        
        // 绑定服务器事件
        bindServerEvents();
        
        // 应用当前选中的平台筛选
        const selectedPlatform = $('#platformList a.active').data('platform');
        filterServersByPlatform(selectedPlatform);
        
        // 绑定服务器编辑事件
        $('.edit-server').click(function() {
            const ip = $(this).data('ip');
            const row = $(this).closest('tr');
            
            $('#edit_server_ip').val(ip);
            $('#edit_hostname').val(row.find('td:eq(1)').text().trim() === '-' ? '' : row.find('td:eq(1)').text());
            $('#edit_cpu').val(row.find('td:eq(2)').text().trim() === '-' ? '' : row.find('td:eq(2)').text());
            $('#edit_memory').val(row.find('td:eq(3)').text().trim() === '-' ? '' : row.find('td:eq(3)').text());
            $('#edit_disk').val(row.find('td:eq(4)').text().trim() === '-' ? '' : row.find('td:eq(4)').text());
            $('#edit_os_system').val(row.find('td:eq(7)').text().trim() === '-' ? '' : row.find('td:eq(7)').text());
            $('#edit_deploy_components').val(row.find('span.view-components').data('components') || '');
            
            $('#editServerModal').modal('show');
        });
        
        // 绑定服务器删除事件
        $('.delete-server').click(function() {
            const ip = $(this).data('ip');
            if (confirm('确定要删除该服务器吗？')) {
                $.ajax({
                    url: '/api/server',
                    method: 'DELETE',
                    data: JSON.stringify({ ip: ip }),
                    contentType: 'application/json',
                    success: function(response) {
                        if (response.success) {
                            loadServerData();
                        } else {
                            alert('删除失败：' + response.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Error deleting server:', error);
                        alert('删除失败，请重试');
                    }
                });
            }
        });
        
        // 注释掉这个，防止重复绑定事件
        /*
        // 绑定查看组件详情事件
        $('.view-components').click(function() {
            const components = $(this).data('components');
            const ip = $(this).data('ip');
            
            let detailContent = '<h6>IP: ' + ip + '</h6>';
            
            if (components && components.trim() !== '') {
                const componentList = components.split(',');
                detailContent += '<ul class="list-group mt-3">';
                
                componentList.forEach(component => {
                    if (component.trim() !== '') {
                        detailContent += `<li class="list-group-item">${component.trim()}</li>`;
                    }
                });
                
                detailContent += '</ul>';
            } else {
                detailContent += '<div class="alert alert-info mt-3">该服务器暂无部署组件数据</div>';
            }
            
            $('#componentDetailContent').html(detailContent);
            $('#componentDetailModal').modal('show');
        });
        */
        
        // 绑定查看目录树事件
        $('.view-directory').click(function() {
            const ip = $(this).data('ip');
            
            // 从directory_tree_json_metadata表获取目录树数据
            $.ajax({
                url: '/api/server/directory',
                method: 'GET',
                data: { ip: ip },
                success: function(response) {
                    if (response.success) {
                        const treeData = response.data;
                        
                        // 生成目录树HTML
                        let treeHtml = `<h6>IP: ${ip} 的目录结构</h6>`;
                        treeHtml += `<div class="directory-tree-container mt-3">`;
                        treeHtml += buildTreeFromJson(treeData);
                        treeHtml += `</div>`;
                        
                        $('#directoryTreeContent').html(treeHtml);
                        
                        // 绑定目录树点击事件
                        $('.tree-toggle').click(function(e) {
                            e.preventDefault();
                            const $this = $(this);
                            const $parent = $this.parent('li');
                            const $children = $parent.children('ul');
                            
                            if ($children.is(':visible')) {
                                $children.hide('fast');
                                $this.find('i').removeClass('fa-caret-down').addClass('fa-caret-right');
                            } else {
                                $children.show('fast');
                                $this.find('i').removeClass('fa-caret-right').addClass('fa-caret-down');
                            }
                        });
                    } else {
                        $('#directoryTreeContent').html(`<div class="alert alert-info">该服务器暂无目录树数据</div>`);
                    }
                    
                    $('#directoryTreeModal').modal('show');
                },
                error: function(xhr, status, error) {
                    // 检查是否是404错误（未找到）
                    if (xhr.status === 404) {
                        $('#directoryTreeContent').html(`<div class="alert alert-info">该服务器暂无目录树数据</div>`);
                    } else {
                        $('#directoryTreeContent').html(`<div class="alert alert-danger">获取目录树数据失败: ${error}</div>`);
                    }
                    
                    $('#directoryTreeModal').modal('show');
                }
            });
        });
    });
}

// 绑定服务器事件
function bindServerEvents() {
    // 先解绑事件，防止重复绑定
    $('.view-components').off('click');
    $('.view-directory').off('click');
    
    // 绑定查看组件详情事件
    $('.view-components').click(function() {
        // 防止多次打开模态框或与componentListModal冲突
        if ($('#componentDetailModal').hasClass('show') || $('#componentListModal').hasClass('show')) {
            return;
        }
        
        const components = $(this).data('components');
        const ip = $(this).data('ip');
        
        let detailContent = '<h6>IP: ' + ip + '</h6>';
        
        if (components && components.trim() !== '') {
            const componentList = components.split(',');
            detailContent += '<ul class="list-group mt-3">';
            
            componentList.forEach(component => {
                if (component.trim() !== '') {
                    detailContent += `<li class="list-group-item">${component.trim()}</li>`;
                }
            });
            
            detailContent += '</ul>';
        } else {
            detailContent += '<div class="alert alert-info mt-3">该服务器暂无部署组件数据</div>';
        }
        
        $('#componentDetailContent').html(detailContent);
        $('#componentDetailModal').modal('show');
    });
    
    // 绑定查看目录树事件
    $('.view-directory').click(function() {
        const ip = $(this).data('ip');
        
        // 从directory_tree_json_metadata表获取目录树数据
        $.ajax({
            url: '/api/server/directory',
            method: 'GET',
            data: { ip: ip },
            success: function(response) {
                if (response.success) {
                    const treeData = response.data;
                    
                    // 生成目录树HTML
                    let treeHtml = `<h6>IP: ${ip} 的目录结构</h6>`;
                    treeHtml += `<div class="directory-tree-container mt-3">`;
                    treeHtml += buildTreeFromJson(treeData);
                    treeHtml += `</div>`;
                    
                    $('#directoryTreeContent').html(treeHtml);
                    
                    // 绑定目录树点击事件
                    $('.tree-toggle').click(function(e) {
                        e.preventDefault();
                        const $this = $(this);
                        const $parent = $this.parent('li');
                        const $children = $parent.children('ul');
                        
                        if ($children.is(':visible')) {
                            $children.hide('fast');
                            $this.find('i').removeClass('fa-caret-down').addClass('fa-caret-right');
                        } else {
                            $children.show('fast');
                            $this.find('i').removeClass('fa-caret-right').addClass('fa-caret-down');
                        }
                    });
                } else {
                    $('#directoryTreeContent').html(`<div class="alert alert-info">该服务器暂无目录树数据</div>`);
                }
                
                $('#directoryTreeModal').modal('show');
            },
            error: function(xhr, status, error) {
                // 检查是否是404错误（未找到）
                if (xhr.status === 404) {
                    $('#directoryTreeContent').html(`<div class="alert alert-info">该服务器暂无目录树数据</div>`);
                } else {
                    $('#directoryTreeContent').html(`<div class="alert alert-danger">获取目录树数据失败: ${error}</div>`);
                }
                
                $('#directoryTreeModal').modal('show');
            }
        });
    });
}

// 从JSON构建目录树HTML
function buildTreeFromJson(jsonData) {
    if (!jsonData) {
        return '<div class="alert alert-info">目录数据为空</div>';
    }
    
    // 检查是否是path格式的数据
    if (Array.isArray(jsonData)) {
        if (jsonData.length > 0 && jsonData[0].path) {
            // 将path格式转换为树形结构
            return buildTreeFromPaths(jsonData);
        }
    } else if (typeof jsonData === 'string') {
        // 尝试解析JSON字符串
        try {
            const parsedData = JSON.parse(jsonData);
            return buildTreeFromJson(parsedData);
        } catch (e) {
            return `<div class="alert alert-danger">JSON解析错误: ${e}</div>`;
        }
    }
    
    // 原来的树形结构处理逻辑
    return buildTreeNode(jsonData);
}

// 从路径数组构建目录树
function buildTreeFromPaths(pathsArray) {
    if (!pathsArray || pathsArray.length === 0) {
        return '<div class="alert alert-info">目录数据为空</div>';
    }
    
    // 构建路径树
    const pathTree = {};
    
    pathsArray.forEach((item, index) => {
        if (!item.path) {
            return;
        }
        
        const pathParts = item.path.split('/').filter(part => part);
        
        let currentLevel = pathTree;
        
        pathParts.forEach((part, idx) => {
            if (!currentLevel[part]) {
                currentLevel[part] = {};
            }
            currentLevel = currentLevel[part];
        });
    });
    
    // 生成HTML
    return buildPathTreeHtml(pathTree);
}

// 构建路径树HTML
function buildPathTreeHtml(node, path = '') {
    if (!node || Object.keys(node).length === 0) return '';
    
    let html = '<ul class="tree" style="list-style-type: none; padding-left: 20px;">';
    
    Object.keys(node).forEach(key => {
        const currentPath = path ? `${path}/${key}` : `/${key}`;
        const hasChildren = Object.keys(node[key]).length > 0;
        
        html += `<li>`;
        if (hasChildren) {
            html += `<a href="#" class="tree-toggle">
                       <i class="fas fa-caret-right mr-1"></i>
                       <i class="fas fa-folder mr-1" style="color: #ffc107;"></i>
                       ${key}
                     </a>`;
            html += `<ul style="display: none; list-style-type: none; padding-left: 20px;">`;
            html += buildPathTreeHtml(node[key], currentPath);
            html += `</ul>`;
        } else {
            html += `<span>
                       <i class="fas fa-folder mr-1" style="color: #ffc107;"></i>
                       ${key}
                     </span>`;
        }
        html += `</li>`;
    });
    
    html += '</ul>';
    return html;
}

// 构建目录树节点
function buildTreeNode(node) {
    if (!node) return '';
    
    let html = '<ul class="tree" style="list-style-type: none; padding-left: 20px;">';
    
    // 处理根节点
    if (node.name && node.type) {
        const isDir = node.type === 'directory';
        const icon = isDir ? 'fa-folder' : 'fa-file';
        const hasChildren = node.children && node.children.length > 0;
        
        html += `<li>`;
        if (isDir && hasChildren) {
            html += `<a href="#" class="tree-toggle">
                       <i class="fas fa-caret-right mr-1"></i>
                       <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                       ${node.name}
                     </a>`;
            html += buildTreeNodeChildren(node.children);
        } else {
            html += `<span>
                       <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                       ${node.name}
                     </span>`;
        }
        html += `</li>`;
    } 
    // 处理根对象包含children的情况
    else if (node.children) {
        node.children.forEach(child => {
            const isDir = child.type === 'directory';
            const icon = isDir ? 'fa-folder' : 'fa-file';
            const hasChildren = child.children && child.children.length > 0;
            
            html += `<li>`;
            if (isDir && hasChildren) {
                html += `<a href="#" class="tree-toggle">
                           <i class="fas fa-caret-right mr-1"></i>
                           <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                           ${child.name}
                         </a>`;
                html += buildTreeNodeChildren(child.children);
            } else {
                html += `<span>
                           <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                           ${child.name}
                         </span>`;
            }
            html += `</li>`;
        });
    }
    
    html += '</ul>';
    return html;
}

// 构建子节点
function buildTreeNodeChildren(children) {
    if (!children || children.length === 0) return '';
    
    let html = '<ul style="display: none; list-style-type: none; padding-left: 20px;">';
    
    children.forEach(child => {
        const isDir = child.type === 'directory';
        const icon = isDir ? 'fa-folder' : 'fa-file';
        const hasChildren = child.children && child.children.length > 0;
        
        html += `<li>`;
        if (isDir && hasChildren) {
            html += `<a href="#" class="tree-toggle">
                       <i class="fas fa-caret-right mr-1"></i>
                       <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                       ${child.name}
                     </a>`;
            html += buildTreeNodeChildren(child.children);
        } else {
            html += `<span>
                       <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                       ${child.name}
                     </span>`;
        }
        html += `</li>`;
    });
    
    html += '</ul>';
    return html;
}

// 加载端口数据
function loadPortData() {
    $.get('/get_server_ports', function(response) {
        const serverPorts = response.server_ports;
        let html = '';
        
        serverPorts.forEach(port => {
            // 格式化端口显示，将多个端口用逗号分隔显示
            const formatPort = (portValue) => {
                if (portValue === null || portValue === undefined || portValue === '') return '-';
                // 确保转换为字符串
                return String(portValue).split(',').map(p => p.trim()).filter(p => p).join(', ');
            };

            html += `
                <tr>
                    <td class="text-center">${port.ip}</td>
                    <td class="text-center">${port.deploy_component || '-'}</td>
                    <td class="text-center">${formatPort(port.app_port)}</td>
                    <td class="text-center">${formatPort(port.dubbo_server_port)}</td>
                    <td class="text-center">${formatPort(port.xxl_job_port)}</td>
                    <td class="text-center">
                        <span class="action-link edit" data-ip="${port.ip}" style="color: blue; cursor: pointer;">编辑</span>&nbsp;
                        <span class="action-link delete" data-ip="${port.ip}" style="color: red; cursor: pointer;">删除</span>
                    </td>
                </tr>
            `;
        });
        
        $('#portTableBody').html(html);

        // 绑定端口事件
        bindPortEvents();
    });
}

// 绑定端口事件
function bindPortEvents() {
    // 绑定编辑事件
    $('#portTableBody .action-link.edit').click(function(e) {
        const ip = $(this).data('ip');
        const row = $(this).closest('tr');
        
        // 获取当前行的数据，并去除占位符
        $('#edit_port_ip').val(ip);
        $('#edit_deploy_component').val(row.find('td:eq(1)').text().trim() === '-' ? '' : row.find('td:eq(1)').text());
        $('#edit_app_port').val(row.find('td:eq(2)').text().trim() === '-' ? '' : row.find('td:eq(2)').text());
        $('#edit_dubbo_port').val(row.find('td:eq(3)').text().trim() === '-' ? '' : row.find('td:eq(3)').text());
        $('#edit_xxl_job_port').val(row.find('td:eq(4)').text().trim() === '-' ? '' : row.find('td:eq(4)').text());
        
        $('#editPortModal').modal('show');
    });

    // 绑定删除事件
    $('#portTableBody .action-link.delete').click(function(e) {
        const ip = $(this).data('ip');
        if (confirm('确定要删除该端口信息吗？')) {
            $.ajax({
                url: '/api/port',
                method: 'DELETE',
                data: JSON.stringify({ ip: ip }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.success) {
                        loadPortData();
                    } else {
                        alert('删除失败：' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error deleting port:', error);
                    alert('删除失败，请重试');
                }
            });
        }
    });
}

// 处理端口编辑表单提交
$('#editPortForm').on('submit', function(e) {
    e.preventDefault();
    
    // 获取并处理表单数据
    const formData = {
        ip: $('#edit_port_ip').val(),
        deploy_component: $('#edit_deploy_component').val().trim(),
        app_port: $('#edit_app_port').val().trim(),
        dubbo_server_port: $('#edit_dubbo_port').val().trim(),
        xxl_job_port: $('#edit_xxl_job_port').val().trim()
    };

    // 发送更新请求
    $.ajax({
        url: '/api/port',
        method: 'PUT',
        data: JSON.stringify(formData),
        contentType: 'application/json',
        success: function(response) {
            if (response.success) {
                $('#editPortModal').modal('hide');
                loadPortData();  // 重新加载端口数据
            } else {
                alert('更新失败：' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error updating port:', error);
            alert('更新失败，请重试');
        }
    });
});

// 端口输入框的事件处理
$('#edit_app_port, #edit_dubbo_port, #edit_xxl_job_port').on('input', function() {
    // 允许输入数字、逗号和空格
    $(this).val($(this).val().replace(/[^0-9,\s]/g, ''));
});

// 移除有问题的tooltip初始化
// $('[data-bs-toggle="tooltip"]').tooltip();

// 初始化加载数据
loadServerData();
loadPortData();

// 保存端口编辑
$('#savePortEdit').click(function() {
    const portData = {
        ip: $('#edit_port_ip').val(),
        deploy_component: $('#edit_deploy_component').val(),
        app_port: $('#edit_app_port').val(),
        dubbo_server_port: $('#edit_dubbo_port').val(),
        xxl_job_port: $('#edit_xxl_job_port').val()
    };

    // 验证必填字段
    for (let key in portData) {
        if (!portData[key] && portData[key] !== 0) {
            alert('请填写所有必填字段');
            return;
        }
    }

    $.ajax({
        url: '/api/port',
        method: 'PUT',
        data: JSON.stringify(portData),
        contentType: 'application/json',
        success: function(response) {
            if (response.success) {
                $('#editPortModal').modal('hide');
                loadPortData();
            } else {
                alert('更新失败：' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error updating port:', error);
            alert('更新失败，请重试');
        }
    });
});
