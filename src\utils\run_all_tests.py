#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一测试脚本
运行所有必要的测试：模块导入测试 + 配置验证
"""

import os
import sys

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

def run_import_tests():
    """运行模块导入测试"""
    print("🔍 第一步：模块导入测试")
    print("=" * 50)
    
    try:
        try:
            from .test_imports import test_imports, test_config_loading
        except ImportError:
            from src.utils.test_imports import test_imports, test_config_loading
        
        # 测试配置加载
        config_ok = test_config_loading()
        
        # 测试模块导入
        import_ok = test_imports()
        
        return config_ok and import_ok
        
    except ImportError as e:
        print(f"❌ 无法导入测试模块: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 导入测试失败: {str(e)}")
        return False

def run_config_validation():
    """运行配置验证"""
    print("\n🔧 第二步：配置验证")
    print("=" * 50)
    
    try:
        try:
            from .validate_config import main as validate_main
        except ImportError:
            from src.utils.validate_config import main as validate_main
        return validate_main()
        
    except ImportError as e:
        print(f"❌ 无法导入配置验证模块: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 配置验证失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 系统启动前测试")
    print("=" * 60)
    print("运行所有必要的测试以确保系统可以正常启动")
    print("=" * 60)
    
    # 第一步：模块导入测试
    import_ok = run_import_tests()
    
    # 第二步：配置验证
    if import_ok:
        config_ok = run_config_validation()
    else:
        print("\n⚠️  跳过配置验证，因为模块导入测试失败")
        config_ok = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("-" * 30)
    print(f"模块导入测试: {'✅ 通过' if import_ok else '❌ 失败'}")
    print(f"配置验证测试: {'✅ 通过' if config_ok else '❌ 失败'}")
    print("-" * 30)
    
    if import_ok and config_ok:
        print("🎉 所有测试通过！系统可以正常启动")
        print("\n🚀 下一步:")
        print("运行启动命令: python start.py")
        return True
    else:
        print("❌ 测试失败，请修复上述问题后重试")
        print("\n💡 建议:")
        if not import_ok:
            print("- 检查模块导入路径和依赖")
            print("- 确保在正确的目录运行脚本")
        if not config_ok:
            print("- 检查 .env 文件配置")
            print("- 确认所有必需配置都已设置")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
