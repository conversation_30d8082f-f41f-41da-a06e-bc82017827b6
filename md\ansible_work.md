# Ansible Web UI

基于Flask的Ansible可视化管理界面，通过跳板机实现对Ansible控制节点的管理。

## 功能特点

- 服务器管理
  - 添加、编辑和删除目标服务器
  - 服务器分组管理（创建、查看和删除分组）
  - 按分组查看服务器列表
  - 服务器状态监控

- 任务管理
  - 执行Ansible playbook
  - 执行ad-hoc命令
  - 任务执行历史记录
  - 任务执行结果实时展示
  - 任务详情查看
  - 任务删除功能
  - 格式化的任务执行结果展示

- Playbook管理
  - 创建、编辑和删除Playbook
  - YAML格式验证
  - 在任务中选择和使用Playbook
  - 自动处理无效路径，确保Playbook可用
  - 自动创建不存在的Playbook文件
  - 默认Playbook内容模板

- 文件管理
  - 浏览服务器文件系统
  - 上传文件（支持覆盖选项）
  - 下载单个或批量文件
  - 文件认证管理
  - 文件操作错误处理

- 用户界面优化
  - 现代化的Toast通知系统，替代原生alert
  - 自定义确认对话框，替代原生confirm
  - 符合ARIA可访问性标准的UI组件
  - 内网环境下的本地静态资源

- SSH跳转
  - 通过************跳转到***********
  - 支持密码和密钥认证
  - 连接池管理
  - 会话超时控制

## 系统要求

- Python 3.10+
- MySQL 5.7.36+
- Ansible 2.9+
- 操作系统：Windows/Linux
- 现代浏览器（支持Bootstrap 5和ES6）

## 环境准备

1. 跳板机配置
   - IP: ************
   - 端口: 6233
   - 确保跳板机可访问

2. Ansible控制节点配置
   - IP: ***********
   - 端口: 22
   - 已安装配置Ansible

3. 数据库配置
   - MySQL 5.7.36
   - 端口: 3310
   - 创建数据库: ansible_ui
   - 字符集: utf8mb4

## 安装步骤

1. 创建并激活虚拟环境：
```bash
# 创建虚拟环境
python -m venv env\myenv

# 激活虚拟环境
.\env\myenv\Scripts\activate  # Windows
source env/myenv/bin/activate   # Linux/Mac
```

2. 安装依赖包：
```bash
# 安装项目依赖
pip install -r requirements.txt
```

3. 数据库初始化：
```sql
CREATE DATABASE ansible_ui CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

4. 配置文件修改：
   - 修改config.py中的数据库连接信息
   - 修改SSH连接参数
   - 设置安全密钥

5. 启动应用：
```bash
python app.py
```

## 目录结构

```
test_practise/
├── app.py                # 主应用程序
├── config.py             # 配置文件
├── models.py             # 数据库模型
├── README.md             # 项目文档
├── requirements.txt      # 依赖包清单
├── migrations/           # 数据库迁移脚本
│   └── add_formatted_result.py  # 添加格式化结果字段
├── utils/              
│   ├── __init__.py
│   ├── ansible_api.py    # Ansible操作接口
│   ├── ssh_proxy.py      # SSH跳转代理
│   └── file_manager.py   # 文件管理模块
├── templates/            # 前端模板
│   ├── index.html        # 主页面
│   └── task_detail.html  # 任务详情页面
└── static/             
    ├── css/            
    │   ├── style.css     # 主样式文件
    │   ├── sidebar.css   # 侧边栏样式
    │   └── ansible-result.css # 任务结果样式
    ├── js/             
    │   ├── main.js       # 主JavaScript文件
    │   └── file-manager.js # 文件管理JavaScript
    └── vendor/           # 第三方库本地化
        ├── bootstrap/    # Bootstrap本地化
        ├── jquery/       # jQuery本地化
        └── bootstrap-icons/ # Bootstrap图标本地化
```

## 数据库表结构

系统使用以下主要表存储数据：

1. `ansible_tasks` - 存储任务信息
   - 包含任务名称、类型、状态、执行结果等
   - 支持格式化结果展示（formatted_result字段）

2. `playbooks` - 存储Playbook信息
   - 包含名称、描述、路径等
   - 支持自动处理无效路径
   - 自动创建不存在的文件

3. `servers` - 存储服务器信息
   - 包含主机名、IP地址、SSH端口等

4. `server_groups` - 存储服务器分组信息
   - 包含分组名称、描述等

5. `server_group_mappings` - 存储服务器与分组的关联关系

## API接口说明

1. 服务器管理接口
   - GET /api/servers - 获取服务器列表
   - POST /api/servers - 添加新服务器
   - GET /api/servers/<id> - 获取服务器详情
   - PUT /api/servers/<id> - 更新服务器信息
   - DELETE /api/servers/<id> - 删除服务器

2. 服务器分组接口
   - GET /api/groups - 获取分组列表
   - POST /api/groups - 创建新分组
   - DELETE /api/groups/<id> - 删除分组
   - GET /api/groups/<id>/servers - 获取分组下的服务器列表

3. 任务管理接口
   - GET /api/tasks - 获取任务列表
   - POST /api/tasks - 创建新任务
   - GET /api/tasks/<id> - 获取任务详情
   - DELETE /api/tasks/<id> - 删除任务
   - POST /api/tasks/<id>/execute - 执行任务

4. Playbook管理接口
   - GET /api/playbooks - 获取Playbook列表
   - POST /api/playbooks - 创建新Playbook
   - GET /api/playbooks/<id> - 获取Playbook详情
   - PUT /api/playbooks/<id> - 更新Playbook
   - DELETE /api/playbooks/<id> - 删除Playbook

5. 文件管理接口
   - POST /api/file-manager/authenticate - 文件管理认证
   - GET /api/file-manager/list - 列出目录内容
   - POST /api/file-manager/upload - 上传文件
   - GET /api/file-manager/download - 下载单个文件
   - POST /api/file-manager/batch-download - 批量下载文件
   - GET /api/file-manager/check-auth - 检查认证状态
   - GET /api/file-manager/check-file - 检查文件是否存在

## 使用说明

1. 系统访问
   - 访问: http://localhost:5000/
   - 系统直接进入主界面，无需登录

2. 服务器管理
   - 添加服务器：填写主机名、IP、SSH端口等信息
   - 创建服务器分组：为服务器分类管理
   - 按分组查看服务器：点击分组的"查看服务器"按钮
   - 编辑和删除服务器：使用操作列中的按钮

3. 任务管理
   - 创建任务：选择任务类型（Playbook或Ad-hoc）
   - 执行任务：点击任务列表中的"执行"按钮
   - 查看任务详情：点击"详情"按钮查看执行结果
   - 删除任务：点击"删除"按钮移除不需要的任务

4. Playbook管理
   - 创建Playbook：填写名称、路径和内容
   - 编辑Playbook：修改现有Playbook的内容
   - 删除Playbook：移除不需要的Playbook
   - 在任务中使用：创建任务时选择已有Playbook

5. 文件管理
   - 认证：输入7.26服务器密码进行认证
   - 浏览文件：导航服务器目录结构
   - 上传文件：选择文件并指定上传路径，可选择是否覆盖已有文件
   - 下载文件：单击文件下载或选择多个文件批量下载
   - 批量下载：系统会将选中文件打包为tar.gz格式下载

## 用户界面特性

1. Toast通知系统
   - 替代原生JavaScript alert
   - 支持成功、错误、警告和信息类型的通知
   - 自动消失和手动关闭功能

2. 自定义确认对话框
   - 替代原生JavaScript confirm
   - 美观的Bootstrap样式
   - 支持自定义标题和消息

3. 密码输入模态框
   - 替代原生JavaScript prompt
   - 安全的密码输入界面
   - 支持回车键确认

4. 删除确认模态框
   - 专用于删除操作的确认界面
   - 警告样式提高用户警觉性

5. 文件上传模态框
   - 支持文件选择和拖放
   - 内置错误提示区域
   - 支持覆盖选项

## 常见问题处理

1. 数据库连接问题
   - 检查端口是否为3310
   - 验证用户名密码
   - 确认数据库字符集

2. SSH连接失败
   - 检查跳板机端口6233
   - 验证目标机器22端口
   - 确认认证信息

3. Ansible执行错误
   - 检查inventory文件
   - 验证目标机器连通性
   - 确认Playbook语法正确

4. Playbook路径问题
   - 系统会自动处理无效路径
   - 确保有权限访问Playbook文件
   - 检查路径是否为绝对路径

5. 文件上传问题
   - 检查文件权限
   - 确认是否需要覆盖同名文件
   - 验证目标路径是否有效

6. 界面显示问题
   - 确保使用现代浏览器
   - 清除浏览器缓存
   - 检查控制台是否有JavaScript错误

## 安全建议

1. 系统访问控制
   - 限制访问IP
   - 设置网络隔离
   - 使用反向代理保护

2. 数据安全
   - 定期备份数据库
   - 加密敏感信息
   - 监控异常访问

3. 文件安全
   - 限制文件上传大小
   - 验证文件类型
   - 控制文件覆盖权限

## 更新日志

### v1.2.0 (2025-04-02)
- 添加文件管理功能，支持浏览、上传和下载文件
- 改进Playbook管理，自动创建不存在的文件
- 优化文件上传功能，添加覆盖选项和错误处理
- 本地化所有静态资源，支持内网环境部署
- 修复模态框无障碍性问题，符合ARIA标准
- 改进错误提示显示方式，提升用户体验

### v1.1.0 (2025-03-24)
- 添加任务删除功能
- 添加服务器删除功能
- 添加按分组查看服务器功能
- 优化任务执行输出格式，移除冗余日志
- 修复Playbook编辑和删除功能
- 修复任务执行时的认证问题

### v1.0.0 (2024-01-24)
- 初始版本发布
- 基础功能实现
