<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网络小工具</title>
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/css/all.min.css">
    <style>
        body {
            background: #f5f7fa;
            min-height: 100vh;
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
        }
        .container {
            max-width: 1400px;
            margin: 40px auto 0 auto;
            padding: 32px 24px;
        }
        h1 {
            text-align: center;
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 32px;
            color: #007AFF;
        }
        .tool-card {
            background: rgba(255,255,255,0.95);
            border-radius: 18px;
            box-shadow: 0 6px 24px rgba(0, 122, 255, 0.08), 0 1.5px 6px rgba(88, 86, 214, 0.06);
            padding: 24px 18px 18px 18px;
            margin-bottom: 32px;
            margin-right: 16px;
            display: flex;
            flex-direction: column;
            justify-content: flex-start;
        }
        .tool-card h2 {
            font-size: 1.3rem;
            font-weight: 600;
            color: #5856D6;
            margin-bottom: 18px;
        }
        label {
            font-weight: 500;
            margin-bottom: 6px;
        }
        .form-control {
            border-radius: 8px;
            padding: 10px 14px;
            border: 1px solid #d0d7de;
            margin-bottom: 14px;
            font-size: 1rem;
        }
        .btn-primary {
            background: linear-gradient(90deg, #007AFF, #5856D6);
            border: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 1.1rem;
            padding: 8px 28px;
            transition: background 0.2s;
        }
        .btn-primary:hover {
            background: linear-gradient(90deg, #0056b3, #5856D6);
        }
        /* 快速选择按钮样式 */
        .btn-port-select {
            border-color: #d0d7de;
            color: #5856D6;
            border-top-right-radius: 8px;
            border-bottom-right-radius: 8px;
            transition: all 0.2s;
            height: 100%;
            display: flex;
            align-items: center;
        }
        .btn-port-select:hover, .btn-port-select:focus {
            background-color: #f0f2f5;
            color: #0056b3;
            border-color: #b0b7be;
        }
        /* 修复输入组样式 */
        .port-input-group {
            display: flex;
            width: 100%;
            margin-bottom: 14px;
            position: relative;
        }
        .port-input-group .form-control {
            flex: 1;
            margin-bottom: 0;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
            height: 42px; /* 确保高度一致 */
        }
        .port-select-btn {
            background-color: #f8f8fd;
            color: #5b54cd;
            border: 1px solid #d0d7de;
            border-left: none;
            min-width: 140px;
            border-top-right-radius: 8px !important;
            border-bottom-right-radius: 8px !important;
            border-top-left-radius: 0 !important;
            border-bottom-left-radius: 0 !important;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0 16px;
            height: 42px;
            box-shadow: none;
            font-size: 0.95rem;
            transition: all 0.2s;
        }
        .port-select-btn:hover, .port-select-btn:focus {
            background-color: #f0f0fb;
            color: #4a43c9;
            border-color: #c0c7ce;
        }
        .port-select-btn::after {
            margin-left: 8px;
            vertical-align: middle;
            color: #666;
        }
        .port-select-btn i {
            color: #5b54cd;
            margin-right: 6px;
            font-size: 0.9rem;
        }
        /* 下拉菜单样式 */
        .port-select-dropdown {
            box-shadow: 0 3px 12px rgba(0,0,0,0.12);
            border-radius: 8px;
            border: 1px solid #e9ecef;
            padding: 8px 0;
        }
        .port-select-dropdown .dropdown-header {
            color: #6c757d;
            font-weight: 600;
            padding: 8px 16px;
        }
        .port-select-dropdown .dropdown-item {
            padding: 8px 16px;
            color: #333;
        }
        .port-select-dropdown .dropdown-item:hover {
            background-color: #f0f7ff;
        }
        .port-select-dropdown .dropdown-item i {
            width: 20px;
            color: #5856D6;
            margin-right: 8px;
        }
        .result-area {
            margin-top: 18px;
        }
        pre {
            background: #e9e9e9;
            border-radius: 6px;
            padding: 14px;
            font-size: 1rem;
            min-height: 40px;
            border: 1px solid #d0d7de;
            color: #222;
            max-height: 300px;
            overflow: auto;
        }
        .loading { color: #007AFF; font-weight: 500; }
        .error { color: #d32f2f !important; font-weight: 600; }
        .success { color: #388e3c !important; font-weight: 600; }
        @media (max-width: 1200px) {
            .col-md-4 { flex: 0 0 50%; max-width: 50%; }
        }
        @media (max-width: 800px) {
            .container { max-width: 98vw; padding: 12px 2vw; }
            .col-md-4 { flex: 0 0 100%; max-width: 100%; }
        }
        @media (max-width: 500px) {
            h1 { font-size: 1.3rem; }
            .tool-card { padding: 12px 4px 8px 4px; min-height: 0; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-network-wired"></i> 网络小工具集</h1>
        <div class="row">
            <!-- Ping 工具 -->
            <div class="col-lg-4 col-md-6 mb-4 d-flex">
                <div class="tool-card flex-fill">
                    <h2><i class="fas fa-wifi"></i> Ping 测试</h2>
                    <form id="pingForm">
                        <label for="pingIpAddress">IP 地址或域名</label>
                        <input type="text" class="form-control" id="pingIpAddress" name="ip_address" required placeholder="如 ******* 或 www.baidu.com">
                        <div class="row">
                            <div class="col">
                                <label for="pingPackets">包数量 (1-20)</label>
                                <input type="number" class="form-control" id="pingPackets" name="packets" min="1" max="20" placeholder="4">
                            </div>
                            <div class="col">
                                <label for="pingTimeout">超时 (秒, 1-60)</label>
                                <input type="number" class="form-control" id="pingTimeout" name="timeout" min="1" max="60" placeholder="10">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary mt-3">执行 Ping</button>
                    </form>
                </div>
            </div>
            <!-- Telnet 工具 -->
            <div class="col-lg-4 col-md-6 mb-4 d-flex">
                <div class="tool-card flex-fill">
                    <h2><i class="fas fa-plug"></i> Telnet 端口测试</h2>
                    <form id="telnetForm">
                        <label for="telnetIpAddress">IP 地址或域名</label>
                        <input type="text" class="form-control" id="telnetIpAddress" name="ip_address" required placeholder="如 ******* 或 www.baidu.com">
                        <div class="row">
                            <div class="col">
                                <label for="telnetPort">端口号 (1-65535)</label>
                                <input type="number" class="form-control" id="telnetPort" name="port" required min="1" max="65535" placeholder="80">
                            </div>
                            <div class="col">
                                <label for="telnetTimeout">超时 (秒, 1-60)</label>
                                <input type="number" class="form-control" id="telnetTimeout" name="timeout" min="1" max="60" placeholder="5">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary mt-3">测试端口</button>
                    </form>
                </div>
            </div>
            <!-- Traceroute 工具 -->
            <div class="col-lg-4 col-md-6 mb-4 d-flex">
                <div class="tool-card flex-fill">
                    <h2><i class="fas fa-route"></i> Traceroute 路由跟踪</h2>
                    <form id="tracerouteForm">
                        <label for="tracerouteIpAddress">IP 地址或域名</label>
                        <input type="text" class="form-control" id="tracerouteIpAddress" name="ip_address" required placeholder="如 ******* 或 www.baidu.com">
                        <div class="row">
                            <div class="col">
                                <label for="tracerouteMaxHops">最大跳数 (1-64)</label>
                                <input type="number" class="form-control" id="tracerouteMaxHops" name="max_hops" min="1" max="64" placeholder="30">
                            </div>
                            <div class="col">
                                <label for="tracerouteTimeout">超时 (秒, 1-30)</label>
                                <input type="number" class="form-control" id="tracerouteTimeout" name="timeout" min="1" max="30" placeholder="5">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary mt-3">执行 Traceroute</button>
                    </form>
                </div>
            </div>
            <!-- 端口扫描工具 -->
            <div class="col-lg-4 col-md-6 mb-4 d-flex">
                <div class="tool-card flex-fill">
                    <h2><i class="fas fa-search"></i> 端口扫描</h2>
                    <form id="portscanForm">
                        <label for="portscanIpAddress">IP 地址或域名</label>
                        <input type="text" class="form-control" id="portscanIpAddress" name="ip_address" required placeholder="如 ******* 或 www.baidu.com">
                        
                        <label for="portscanPorts">端口范围</label>
                        <div class="port-input-group">
                            <input type="text" class="form-control" id="portscanPorts" name="ports" required placeholder="如 22,80,443 或 8000-8100">
                            <button class="port-select-btn dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-list"></i> 快速选择
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end port-select-dropdown">
                                <li><h6 class="dropdown-header">常用端口组合</h6></li>
                                <li><a class="dropdown-item" href="#" data-ports="20,21,22,23,25,53,80,110,143,443,465,587,993,995,3306,3389,5432,6379,8080,8443"><i class="fas fa-star"></i>常用服务端口</a></li>
                                <li><a class="dropdown-item" href="#" data-ports="80,81,443,8000,8001,8080,8081,8443,9000,9001"><i class="fas fa-globe"></i>Web 服务端口</a></li>
                                <li><a class="dropdown-item" href="#" data-ports="3306,5432,1433,1521,6379,27017,11211"><i class="fas fa-database"></i>数据库端口</a></li>
                                <li><a class="dropdown-item" href="#" data-ports="22,23,3389"><i class="fas fa-terminal"></i>远程登录端口</a></li>
                                <li><a class="dropdown-item" href="#" data-ports="1-1024"><i class="fas fa-shield-alt"></i>系统保留端口</a></li>
                            </ul>
                        </div>

                        <div class="row">
                            <div class="col">
                                <label for="portscanTimeout">超时 (秒)</label>
                                <input type="number" class="form-control" id="portscanTimeout" name="timeout" min="1" max="10" placeholder="1">
                            </div>
                            <div class="col">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="portscanDetailMode" checked>
                                    <label class="form-check-label" for="portscanDetailMode">
                                        详细模式 (本地扫描时尝试显示进程信息)
                                    </label>
                                </div>
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary mt-3">
                            <i class="fas fa-search"></i> 扫描端口
                        </button>
                    </form>
                </div>
            </div>
            <!-- 预留卡片位，可扩展更多工具 -->
            <div class="col-lg-4 col-md-6 mb-4 d-flex">
                <div class="tool-card flex-fill d-flex align-items-center justify-content-center" style="min-height: 200px; color: #bbb; font-size: 1.2rem; background-color: #f8f9fa; border: 2px dashed #ddd;">
                    <div class="text-center">
                        <i class="fas fa-plus-circle fa-2x mb-2"></i>
                        <div>更多工具，敬请期待</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 统一结果弹窗Modal -->
    <div class="modal fade" id="resultModal" tabindex="-1" aria-labelledby="resultModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
          <div class="modal-header">
            <h5 class="modal-title" id="resultModalLabel">结果</h5>
            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
          </div>
          <div class="modal-body">
            <pre id="modalResultContent" style="max-height: 400px; overflow:auto; background:#e9e9e9; border-radius:6px; border:1px solid #d0d7de; color:#222; font-size:1rem;"></pre>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            <button type="button" class="btn btn-outline-primary" id="copyResultBtn">复制结果</button>
          </div>
        </div>
      </div>
    </div>

    <script src="/static/js/lib/jquery-3.6.0.min.js"></script>
    <script src="/static/js/lib/bootstrap.bundle.min.js"></script>
    <script>
        function showResultModal(title, content, isSuccess) {
            $('#resultModalLabel').text(title);
            const $content = $('#modalResultContent');
            $content.text(content);
            $content.removeClass('success error');
            if(isSuccess) $content.addClass('success');
            else $content.addClass('error');
            const modal = new bootstrap.Modal(document.getElementById('resultModal'));
            modal.show();
        }
        $('#copyResultBtn').on('click', function(){
            const text = $('#modalResultContent').text();
            navigator.clipboard.writeText(text).then(function(){
                $('#copyResultBtn').text('已复制!');
                setTimeout(()=>{$('#copyResultBtn').text('复制结果');}, 1200);
            });
        });
        function handleSubmit(formId, url, resultAreaId, loadingText) {
            const form = document.getElementById(formId);
            form.addEventListener('submit', function(event) {
                event.preventDefault();
                const formData = new FormData(form);
                const data = {};
                formData.forEach((value, key) => {
                    const inputElement = form.elements[key];
                    if (inputElement && value === '' && inputElement.placeholder) {
                        data[key] = inputElement.placeholder;
                    } else {
                        data[key] = value;
                    }
                });
                if (data.packets) data.packets = parseInt(data.packets);
                if (data.timeout) data.timeout = parseInt(data.timeout);
                if (data.port) data.port = parseInt(data.port);
                if (data.max_hops) data.max_hops = parseInt(data.max_hops);
                showResultModal('请稍候', loadingText, true);
                fetch(url, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().catch(() => {
                            throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
                        }).then(errData => {
                             throw new Error(errData.error || `HTTP error! status: ${response.status}`);
                        });
                    }
                    return response.json();
                })
                .then(responseData => {
                    let formattedResult = "状态: " + responseData.status + "\n";
                    if (responseData.rtt_avg_ms !== null && responseData.rtt_avg_ms !== undefined) {
                        formattedResult += "平均 RTT: " + responseData.rtt_avg_ms + " ms\n";
                    }
                    if (responseData.packet_loss_percent !== null && responseData.packet_loss_percent !== undefined) {
                        formattedResult += "丢包率: " + responseData.packet_loss_percent + " %\n";
                    }
                    if (responseData.ip) formattedResult += "IP: " + responseData.ip + "\n";
                    if (responseData.port) formattedResult += "端口: " + responseData.port + "\n";
                    formattedResult += "\n--- 详细信息 ---\n";
                    if (typeof responseData.details === 'string') {
                        const cleanDetails = responseData.details.replace(/\x1B\[[0-9;]*[mK]/g, '');
                        formattedResult += cleanDetails;
                    } else if (responseData.details) {
                        formattedResult += JSON.stringify(responseData.details, null, 2);
                    }
                    showResultModal('结果', formattedResult, responseData.status === 'success' || responseData.status === 'open');
                })
                .catch(error => {
                    showResultModal('错误', '请求失败: ' + error.message, false);
                });
            });
        }
        handleSubmit('pingForm', '/tools/ping', '', '正在执行 Ping...');
        handleSubmit('telnetForm', '/tools/telnet', '', '正在测试端口...');
        handleSubmit('tracerouteForm', '/tools/traceroute', '', '正在执行 Traceroute...');
        let portscanController = null;
        function handlePortScan() {
            const form = document.getElementById('portscanForm');
            form.addEventListener('submit', function(event) {
                event.preventDefault();
                const ip = form.ip_address.value;
                const ports = form.ports.value;
                const timeout = form.timeout.value || 1;
                const detailMode = document.getElementById('portscanDetailMode').checked;
                
                showResultModal('请稍候', '正在扫描端口...\n\n注意：大范围的端口扫描可能需要较长时间', true);
                portscanController = new AbortController();
                
                // 根据是否选择详细模式决定使用哪个API
                const apiUrl = detailMode ? '/tools/portscan_detail' : '/tools/portscan';
                
                fetch(apiUrl, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ ip_address: ip, ports: ports, timeout: timeout }),
                    signal: portscanController.signal
                })
                .then(response => {
                    if (!response.ok) {
                        return response.json().then(errData => {
                            throw new Error(errData.error || `HTTP error! status: ${response.status}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    const isSuccess = data.status === 'success' || data.status === 'warning';
                    let title = isSuccess ? '端口扫描结果' : '扫描出错';
                    let formattedResult = "";
                    
                    if (data.status === 'warning') {
                        formattedResult += `⚠️ ${data.details}\n\n`;
                    } else if (data.status === 'error') {
                        formattedResult += `❌ ${data.details}\n`;
                        showResultModal(title, formattedResult, false);
                        return;
                    }
                    
                    // 添加IP信息
                    formattedResult += `目标: ${data.ip}`;
                    if (data.resolved_ip && data.resolved_ip !== data.ip) {
                        formattedResult += ` (解析到 ${data.resolved_ip})`;
                    }
                    formattedResult += '\n';
                    
                    // 添加扫描统计信息
                    const totalPorts = data.scanned_ports_count || (data.scan_statistics && data.scan_statistics.total_ports);
                    const openPorts = data.open_ports_count || (data.open_ports && data.open_ports.length);
                    
                    formattedResult += `扫描完成: 共扫描 ${totalPorts || '?'} 个端口，发现 ${openPorts || 0} 个开放端口`;
                    
                    const scanDuration = data.scan_statistics && data.scan_statistics.scan_duration_seconds;
                    if (scanDuration) {
                        formattedResult += `，耗时 ${scanDuration} 秒`;
                    }
                    formattedResult += '\n';
                    
                    if (data.is_local_scan === true) {
                        formattedResult += "本地扫描模式: 将显示进程信息\n";
                    } else if (data.is_local_scan === false) {
                        formattedResult += "远程扫描模式: 无法获取进程信息\n";
                    }
                    
                    if (data.details) {
                        formattedResult += "\n" + data.details;
                    } else if (data.open_ports && data.open_ports.length > 0) {
                        formattedResult += "\n端口列表:\n";
                        data.open_ports.forEach(port => {
                            const service = port.service ? ` (${port.service})` : '';
                            formattedResult += `${port.port}${service}\n`;
                            
                            if (port.banner) {
                                formattedResult += `  - ${port.banner}\n`;
                            }
                            
                            if (port.process && port.process.pid) {
                                formattedResult += `  - PID: ${port.process.pid}\n`;
                            }
                        });
                    } else {
                        formattedResult += "\n未发现开放端口";
                    }
                    
                    showResultModal(title, formattedResult, isSuccess);
                })
                .catch(error => {
                    if (error.name === 'AbortError') {
                        return;
                    }
                    showResultModal('扫描错误', '请求失败: ' + error.message, false);
                });
            });
        }
        handlePortScan();
        document.getElementById('resultModal').addEventListener('hidden.bs.modal', function () {
            if (portscanController) {
                portscanController.abort();
            }
            // 清除模态窗口背景遮罩
            const modalBackdrop = document.querySelector('.modal-backdrop');
            if (modalBackdrop) {
                modalBackdrop.remove();
            }
            // 确保body不再有modal-open类
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        });
        // 端口快速选择
        document.querySelectorAll('.dropdown-item[data-ports]').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                document.getElementById('portscanPorts').value = this.getAttribute('data-ports');
            });
        });
    </script>
</body>
</html> 