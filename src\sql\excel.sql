/*
 Navicat Premium Dump SQL

 Source Server         : mysql5.7
 Source Server Type    : MySQL
 Source Server Version : 50736 (5.7.36-log)
 Source Host           : **************:3310
 Source Schema         : excel

 Target Server Type    : MySQL
 Target Server Version : 50736 (5.7.36-log)
 File Encoding         : 65001

 Date: 09/05/2025 10:27:57
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for country_people_metadata
-- ----------------------------
DROP TABLE IF EXISTS `country_people_metadata`;
CREATE TABLE `country_people_metadata`  (
  `provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区县名称',
  `population` int(11) NULL DEFAULT NULL COMMENT '该县人口数',
  `englinsh_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区县人口数' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for data_center_ddl_metadata
-- ----------------------------
DROP TABLE IF EXISTS `data_center_ddl_metadata`;
CREATE TABLE `data_center_ddl_metadata`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `server_ip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务器IP',
  `server_port` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务器端口',
  `server_account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务器账号',
  `server_password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务器密码',
  `database_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据库名称',
  `table_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称',
  `table_comment` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表注释',
  `table_structure` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '表结构JSON',
  `record_count` int(11) NULL DEFAULT 0 COMMENT '记录数量',
  `created_at` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `idx_server_database`(`server_ip`(50), `server_port`, `server_account`(50)) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据中台配置和表结构元数据' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for directory_tree_json_metadata
-- ----------------------------
DROP TABLE IF EXISTS `directory_tree_json_metadata`;
CREATE TABLE `directory_tree_json_metadata`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `tree_json` json NOT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `ip`(`ip`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 34 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_gan_gu
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_gan_gu`;
CREATE TABLE `excel_data_gan_gu`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `data_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据单位',
  `chinese_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `org_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `is_pushed` tinyint(4) NULL DEFAULT 0 COMMENT '是否推送',
  `is_imported` tinyint(4) NULL DEFAULT 0 COMMENT '是否入湖'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '天水市_甘谷县' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_gan_gu_metadata
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_gan_gu_metadata`;
CREATE TABLE `excel_data_gan_gu_metadata`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入湖数据提供单位',
  `chinese_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` int(11) NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` datetime NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据表元数据信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_mai_ji
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_mai_ji`;
CREATE TABLE `excel_data_mai_ji`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `data_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据单位',
  `chinese_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `org_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `is_pushed` tinyint(4) NULL DEFAULT 0 COMMENT '是否推送',
  `is_imported` tinyint(4) NULL DEFAULT 0 COMMENT '是否入湖'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '天水市_麦积区' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_mai_ji_metadata
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_mai_ji_metadata`;
CREATE TABLE `excel_data_mai_ji_metadata`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入湖数据提供单位',
  `chinese_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` int(11) NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` datetime NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据表元数据信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_qin_an
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_qin_an`;
CREATE TABLE `excel_data_qin_an`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `data_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据单位',
  `chinese_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `org_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `is_pushed` tinyint(4) NULL DEFAULT 0 COMMENT '是否推送',
  `is_imported` tinyint(4) NULL DEFAULT 0 COMMENT '是否入湖'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '天水市_秦安县' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_qin_an_metadata
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_qin_an_metadata`;
CREATE TABLE `excel_data_qin_an_metadata`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入湖数据提供单位',
  `chinese_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` int(11) NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` datetime NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据表元数据信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_qin_shui
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_qin_shui`;
CREATE TABLE `excel_data_qin_shui`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `data_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入湖数据提供单位',
  `chinese_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `org_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `is_pushed` tinyint(4) NULL DEFAULT 0 COMMENT '是否推送',
  `is_imported` tinyint(4) NULL DEFAULT 0 COMMENT '是否入湖'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '天水市_清水县' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_qin_shui_metadata
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_qin_shui_metadata`;
CREATE TABLE `excel_data_qin_shui_metadata`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入湖数据提供单位',
  `chinese_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` int(11) NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` datetime NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据表元数据信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_qin_zhou
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_qin_zhou`;
CREATE TABLE `excel_data_qin_zhou`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `data_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据单位',
  `chinese_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `org_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `is_pushed` tinyint(4) NULL DEFAULT 0 COMMENT '是否推送',
  `is_imported` tinyint(4) NULL DEFAULT 0 COMMENT '是否入湖'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '天水市_秦州区' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_qin_zhou_metadata
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_qin_zhou_metadata`;
CREATE TABLE `excel_data_qin_zhou_metadata`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入湖数据提供单位',
  `chinese_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` int(11) NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` datetime NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据表元数据信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_tss_sz
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_tss_sz`;
CREATE TABLE `excel_data_tss_sz`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `data_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据单位',
  `chinese_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `org_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `is_pushed` tinyint(4) NULL DEFAULT 0 COMMENT '是否推送',
  `is_imported` tinyint(4) NULL DEFAULT 0 COMMENT '是否入湖'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '天水市_市直单位' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_tss_sz_metadata
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_tss_sz_metadata`;
CREATE TABLE `excel_data_tss_sz_metadata`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入湖数据提供单位',
  `chinese_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` int(11) NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` datetime NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据表元数据信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_wu_shan
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_wu_shan`;
CREATE TABLE `excel_data_wu_shan`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `data_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据单位',
  `chinese_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `org_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `is_pushed` tinyint(4) NULL DEFAULT 0 COMMENT '是否推送',
  `is_imported` tinyint(4) NULL DEFAULT 0 COMMENT '是否入湖'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '天水市_武山县' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_wu_shan_metadata
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_wu_shan_metadata`;
CREATE TABLE `excel_data_wu_shan_metadata`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入湖数据提供单位',
  `chinese_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` int(11) NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` datetime NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据表元数据信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_zhang_jia_chuan
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_zhang_jia_chuan`;
CREATE TABLE `excel_data_zhang_jia_chuan`  (
  `id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `data_provider` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据单位',
  `chinese_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `org_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `is_pushed` tinyint(4) NULL DEFAULT 0 COMMENT '是否推送',
  `is_imported` tinyint(4) NULL DEFAULT 0 COMMENT '是否入湖'
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '天水市_张家川县' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for excel_data_zhang_jia_chuan_metadata
-- ----------------------------
DROP TABLE IF EXISTS `excel_data_zhang_jia_chuan_metadata`;
CREATE TABLE `excel_data_zhang_jia_chuan_metadata`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `data_provider` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '入湖数据提供单位',
  `chinese_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '中文名称',
  `table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名',
  `record_count` int(11) NULL DEFAULT NULL COMMENT '数据条数',
  `provide_time` datetime NULL DEFAULT NULL COMMENT '提供时间',
  `table_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表名称描述',
  `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据表元数据信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for test_org_code_metadata
-- ----------------------------
DROP TABLE IF EXISTS `test_org_code_metadata`;
CREATE TABLE `test_org_code_metadata`  (
  `org_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `org_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位名称'
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '机构部门表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
