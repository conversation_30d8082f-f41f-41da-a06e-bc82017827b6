from flask import Blueprint, request, jsonify, render_template, send_file

from src.utils.error_handler import (
    handle_exceptions, DatabaseError, NetworkError, ValidationError,
    BusinessError, create_error_response, create_success_response,
    ErrorCategory, ErrorLevel
)
from datetime import datetime
import logging
import pandas as pd
from io import BytesIO, StringIO

# 从您的项目中导入必要的模块
# 请确保这些路径和模块名是正确的
from src.mysql_audit.models import (
    get_user_activities, get_operation_stats,
    get_all_servers, get_server_by_id, add_server, update_server, delete_server,
    get_system_setting, update_system_setting, init_db as audit_init_db
)
from src.mysql_audit.log_parser import scan_logs_for_server, scan_all_servers
from src.mysql_audit.reports import ReportGenerator
from src.mysql_audit.config import APP_CONFIG # 假设 APP_CONFIG 仍然从这里导入

# 创建蓝图
# 确保 template_folder 指向 mysql_audit 相关的模板目录
# 通常，如果 audit_routes.py 在 src/mysql_audit/ 下，templates 在项目根目录，
# 那么 'templates/mysql_audit' 可能是正确的相对路径给 render_template，
# 而 Blueprint 的 template_folder 参数是相对于蓝图文件本身或应用根目录的。
# 为了清晰，我们将指定相对于应用根目录的模板文件夹，并在 render_template 中使用相对路径。
mysql_audit_bp = Blueprint('mysql_audit', __name__, template_folder='../templates/mysql_audit', url_prefix='/mysql_audit')


# --- 初始配置缓存 for mysql_audit ---
CACHED_RISK_OPERATIONS = None
CACHED_WRITE_RISK_LEVELS = None

# --- 初始加载配置 for mysql_audit ---
def load_system_settings():
    """从数据库加载系统配置到内存缓存"""
    global CACHED_RISK_OPERATIONS, CACHED_WRITE_RISK_LEVELS
    
    # 加载风险操作规则
    risk_operations_setting = get_system_setting('RISK_OPERATIONS')
    if risk_operations_setting:
        CACHED_RISK_OPERATIONS = risk_operations_setting
    else:
        # 如果数据库中没有，使用默认值
        CACHED_RISK_OPERATIONS = APP_CONFIG.get('RISK_OPERATIONS', {})
    
    # 加载写入风险级别
    write_risk_levels_setting = get_system_setting('WRITE_RISK_LEVELS')
    if write_risk_levels_setting:
        CACHED_WRITE_R_LEVELS = write_risk_levels_setting
    else:
        # 如果数据库中没有，使用默认值
        CACHED_WRITE_RISK_LEVELS = APP_CONFIG.get('WRITE_RISK_LEVELS', ['High', 'Medium'])
    
    print("MySQL审计系统配置已从数据库加载到内存缓存 (via audit_routes.py)")


@mysql_audit_bp.route('/') #相对于蓝图的url_prefix
def mysql_audit_index():
    """MySQL审计首页"""
    try:
        servers = get_all_servers()
        for server in servers:
            if 'server_id' not in server:
                server['server_id'] = server.get('id', None)
        # 模板路径是相对于 templates 目录的
        return render_template('mysql_audit/index.html', servers=servers)
    except Exception as e:
        print(f"访问MySQL审计首页失败: {e}")
        # 确保有一个 error.html 模板
        # 考虑使用更通用的错误模板或mysql_audit下的错误模板
        return render_template('mysql_audit/error.html', error=f"访问MySQL审计首页失败: {e}") # Render mysql_audit error template

@mysql_audit_bp.route('/api/activities', methods=['GET'])
def mysql_audit_get_activities():
    """获取用户活动记录 API"""
    try:
        server_id = request.args.get('server_id', type=int)
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        operation_type = request.args.get('operation_type')
        risk_level = request.args.get('risk_level')
        user_name = request.args.get('user_name')
        page = request.args.get('page', 1, type=int)
        limit = request.args.get('limit', 10, type=int)
        offset = (page - 1) * limit

        start_date = None
        end_date = None
        if start_date_str and start_date_str.lower() != 'invalid date':
            try: start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            except ValueError: print(f"无效的 start_date 格式: {start_date_str}")
        if end_date_str and end_date_str.lower() != 'invalid date':
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                if end_date: end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
            except ValueError: print(f"无效的 end_date 格式: {end_date_str}")

        activities, total = get_user_activities(
            server_id=server_id if server_id else None,
            start_date=start_date,
            end_date=end_date,
            operation_type=operation_type if operation_type else None,
            risk_level=risk_level if risk_level else None,
            user_name=user_name if user_name else None,
            limit=limit,
            offset=offset
        )

        return jsonify({
            'data': activities,
            'count': total,
            'page': page,
            'limit': limit
        })

    except Exception as e:
        print(f"获取活动数据失败: {e}")
        return jsonify({'error': f'获取活动数据失败: {e}'}), 500

@mysql_audit_bp.route('/api/stats')
def get_audit_stats():
    """获取审计统计数据"""
    try:
        server_id_str = request.args.get('server_id')
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        server_id = None
        if server_id_str:
            try:
                server_id = int(server_id_str)
            except (TypeError, ValueError):
                if server_id_str != '':
                    print(f"[警告] 无效的server_id格式: {server_id_str}")
        
        print(f"[stats] 接收到的筛选参数: server_id_原始值={server_id_str}, 转换后={server_id}, start_date={start_date}, end_date={end_date}")
        
        all_params = request.args.to_dict(flat=False)
        print(f"[stats] 原始请求参数: {all_params}")

        stats = get_operation_stats(
            server_id=server_id,
            start_date=start_date,
            end_date=end_date
        )
        
        return jsonify(stats)
    except Exception as e:
        logging.error(f"获取审计统计数据失败: {str(e)}")
        return create_error_response("获取审计统计数据失败", "ERROR", 500)

@mysql_audit_bp.route('/api/scan', methods=['POST'])
def scan_audit_logs():
    """扫描审计日志"""
    try:
        data = request.get_json()
        server_id = data.get('server_id') if data else None

        if server_id:
            server = get_server_by_id(server_id)
            if not server:
                return create_error_response("服务器不存在", "ERROR", 404)
            scan_logs_for_server(server)
        else:
            scan_all_servers()

        return jsonify({
            "status": "success",
            "message": "开始扫描日志，请稍后刷新页面查看结果"
        })
    except Exception as e:
        logging.error(f"扫描审计日志失败: {str(e)}")
        return create_error_response("扫描审计日志失败", "ERROR", 500)

@mysql_audit_bp.route('/api/servers', methods=['GET'])
def mysql_audit_get_servers():
    """获取所有服务器配置"""
    try:
        servers = get_all_servers()
        return jsonify({'status': 'success', 'servers': servers})
    except Exception as e:
        print(f"获取服务器配置失败: {e}")
        return jsonify({'status': 'error', 'error': f'获取服务器配置失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/server/<int:server_id>', methods=['GET'])
def mysql_audit_get_server(server_id):
    """获取特定服务器配置"""
    try:
        server = get_server_by_id(server_id)
        if not server:
            return jsonify({'status': 'error', 'error': f'未找到ID为 {server_id} 的服务器配置'}), 404
        return jsonify({'status': 'success', 'server': server})
    except Exception as e:
        print(f"获取服务器配置失败: {e}")
        return jsonify({'status': 'error', 'error': f'获取服务器配置失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/server', methods=['POST'])
def mysql_audit_add_server():
    """添加新服务器配置"""
    try:
        if not request.is_json:
            return jsonify({'status': 'error', 'error': '请求必须是JSON格式'}), 400
            
        server_data = request.json
        
        required_fields = ['name', 'host', 'port', 'user']
        missing_fields = [field for field in required_fields if field not in server_data]
        if missing_fields:
            return jsonify({'status': 'error', 'error': f'缺少必要字段: {", ".join(missing_fields)}'}), 400
            
        server_id = add_server(server_data)
        if server_id:
            return jsonify({
                'status': 'success',
                'message': '服务器配置已添加',
                'server_id': server_id
            })
        else:
            return jsonify({'status': 'error', 'error': '添加服务器配置失败'}), 500
    except Exception as e:
        print(f"添加服务器配置失败: {e}")
        return jsonify({'status': 'error', 'error': f'添加服务器配置失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/server/<int:server_id>', methods=['PUT'])
def mysql_audit_update_server(server_id):
    """更新服务器配置"""
    try:
        if not request.is_json:
            return jsonify({'status': 'error', 'error': '请求必须是JSON格式'}), 400
            
        server_data = request.json
        
        required_fields = ['name', 'host', 'port', 'user']
        missing_fields = [field for field in required_fields if field not in server_data]
        if missing_fields:
            return jsonify({'status': 'error', 'error': f'缺少必要字段: {", ".join(missing_fields)}'}), 400
            
        success = update_server(server_id, server_data)
        if success:
            return jsonify({
                'status': 'success',
                'message': '服务器配置已更新'
            })
        else:
            return jsonify({'status': 'error', 'error': f'未找到ID为 {server_id} 的服务器配置或更新失败'}), 404
    except Exception as e:
        print(f"更新服务器配置失败: {e}")
        return jsonify({'status': 'error', 'error': f'更新服务器配置失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/server/<int:server_id>', methods=['DELETE'])
def mysql_audit_delete_server(server_id):
    """删除服务器配置"""
    try:
        success = delete_server(server_id)
        if success:
            return jsonify({
                'status': 'success',
                'message': '服务器配置已删除'
            })
        else:
            return jsonify({'status': 'error', 'error': f'未找到ID为 {server_id} 的服务器配置'}), 404
    except Exception as e:
        print(f"删除服务器配置失败: {e}")
        return jsonify({'status': 'error', 'error': f'删除服务器配置失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/risk_rules', methods=['GET'])
def mysql_audit_get_risk_rules():
    """获取风险规则配置"""
    try:
        risk_rules = get_system_setting('RISK_OPERATIONS')
        return jsonify({'status': 'success', 'risk_rules': risk_rules})
    except Exception as e:
        print(f"获取风险规则失败: {e}")
        return jsonify({'status': 'error', 'error': f'获取风险规则失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/risk_rules', methods=['PUT'])
def mysql_audit_update_risk_rules():
    """更新风险规则配置"""
    try:
        if not request.is_json:
            return jsonify({'status': 'error', 'error': '请求必须是JSON格式'}), 400
            
        risk_rules = request.json.get('risk_rules')
        if not risk_rules or not isinstance(risk_rules, dict):
            return jsonify({'status': 'error', 'error': '风险规则格式无效'}), 400
            
        for level, rules in risk_rules.items():
            if level not in ['High', 'Medium', 'Low']:
                return jsonify({'status': 'error', 'error': f'无效的风险等级: {level}'}), 400
            if not isinstance(rules, list):
                return jsonify({'status': 'error', 'error': f'等级 {level} 的规则必须是列表'}), 400
            for rule in rules:
                if not isinstance(rule, dict):
                    return jsonify({'status': 'error', 'error': f'等级 {level} 的规则必须是字典'}), 400
                if 'type' not in rule and 'keyword' not in rule:
                    return jsonify({'status': 'error', 'error': f'规则必须包含 type 或 keyword 字段'}), 400
        
        success = update_system_setting('RISK_OPERATIONS', risk_rules, '风险操作规则配置')
        if success:
            load_system_settings() # 重新加载更新后的配置
            return jsonify({
                'status': 'success',
                'message': '风险规则已更新'
            })
        else:
            return jsonify({'status': 'error', 'error': '更新风险规则失败'}), 500
    except Exception as e:
        print(f"更新风险规则失败: {e}")
        return jsonify({'status': 'error', 'error': f'更新风险规则失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/write_risk_levels', methods=['GET'])
def mysql_audit_get_write_risk_levels():
    """获取写入风险级别配置"""
    try:
        write_risk_levels = get_system_setting('WRITE_RISK_LEVELS')
        return jsonify({'status': 'success', 'write_risk_levels': write_risk_levels})
    except Exception as e:
        print(f"获取写入风险级别失败: {e}")
        return jsonify({'status': 'error', 'error': f'获取写入风险级别失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/write_risk_levels', methods=['PUT'])
def mysql_audit_update_write_risk_levels():
    """更新写入风险级别配置"""
    try:
        if not request.is_json:
            return jsonify({'status': 'error', 'error': '请求必须是JSON格式'}), 400
            
        write_risk_levels = request.json.get('write_risk_levels')
        if not write_risk_levels or not isinstance(write_risk_levels, list):
            return jsonify({'status': 'error', 'error': '写入风险级别格式无效'}), 400
            
        for level in write_risk_levels:
            if level not in ['High', 'Medium', 'Low']:
                return jsonify({'status': 'error', 'error': f'无效的风险等级: {level}'}), 400
                
        success = update_system_setting('WRITE_RISK_LEVELS', write_risk_levels)
        if success:
            load_system_settings() # 重新加载更新后的配置
            return jsonify({
                'status': 'success',
                'message': '写入风险级别已更新'
            })
        else:
            return jsonify({'status': 'error', 'error': '更新写入风险级别失败'}), 500
    except Exception as e:
        print(f"更新写入风险级别失败: {e}")
        return jsonify({'status': 'error', 'error': f'更新写入风险级别失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/reports/daily', methods=['GET'])
def mysql_audit_get_daily_report():
    """获取每日报告 API"""
    try:
        server_id_str = request.args.get('server_id')
        server_id = int(server_id_str) if server_id_str and server_id_str.strip() else None
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        operation_type = request.args.get('operation_type')
        risk_level = request.args.get('risk_level')
        user_name = request.args.get('user_name')
        
        start_date = None
        end_date = None
        if start_date_str and start_date_str.lower() != 'invalid date':
            try: start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            except ValueError: logging.warning(f"无效的开始日期格式: {start_date_str}")
        if end_date_str and end_date_str.lower() != 'invalid date':
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                if end_date: end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
            except ValueError: logging.warning(f"无效的结束日期格式: {end_date_str}")
        
        logging.info(f"日报生成筛选条件: server_id={server_id}, start_date={start_date}, end_date={end_date}, "
                    f"operation_type={operation_type}, risk_level={risk_level}, user_name={user_name}")
                
        if request.args.get('download') == 'true':
            report_file = ReportGenerator.generate_daily_report(
                start_date=start_date, end_date=end_date, server_id=server_id,
                operation_type=operation_type, risk_level=risk_level, user_name=user_name
            )
            return send_file(report_file, as_attachment=True, download_name='daily_report.xlsx')
        else:
            report_data = ReportGenerator.generate_daily_report_json(
                start_date=start_date, end_date=end_date, server_id=server_id,
                operation_type=operation_type, risk_level=risk_level, user_name=user_name
            )
            return jsonify({"status": "success", "data": report_data})
    except Exception as e:
        logging.error(f"生成每日报告失败: {str(e)}")
        return jsonify({'error': f'生成每日报告失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/reports/weekly', methods=['GET'])
def mysql_audit_get_weekly_report():
    """获取每周报告 API"""
    try:
        server_id_str = request.args.get('server_id')
        server_id = int(server_id_str) if server_id_str and server_id_str.strip() else None
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        operation_type = request.args.get('operation_type')
        risk_level = request.args.get('risk_level')
        user_name = request.args.get('user_name')

        start_date = None
        end_date = None
        if start_date_str and start_date_str.lower() != 'invalid date':
            try: start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            except ValueError: logging.warning(f"无效的开始日期格式: {start_date_str}")
        if end_date_str and end_date_str.lower() != 'invalid date':
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                if end_date: end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
            except ValueError: logging.warning(f"无效的结束日期格式: {end_date_str}")

        logging.info(f"周报生成筛选条件: server_id={server_id}, start_date={start_date}, end_date={end_date}, "
                    f"operation_type={operation_type}, risk_level={risk_level}, user_name={user_name}")

        if request.args.get('download') == 'true':
            report_file = ReportGenerator.generate_weekly_report(
                start_date=start_date, end_date=end_date, server_id=server_id,
                operation_type=operation_type, risk_level=risk_level, user_name=user_name
            )
            return send_file(report_file, as_attachment=True, download_name='weekly_report.xlsx')
        else:
            report_data = ReportGenerator.generate_weekly_report_json(
                start_date=start_date, end_date=end_date, server_id=server_id,
                operation_type=operation_type, risk_level=risk_level, user_name=user_name
            )
            return jsonify({"status": "success", "data": report_data})
    except Exception as e:
        logging.error(f"生成每周报告失败: {str(e)}")
        return jsonify({'error': f'生成每周报告失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/reports/monthly', methods=['GET'])
def mysql_audit_get_monthly_report():
    """获取每月报告 API"""
    try:
        server_id_str = request.args.get('server_id')
        server_id = int(server_id_str) if server_id_str and server_id_str.strip() else None
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        operation_type = request.args.get('operation_type')
        risk_level = request.args.get('risk_level')
        user_name = request.args.get('user_name')

        start_date = None
        end_date = None
        if start_date_str and start_date_str.lower() != 'invalid date':
            try: start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            except ValueError: logging.warning(f"无效的开始日期格式: {start_date_str}")
        if end_date_str and end_date_str.lower() != 'invalid date':
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                if end_date: end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
            except ValueError: logging.warning(f"无效的结束日期格式: {end_date_str}")

        logging.info(f"月报生成筛选条件: server_id={server_id}, start_date={start_date}, end_date={end_date}, "
                    f"operation_type={operation_type}, risk_level={risk_level}, user_name={user_name}")

        if request.args.get('download') == 'true':
            report_file = ReportGenerator.generate_monthly_report(
                start_date=start_date, end_date=end_date, server_id=server_id,
                operation_type=operation_type, risk_level=risk_level, user_name=user_name
            )
            return send_file(report_file, as_attachment=True, download_name='monthly_report.xlsx')
        else:
            report_data = ReportGenerator.generate_monthly_report_json(
                start_date=start_date, end_date=end_date, server_id=server_id,
                operation_type=operation_type, risk_level=risk_level, user_name=user_name
            )
            return jsonify({"status": "success", "data": report_data})
    except Exception as e:
        logging.error(f"生成每月报告失败: {str(e)}")
        return jsonify({'error': f'生成每月报告失败: {str(e)}'}), 500

@mysql_audit_bp.route('/api/export', methods=['GET'])
def mysql_audit_export_activities():
    """导出活动数据 API"""
    try:
        server_id = request.args.get('server_id', type=int)
        start_date_str = request.args.get('start_date')
        end_date_str = request.args.get('end_date')
        operation_type = request.args.get('operation_type')
        risk_level = request.args.get('risk_level')
        user_name = request.args.get('user_name')
        file_format = request.args.get('format', 'excel')
        is_check = request.args.get('check') == 'true'

        start_date = None
        end_date = None
        if start_date_str and start_date_str.lower() != 'invalid date':
            try: start_date = datetime.strptime(start_date_str, '%Y-%m-%d')
            except ValueError: 
                logging.error(f"无效的 start_date 格式: {start_date_str}")
                return jsonify({'error': f'无效的开始日期格式: {start_date_str}'}), 400
        if end_date_str and end_date_str.lower() != 'invalid date':
            try:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d')
                if end_date: end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
            except ValueError: 
                logging.error(f"无效的 end_date 格式: {end_date_str}")
                return jsonify({'error': f'无效的结束日期格式: {end_date_str}'}), 400

        activities, count = get_user_activities(
            server_id=server_id if server_id else None,
            start_date=start_date,
            end_date=end_date,
            operation_type=operation_type if operation_type else None,
            risk_level=risk_level if risk_level else None,
            user_name=user_name if user_name else None,
            limit=None
        )
        
        if is_check:
            return jsonify({'status': 'success', 'count': count})

        if count == 0:
            return jsonify({'error': '没有符合条件的数据可导出'}), 404

        if file_format.lower() == 'excel':
            output = BytesIO()
            df = pd.DataFrame(activities)
            df.to_excel(output, index=False)
            output.seek(0)
            
            return send_file(
                output,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                as_attachment=True,
                download_name=f'mysql_audit_export_{start_date_str or "all"}_{end_date_str or "all"}.xlsx'
            )
        elif file_format.lower() == 'csv':
            output = StringIO()
            df = pd.DataFrame(activities)
            df.to_csv(output, index=False)
            output.seek(0)
            
            return send_file(
                BytesIO(output.getvalue().encode('utf-8-sig')),
                mimetype='text/csv',
                as_attachment=True,
                download_name=f'mysql_audit_export_{start_date_str or "all"}_{end_date_str or "all"}.csv'
            )
        else:
            return jsonify({'error': f'不支持的导出格式: {file_format}'}), 400

    except Exception as e:
        logging.error(f"导出活动数据失败: {str(e)}", exc_info=True)
        return jsonify({'error': f'导出活动数据失败: {str(e)}'}), 500 