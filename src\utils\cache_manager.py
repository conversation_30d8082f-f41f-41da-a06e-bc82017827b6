"""
缓存管理系统
提供多级缓存、智能失效、性能优化等功能
"""

import time
import json
import hashlib
import threading
from typing import Any, Dict, Optional, Callable, Union
from datetime import datetime, timedelta
from collections import OrderedDict
from dataclasses import dataclass, asdict
import logging
import functools
import pickle

logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """缓存条目"""
    key: str
    value: Any
    created_at: datetime
    expires_at: Optional[datetime]
    access_count: int = 0
    last_accessed: Optional[datetime] = None
    size_bytes: int = 0

class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
        self._cache: OrderedDict = OrderedDict()
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'expired': 0
        }
    
    def _generate_key(self, key: Union[str, tuple, dict]) -> str:
        """生成缓存键"""
        if isinstance(key, str):
            return key
        elif isinstance(key, (tuple, list)):
            return hashlib.md5(str(key).encode()).hexdigest()
        elif isinstance(key, dict):
            sorted_items = sorted(key.items())
            return hashlib.md5(str(sorted_items).encode()).hexdigest()
        else:
            return hashlib.md5(str(key).encode()).hexdigest()
    
    def _calculate_size(self, value: Any) -> int:
        """计算值的大小（字节）"""
        try:
            return len(pickle.dumps(value))
        except:
            return len(str(value).encode('utf-8'))
    
    def _is_expired(self, entry: CacheEntry) -> bool:
        """检查缓存条目是否过期"""
        if entry.expires_at is None:
            return False
        return datetime.now() > entry.expires_at
    
    def _evict_expired(self):
        """清理过期条目"""
        expired_keys = []
        for key, entry in self._cache.items():
            if self._is_expired(entry):
                expired_keys.append(key)
        
        for key in expired_keys:
            del self._cache[key]
            self._stats['expired'] += 1
    
    def _evict_lru(self):
        """清理最少使用的条目"""
        while len(self._cache) >= self.max_size:
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]
            self._stats['evictions'] += 1
    
    def get(self, key: Union[str, tuple, dict], default: Any = None) -> Any:
        """获取缓存值"""
        cache_key = self._generate_key(key)
        
        with self._lock:
            self._evict_expired()
            
            if cache_key in self._cache:
                entry = self._cache[cache_key]
                
                if self._is_expired(entry):
                    del self._cache[cache_key]
                    self._stats['expired'] += 1
                    self._stats['misses'] += 1
                    return default
                
                # 更新访问信息
                entry.access_count += 1
                entry.last_accessed = datetime.now()
                
                # 移到末尾（最近使用）
                self._cache.move_to_end(cache_key)
                
                self._stats['hits'] += 1
                return entry.value
            
            self._stats['misses'] += 1
            return default
    
    def set(self, key: Union[str, tuple, dict], value: Any, ttl_seconds: Optional[int] = None) -> bool:
        """设置缓存值"""
        cache_key = self._generate_key(key)
        ttl = ttl_seconds or self.ttl_seconds
        
        with self._lock:
            self._evict_expired()
            self._evict_lru()
            
            expires_at = datetime.now() + timedelta(seconds=ttl) if ttl > 0 else None
            size_bytes = self._calculate_size(value)
            
            entry = CacheEntry(
                key=cache_key,
                value=value,
                created_at=datetime.now(),
                expires_at=expires_at,
                access_count=0,
                size_bytes=size_bytes
            )
            
            self._cache[cache_key] = entry
            return True
    
    def delete(self, key: Union[str, tuple, dict]) -> bool:
        """删除缓存条目"""
        cache_key = self._generate_key(key)
        
        with self._lock:
            if cache_key in self._cache:
                del self._cache[cache_key]
                return True
            return False
    
    def clear(self):
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._stats = {
                'hits': 0,
                'misses': 0,
                'evictions': 0,
                'expired': 0
            }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = (self._stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            total_size = sum(entry.size_bytes for entry in self._cache.values())
            
            return {
                'size': len(self._cache),
                'max_size': self.max_size,
                'hit_rate': round(hit_rate, 2),
                'total_size_mb': round(total_size / (1024**2), 2),
                **self._stats
            }

class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self._caches: Dict[str, LRUCache] = {}
        self._lock = threading.RLock()
        
        # 创建默认缓存实例
        self.create_cache('default', max_size=1000, ttl_seconds=3600)
        self.create_cache('database', max_size=500, ttl_seconds=1800)
        self.create_cache('api', max_size=200, ttl_seconds=300)
        self.create_cache('session', max_size=100, ttl_seconds=7200)
    
    def create_cache(self, name: str, max_size: int = 1000, ttl_seconds: int = 3600) -> LRUCache:
        """创建新的缓存实例"""
        with self._lock:
            cache = LRUCache(max_size=max_size, ttl_seconds=ttl_seconds)
            self._caches[name] = cache
            logger.info(f"创建缓存实例: {name} (max_size={max_size}, ttl={ttl_seconds}s)")
            return cache
    
    def get_cache(self, name: str = 'default') -> Optional[LRUCache]:
        """获取缓存实例"""
        return self._caches.get(name)
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有缓存的统计信息"""
        with self._lock:
            return {
                name: cache.get_stats()
                for name, cache in self._caches.items()
            }
    
    def clear_all(self):
        """清空所有缓存"""
        with self._lock:
            for cache in self._caches.values():
                cache.clear()
            logger.info("已清空所有缓存")

# 全局缓存管理器实例
cache_manager = CacheManager()

def cached(cache_name: str = 'default', ttl_seconds: Optional[int] = None, 
          key_func: Optional[Callable] = None):
    """缓存装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            cache = cache_manager.get_cache(cache_name)
            if not cache:
                logger.warning(f"缓存实例不存在: {cache_name}")
                return func(*args, **kwargs)
            
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{func.__module__}.{func.__name__}:{args}:{sorted(kwargs.items())}"
            
            # 尝试从缓存获取
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {cache_key}")
                return cached_result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl_seconds)
            logger.debug(f"缓存设置: {cache_key}")
            
            return result
        
        # 添加缓存控制方法
        wrapper.cache_clear = lambda: cache_manager.get_cache(cache_name).clear()
        wrapper.cache_delete = lambda *args, **kwargs: cache_manager.get_cache(cache_name).delete(
            key_func(*args, **kwargs) if key_func else f"{func.__module__}.{func.__name__}:{args}:{sorted(kwargs.items())}"
        )
        
        return wrapper
    return decorator

def cache_key_generator(*key_parts):
    """缓存键生成器"""
    def generator(*args, **kwargs):
        parts = []
        for part in key_parts:
            if callable(part):
                parts.append(str(part(*args, **kwargs)))
            elif isinstance(part, int) and part < len(args):
                parts.append(str(args[part]))
            elif isinstance(part, str) and part in kwargs:
                parts.append(str(kwargs[part]))
            else:
                parts.append(str(part))
        return ':'.join(parts)
    return generator

# 常用缓存键生成器
def table_cache_key(table_name: str, *args, **kwargs):
    """表数据缓存键生成器"""
    return f"table:{table_name}:{hashlib.md5(str(args + tuple(sorted(kwargs.items()))).encode()).hexdigest()}"

def user_cache_key(user_id: str, *args, **kwargs):
    """用户数据缓存键生成器"""
    return f"user:{user_id}:{hashlib.md5(str(args + tuple(sorted(kwargs.items()))).encode()).hexdigest()}"
