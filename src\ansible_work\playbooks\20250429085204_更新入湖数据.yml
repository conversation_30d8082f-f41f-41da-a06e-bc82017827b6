---
- name: 传输并更新 Docker 容器中的 Excel 文件并执行 Python 脚本
  hosts: docker_server
  become: yes  # 使用 sudo 权限
  vars:
    excel_file: moban.xlsx  # Excel 文件名
    local_excel_path: /data/image_bak/moban.xlsx  # 7.3 上 Excel 文件的完整路径
    remote_excel_path: /tmp/moban.xlsx  # 7.49 上临时存储 Excel 文件的路径
    container_name: myapp  # Docker 容器名称
    container_excel_path: /app/moban.xlsx  # 容器内 Excel 文件的路径（若存在同名文件，将被覆盖）
    python_script: src/county_data/store_data.py  # 容器内 Python 脚本路径

  tasks:
    - name: 检查本地 Excel 文件是否存在
      ansible.builtin.stat:
        path: "{{ local_excel_path }}"
      delegate_to: localhost  # 在 7.3 上执行
      register: local_excel_stat
      failed_when: not local_excel_stat.stat.exists or local_excel_stat.stat.isdir  # 如果文件不存在或为目录，失败

    - name: 复制 Excel 文件到远程服务器
      ansible.builtin.copy:
        src: "{{ local_excel_path }}"
        dest: "{{ remote_excel_path }}"
        mode: '0644'

    - name: 检查远程 Excel 文件是否有效
      ansible.builtin.stat:
        path: "{{ remote_excel_path }}"
      register: remote_excel_stat
      failed_when: not remote_excel_stat.stat.exists or remote_excel_stat.stat.isdir  # 如果文件不存在或为目录，失败

    - name: 复制 Excel 文件到 Docker 容器（自动覆盖容器内同名文件）
      ansible.builtin.command: docker cp {{ remote_excel_path }} {{ container_name }}:{{ container_excel_path }}
      register: docker_cp_result
      changed_when: docker_cp_result.rc == 0
      failed_when: docker_cp_result.rc != 0  # 如果复制失败，任务失败

    - name: 执行容器内的 Python 脚本并捕获输出
      ansible.builtin.command: docker exec {{ container_name }} python {{ python_script }}
      register: python_result
      changed_when: python_result.rc == 0
      failed_when: python_result.rc != 0  # 如果脚本执行失败，任务失败

    - name: 显示 Python 脚本的输出
      ansible.builtin.debug:
        msg:
          - "Python 脚本标准输出 (stdout): {{ python_result.stdout }}"
          - "Python 脚本标准错误 (stderr): {{ python_result.stderr }}"
      when: python_result.rc == 0  # 仅在脚本成功执行时显示输出

    - name: 清理远程服务器上的 Excel 文件
      ansible.builtin.file:
        path: "{{ remote_excel_path }}"
        state: absent