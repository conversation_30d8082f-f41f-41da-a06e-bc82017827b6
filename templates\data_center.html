<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据中台 - 数据治理系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/custom.css') }}">
    <style>
        .sidebar {
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 100;
        }
        .main-content {
            margin-left: 16.666667%;
        }
        .card {
            margin-bottom: 20px;
        }
        .saved-config.active {
            background-color: #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 左侧导航 -->
            <div class="col-md-2 bg-dark sidebar">
                <div class="d-flex flex-column p-3 text-white">
                    <a href="/" class="d-flex align-items-center mb-3 mb-md-0 me-md-auto text-white text-decoration-none">
                        <i class="fa fa-database me-2"></i>
                        <span class="fs-4">数据治理系统</span>
                    </a>
                    <hr>
                    <ul class="nav nav-pills flex-column mb-auto">
                        <li class="nav-item">
                            <a href="/" class="nav-link text-white">
                                <i class="fa fa-home me-2"></i>
                                首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/data_center" class="nav-link active">
                                <i class="fa fa-desktop me-2"></i>
                                数据中台
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 主要内容 -->
            <div class="col-md-10 main-content">
                <div class="container mt-4">
                    <h2 class="mb-4"><i class="fa fa-desktop me-2"></i>数据中台</h2>
                    
                    <div class="row">
                        <!-- 左侧服务器信息 -->
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">服务器信息</h5>
                                </div>
                                <div class="card-body">
                                    <form id="serverForm">
                                        <div class="mb-3">
                                            <label for="serverIp" class="form-label">服务器IP</label>
                                            <input type="text" class="form-control" id="serverIp" placeholder="例如: ***********">
                                        </div>
                                        <div class="mb-3">
                                            <label for="serverPort" class="form-label">端口</label>
                                            <input type="text" class="form-control" id="serverPort" placeholder="例如: 3306">
                                        </div>
                                        <div class="mb-3">
                                            <label for="serverAccount" class="form-label">账号</label>
                                            <input type="text" class="form-control" id="serverAccount" placeholder="例如: root">
                                        </div>
                                        <div class="mb-3">
                                            <label for="serverPassword" class="form-label">密码</label>
                                            <input type="password" class="form-control" id="serverPassword">
                                        </div>
                                        <div class="d-grid gap-2">
                                            <button type="button" class="btn btn-primary" id="testConnection">
                                                <i class="fa fa-plug me-1"></i> 测试连接
                                            </button>
                                            <button type="button" class="btn btn-success" id="saveConfig">
                                                <i class="fa fa-save me-1"></i> 保存配置
                                            </button>
                                            <button type="button" class="btn btn-info" id="getDatabases">
                                                <i class="fa fa-database me-1"></i> 获取数据库
                                            </button>
                                        </div>
                                    </form>
                                    <div id="connection-result" class="mt-3"></div>
                                </div>
                            </div>
                            
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0">已保存的数据库配置</h5>
                                </div>
                                <div class="card-body">
                                    <div id="savedConfigs">
                                        <div class="d-flex justify-content-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 右侧数据库信息 -->
                        <div class="col-md-8">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <h5 class="mb-0">数据库信息</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="databaseSelect" class="form-label">选择数据库</label>
                                            <select class="form-select" id="databaseSelect">
                                                <option value="">请先获取数据库</option>
                                            </select>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="tableSelect" class="form-label">选择表</label>
                                            <select class="form-select" id="tableSelect">
                                                <option value="">请先选择数据库</option>
                                                <option value="all" selected>全部表</option>
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div>
                                                <button type="button" class="btn btn-sm btn-primary me-2" id="countRecords">
                                                    <i class="fa fa-calculator"></i> 计算表条数
                                                </button>
                                                <button type="button" class="btn btn-sm btn-info" id="copyTableInfo">
                                                    <i class="fa fa-copy"></i> 复制表信息
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="alert alert-info" id="tableCount">
                                        <i class="fa fa-info-circle"></i> 选择数据库和表以查看记录数量
                                    </div>
                                    
                                    <div id="tableInfoResult" class="mt-3">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载必要的JavaScript库 -->
    <script src="{{ url_for('static', filename='js/lib/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/lib/bootstrap.bundle.min.js') }}"></script>
    
    <!-- 数据中台功能脚本 -->
    <script>
        $(document).ready(function() {
            // 存储当前服务器数据
            let serverData = {
                ip: '',
                port: '',
                account: '',
                password: '',
                database: ''
            };
            
            // 加载保存的配置
            loadSavedConfigs();
            
            // 测试连接
            $('#testConnection').on('click', function() {
                // 获取表单数据
                serverData.ip = $('#serverIp').val();
                serverData.port = $('#serverPort').val();
                serverData.account = $('#serverAccount').val();
                serverData.password = $('#serverPassword').val();
                
                // 验证必填字段
                if (!serverData.ip || !serverData.port || !serverData.account || !serverData.password) {
                    $('#connection-result').html(
                        '<div class="alert alert-danger">' + 
                        '<i class="fa fa-times-circle"></i> 请填写所有服务器信息' + 
                        '</div>'
                    );
                    return;
                }
                
                // 发送测试连接请求
                $.ajax({
                    url: '/api/data_center/test_connection',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(serverData),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            $('#connection-result').html(
                                '<div class="alert alert-success">' + 
                                '<i class="fa fa-check-circle"></i> ' + response.message + 
                                '</div>'
                            );
                        } else {
                            $('#connection-result').html(
                                '<div class="alert alert-danger">' + 
                                '<i class="fa fa-times-circle"></i> ' + response.message + 
                                '</div>'
                            );
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = '连接失败';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg = response.message || errorMsg;
                        } catch (e) {}
                        
                        $('#connection-result').html(
                            '<div class="alert alert-danger">' + 
                            '<i class="fa fa-times-circle"></i> ' + errorMsg + 
                            '</div>'
                        );
                    }
                });
            });
            
            // 保存配置
            $('#saveConfig').on('click', function() {
                const configData = {
                    ip: $('#serverIp').val().trim(),
                    port: $('#serverPort').val().trim(),
                    account: $('#serverAccount').val().trim(),
                    password: $('#serverPassword').val().trim()
                };
                
                if(!configData.ip || !configData.port || !configData.account || !configData.password) {
                    $('#connection-result').html('<div class="alert alert-danger">请填写完整的服务器信息</div>');
                    return;
                }
                
                $.ajax({
                    url: '/api/data_center/save_config',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(configData),
                    success: function(response) {
                        if (response.success) {
                            $('#connection-result').html(
                                '<div class="alert alert-success">' + 
                                '<i class="fa fa-check-circle"></i> ' + response.message + 
                                '</div>'
                            );
                            
                            // 重新加载保存的配置
                            loadSavedConfigs();
                        } else {
                            $('#connection-result').html(
                                '<div class="alert alert-danger">' + 
                                '<i class="fa fa-times-circle"></i> ' + response.message + 
                                '</div>'
                            );
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = '保存失败';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg = response.message || errorMsg;
                        } catch (e) {}
                        
                        $('#connection-result').html(
                            '<div class="alert alert-danger">' + 
                            '<i class="fa fa-times-circle"></i> ' + errorMsg + 
                            '</div>'
                        );
                    }
                });
            });
            
            // 获取数据库
            $('#getDatabases').on('click', function() {
                // 验证必填字段
                if (!serverData.ip || !serverData.port || !serverData.account || !serverData.password) {
                    $('#connection-result').html(
                        '<div class="alert alert-danger">' + 
                        '<i class="fa fa-times-circle"></i> 请填写所有服务器信息' + 
                        '</div>'
                    );
                    return;
                }
                
                $('#connection-result').html(
                    '<div class="alert alert-info">' + 
                    '<i class="fa fa-spinner fa-spin"></i> 正在获取数据库列表...' + 
                    '</div>'
                );
                
                $.ajax({
                    url: '/api/data_center/get_databases',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(serverData),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            const databases = response.databases;
                            
                            let options = '<option value="">请选择数据库</option>';
                            databases.forEach(function(db) {
                                options += '<option value="' + db + '">' + db + '</option>';
                            });
                            
                            $('#databaseSelect').html(options);
                            
                            $('#connection-result').html(
                                '<div class="alert alert-success">' + 
                                '<i class="fa fa-check-circle"></i> 成功获取 ' + databases.length + ' 个数据库' + 
                                '</div>'
                            );
                        } else {
                            $('#connection-result').html(
                                '<div class="alert alert-danger">' + 
                                '<i class="fa fa-times-circle"></i> ' + response.message + 
                                '</div>'
                            );
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = '获取数据库失败';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg = response.message || errorMsg;
                        } catch (e) {}
                        
                        $('#connection-result').html(
                            '<div class="alert alert-danger">' + 
                            '<i class="fa fa-times-circle"></i> ' + errorMsg + 
                            '</div>'
                        );
                    }
                });
            });
            
            // 选择数据库时加载表
            $('#databaseSelect').on('change', function() {
                const database = $(this).val();
                
                if (!database) {
                    $('#tableSelect').html('<option value="">请选择数据库</option>');
                    return;
                }
                
                // 更新当前数据库
                serverData.database = database;
                
                // 保存数据库名称到配置中
                updateDatabaseName(serverData);
                
                $('#tableSelect').html('<option value="">加载中...</option>');
                
                $.ajax({
                    url: '/api/data_center/get_tables',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(serverData),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            const tables = response.tables;
                            
                            let options = '<option value="">请选择表</option>';
                            options += '<option value="all" selected>全部表</option>';
                            tables.forEach(function(table) {
                                options += '<option value="' + table.name + '">' + table.name + (table.comment ? ' (' + table.comment + ')' : '') + '</option>';
                            });
                            
                            $('#tableSelect').html(options);
                        } else {
                            $('#connection-result').html(
                                '<div class="alert alert-danger">' + 
                                '<i class="fa fa-times-circle"></i> ' + response.message + 
                                '</div>'
                            );
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = '获取表失败';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg = response.message || errorMsg;
                        } catch (e) {}
                        
                        $('#connection-result').html(
                            '<div class="alert alert-danger">' + 
                            '<i class="fa fa-times-circle"></i> ' + errorMsg + 
                            '</div>'
                        );
                    }
                });
            });
            
            // 选择表时显示表信息
            $('#tableSelect').on('change', function() {
                const table = $(this).val();
                
                // 清空表信息
                $('#tableInfo').html('');
                $('#tableCount').html('');
                
                if (!serverData.database) {
                    return;
                }
                
                // 如果选择了表，则获取表信息
                if (table && table !== 'all') {
                    serverData.table = table;
                    getTableInfo({
                        ...serverData,
                        table: table
                    });
                } else {
                    // 如果选择了"全部表"，则清除当前表
                    delete serverData.table;
                }
            });
            
            // 获取表信息
            function getTableInfo(serverData) {
                $.ajax({
                    url: '/api/data_center/get_table_info',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(serverData),
                    success: function(response) {
                        if (response.success) {
                            const data = response.data;
                            
                            if (data.type === 'database') {
                                // 显示数据库信息
                                let infoHtml = '<h5>数据库: ' + serverData.database + '</h5>';
                                infoHtml += '<p>表数量: ' + data.tables.length + '</p>';
                                infoHtml += '<p>总记录数: ' + data.count + '</p>';
                                infoHtml += '<h6>表列表:</h6><ul>';
                                
                                data.tables.forEach(function(table) {
                                    infoHtml += '<li>' + table + '</li>';
                                });
                                
                                infoHtml += '</ul>';
                                
                                $('#tableInfo').html(infoHtml);
                                $('#tableCount').html('数据库 "' + serverData.database + '" 中所有表共有 ' + data.count + ' 条记录');
                            } else if (data.type === 'table') {
                                // 获取表的注释
                                $.ajax({
                                    url: '/api/data_center/get_table_count',
                                    type: 'POST',
                                    contentType: 'application/json',
                                    data: JSON.stringify({
                                        ...serverData,
                                        table: serverData.table
                                    }),
                                    success: function(commentResponse) {
                                        if (commentResponse.success) {
                                            const tableData = commentResponse.data;
                                            const comment = tableData.comment || '';
                                            const count = data.count;
                                            
                                            // 显示单个表的记录数
                                            let resultHtml = '<div class="alert alert-success">';
                                            resultHtml += '<div class="d-flex justify-content-between align-items-center">';
                                            resultHtml += '<span><i class="fa fa-info-circle"></i> 表 <strong>' + serverData.table + '</strong> 共有 <strong>' + count + '</strong> 条记录</span>';
                                            resultHtml += '<button type="button" class="btn btn-sm btn-primary" id="copyTableCountResults"><i class="fa fa-copy"></i> 复制结果</button>';
                                            resultHtml += '</div>';
                                            resultHtml += '</div>';
                                            
                                            // 使用卡片样式显示单个表信息
                                            resultHtml += '<div class="mt-3">';
                                            resultHtml += '<div class="card mb-2">';
                                            resultHtml += '<div class="card-body py-2 d-flex justify-content-between align-items-center">';
                                            resultHtml += '<div class="formatted-result">';
                                            const formattedResult = serverData.table + ':' + (comment || serverData.table) + ':' + count;
                                            resultHtml += '<strong>' + formattedResult + '</strong>';
                                            resultHtml += '</div>';
                                            resultHtml += '<button type="button" class="btn btn-sm btn-outline-primary copy-single-table-count" data-table="' + serverData.table + '" data-comment="' + (comment || serverData.table) + '" data-count="' + count + '"><i class="fa fa-copy"></i></button>';
                                            resultHtml += '</div>';
                                            resultHtml += '</div>';
                                            resultHtml += '</div>';
                                            
                                            $('#tableCount').html(resultHtml);
                                            
                                            // 添加复制按钮的点击事件
                                            $('#copyTableCountResults').on('click', function() {
                                                let copyText = serverData.table + ':' + (comment || serverData.table) + ':' + count;
                                                copyToClipboard(copyText);
                                                
                                                // 显示复制成功提示
                                                const $btn = $(this);
                                                const originalHtml = $btn.html();
                                                $btn.html('<i class="fa fa-check"></i> 已复制');
                                                setTimeout(function() {
                                                    $btn.html(originalHtml);
                                                }, 2000);
                                            });
                                            
                                            // 添加单个表复制按钮的点击事件
                                            $('.copy-single-table-count').on('click', function() {
                                                const $btn = $(this);
                                                const tableName = $btn.data('table');
                                                const tableComment = $btn.data('comment');
                                                const tableCount = $btn.data('count');
                                                
                                                let copyText = tableName + ':' + tableComment + ':' + tableCount;
                                                copyToClipboard(copyText);
                                                
                                                // 显示复制成功提示
                                                const originalHtml = $btn.html();
                                                $btn.html('<i class="fa fa-check"></i>');
                                                setTimeout(function() {
                                                    $btn.html(originalHtml);
                                                }, 2000);
                                            });
                                            
                                            // 不再显示表结构信息
                                            $('#tableInfoResult').html('');
                                        }
                                    }
                                });
                            }
                        } else {
                            $('#connection-result').html(
                                '<div class="alert alert-danger">' + 
                                '<i class="fa fa-times-circle"></i> ' + response.message + 
                                '</div>'
                            );
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = '获取信息失败';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg = response.message || errorMsg;
                        } catch (e) {}
                        
                        $('#connection-result').html(
                            '<div class="alert alert-danger">' + 
                            '<i class="fa fa-times-circle"></i> ' + errorMsg + 
                            '</div>'
                        );
                    }
                });
            }
            
            // 复制表信息
            function copyTableInfo() {
                const database = $('#databaseSelect').val();
                const table = $('#tableSelect').val();
                
                if (!database) {
                    $('#tableInfoResult').html(
                        '<div class="alert alert-warning">' + 
                        '<i class="fa fa-exclamation-triangle"></i> 请先选择数据库' + 
                        '</div>'
                    );
                    return;
                }
                
                // 确保serverData中包含数据库名
                serverData.database = database;
                
                $('#tableInfoResult').html(
                    '<div class="d-flex justify-content-center">' +
                    '<div class="spinner-border text-primary" role="status">' +
                    '<span class="visually-hidden">获取中...</span>' +
                    '</div>' +
                    '</div>'
                );
                
                // 调用API获取表信息
                $.ajax({
                    url: '/api/data_center/get_tables',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(serverData),
                    success: function(response) {
                        if (response.success) {
                            const tables = response.tables;
                            let tableInfoText = '';
                            
                            if (table === 'all' || !table) {
                                // 获取所有表的注释
                                tables.forEach(function(tableInfo) {
                                    tableInfoText += tableInfo.name + ':' + (tableInfo.comment || tableInfo.name) + ':' + (tableInfo.count || 0) + '\n';
                                });
                            } else {
                                // 获取单个表的注释
                                const tableInfo = tables.find(t => t.name === table);
                                
                                if (tableInfo) {
                                    tableInfoText = tableInfo.name + ':' + (tableInfo.comment || tableInfo.name) + ':' + (tableInfo.count || 0);
                                } else {
                                    $('#tableInfoResult').html(
                                        '<div class="alert alert-warning">' + 
                                        '<i class="fa fa-exclamation-triangle"></i> 未找到表信息' + 
                                        '</div>'
                                    );
                                    return;
                                }
                            }
                            
                            // 复制到剪贴板
                            copyToClipboard(tableInfoText);
                            
                            $('#tableInfoResult').html(
                                '<div class="alert alert-success">' + 
                                '<i class="fa fa-check-circle"></i> 表信息已复制到剪贴板' + 
                                '</div>' +
                                '<pre class="mt-2 p-2 border rounded bg-light">' + tableInfoText + '</pre>'
                            );
                        } else {
                            $('#tableInfoResult').html(
                                '<div class="alert alert-danger">' + 
                                '<i class="fa fa-times-circle"></i> ' + response.message + 
                                '</div>'
                            );
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = '获取表信息失败';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg = response.message || errorMsg;
                        } catch (e) {}
                        
                        $('#tableInfoResult').html(
                            '<div class="alert alert-danger">' + 
                            '<i class="fa fa-times-circle"></i> ' + errorMsg + 
                            '</div>'
                        );
                    }
                });
            }
            
            // 复制文本到剪贴板
            function copyToClipboard(text) {
                const textarea = document.createElement('textarea');
                textarea.value = text;
                document.body.appendChild(textarea);
                textarea.select();
                document.execCommand('copy');
                document.body.removeChild(textarea);
            }
            
            // 计算表条数
            $('#countRecords').on('click', function() {
                const database = $('#databaseSelect').val();
                const table = $('#tableSelect').val();
                
                if (!database) {
                    $('#tableCount').html(
                        '<div class="alert alert-info">' + 
                        '<i class="fa fa-info-circle"></i> 请从下拉菜单中选择一个具体的表，然后再点击"计算条数"按钮' + 
                        '</div>'
                    );
                    return;
                }
                
                if (!table || table === 'all') {
                    // 计算所有表的记录数
                    $('#tableCount').html(
                        '<div class="alert alert-info">' +
                        '<i class="fa fa-spinner fa-spin"></i> 正在计算所有表的记录数，请稍候...' +
                        '<div id="progressContainer" class="mt-2"></div>' +
                        '</div>'
                    );
                    
                    $.ajax({
                        url: '/api/data_center/get_all_tables_count',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            ...serverData,
                            database: database
                        }),
                        success: function(response) {
                            if (response.success) {
                                const data = response.data;
                                const tables = data.tables;
                                const totalCount = data.total_count;
                                
                                // 显示所有表的记录数
                                let resultHtml = '<div class="alert alert-success">';
                                resultHtml += '<div class="d-flex justify-content-between align-items-center">';
                                resultHtml += '<span><i class="fa fa-info-circle"></i> 数据库 <strong>' + database + '</strong> 中共有 <strong>' + tables.length + '</strong> 个表，总计 <strong>' + totalCount + '</strong> 条记录</span>';
                                resultHtml += '<button type="button" class="btn btn-sm btn-primary" id="copyAllTablesCountResults"><i class="fa fa-copy"></i> 复制结果</button>';
                                resultHtml += '</div>';
                                resultHtml += '</div>';
                                
                                // 显示每个表的记录数（简洁版，不使用表格）
                                resultHtml += '<div class="mt-3">';
                                tables.forEach(function(table) {
                                    const formattedResult = table.name + ':' + (table.comment || table.name) + ':' + table.count;
                                    resultHtml += '<div class="card mb-2">';
                                    resultHtml += '<div class="card-body py-2 d-flex justify-content-between align-items-center">';
                                    resultHtml += '<div class="formatted-result">';
                                    resultHtml += '<strong>' + formattedResult + '</strong>';
                                    resultHtml += '</div>';
                                    resultHtml += '<button type="button" class="btn btn-sm btn-outline-primary copy-table-count" data-table="' + table.name + '" data-comment="' + (table.comment || table.name) + '" data-count="' + table.count + '"><i class="fa fa-copy"></i></button>';
                                    resultHtml += '</div>';
                                    resultHtml += '</div>';
                                });
                                resultHtml += '</div>';
                                
                                $('#tableCount').html(resultHtml);
                                
                                // 添加复制所有表记录数的点击事件
                                $('#copyAllTablesCountResults').on('click', function() {
                                    let copyText = '';
                                    tables.forEach(function(table) {
                                        copyText += table.name + ':' + (table.comment || table.name) + ':' + table.count + '\n';
                                    });
                                    copyToClipboard(copyText);
                                    
                                    // 显示复制成功提示
                                    const $btn = $(this);
                                    const originalHtml = $btn.html();
                                    $btn.html('<i class="fa fa-check"></i> 已复制');
                                    setTimeout(function() {
                                        $btn.html(originalHtml);
                                    }, 2000);
                                });
                                
                                // 添加复制单个表记录数的点击事件
                                $('.copy-table-count').on('click', function() {
                                    const $btn = $(this);
                                    const tableName = $btn.data('table');
                                    const tableComment = $btn.data('comment');
                                    const tableCount = $btn.data('count');
                                    
                                    let copyText = tableName + ':' + tableComment + ':' + tableCount;
                                    copyToClipboard(copyText);
                                    
                                    // 显示复制成功提示
                                    const originalHtml = $btn.html();
                                    $btn.html('<i class="fa fa-check"></i>');
                                    setTimeout(function() {
                                        $btn.html(originalHtml);
                                    }, 2000);
                                });
                                
                                // 清空表结构显示区域
                                $('#tableInfoResult').html('');
                            } else {
                                $('#tableCount').html(
                                    '<div class="alert alert-danger">' + 
                                    '<i class="fa fa-times-circle"></i> ' + (response.message || '获取表记录数失败') + 
                                    '</div>'
                                );
                            }
                        },
                        error: function(xhr) {
                            let errorMsg = '获取表记录数失败';
                            try {
                                const response = JSON.parse(xhr.responseText);
                                errorMsg = response.message || errorMsg;
                            } catch (e) {}
                            
                            $('#tableCount').html(
                                '<div class="alert alert-danger">' + 
                                '<i class="fa fa-times-circle"></i> ' + errorMsg + 
                                '</div>'
                            );
                        }
                    });
                    
                    return;
                }
                
                // 如果选择了单个表，直接获取该表的记录数
                if (table !== 'all') {
                    // 确保serverData中包含数据库名
                    serverData.database = database;
                    
                    $('#tableCount').html(
                        '<div class="alert alert-info">' +
                        '<i class="fa fa-spinner fa-spin"></i> 正在计算表记录数，请稍候...' +
                        '<div id="progressContainer" class="mt-2"></div>' +
                        '</div>'
                    );
                    
                    // 获取选择的表的记录数
                    $.ajax({
                        url: '/api/data_center/get_table_count',
                        type: 'POST',
                        contentType: 'application/json',
                        data: JSON.stringify({
                            ...serverData,
                            table: table
                        }),
                        success: function(response) {
                            if (response.success) {
                                const tableData = response.data;
                                const count = tableData.count;
                                const comment = tableData.comment || '';
                                
                                // 显示单个表的记录数
                                let resultHtml = '<div class="alert alert-success">';
                                resultHtml += '<div class="d-flex justify-content-between align-items-center">';
                                resultHtml += '<span><i class="fa fa-info-circle"></i> 表 <strong>' + table + '</strong> 共有 <strong>' + count + '</strong> 条记录</span>';
                                resultHtml += '<button type="button" class="btn btn-sm btn-primary" id="copyTableCountResults"><i class="fa fa-copy"></i> 复制结果</button>';
                                resultHtml += '</div>';
                                resultHtml += '</div>';
                                
                                // 使用卡片样式显示单个表信息
                                resultHtml += '<div class="mt-3">';
                                resultHtml += '<div class="card mb-2">';
                                resultHtml += '<div class="card-body py-2 d-flex justify-content-between align-items-center">';
                                resultHtml += '<div class="formatted-result">';
                                const formattedResult = table + ':' + (comment || table) + ':' + count;
                                resultHtml += '<strong>' + formattedResult + '</strong>';
                                resultHtml += '</div>';
                                resultHtml += '<button type="button" class="btn btn-sm btn-outline-primary copy-single-table-count" data-table="' + table + '" data-comment="' + (comment || table) + '" data-count="' + count + '"><i class="fa fa-copy"></i></button>';
                                resultHtml += '</div>';
                                resultHtml += '</div>';
                                resultHtml += '</div>';
                                
                                $('#tableCount').html(resultHtml);
                                
                                // 添加复制按钮的点击事件
                                $('#copyTableCountResults').on('click', function() {
                                    let copyText = table + ':' + (comment || table) + ':' + count;
                                    copyToClipboard(copyText);
                                    
                                    // 显示复制成功提示
                                    const $btn = $(this);
                                    const originalHtml = $btn.html();
                                    $btn.html('<i class="fa fa-check"></i> 已复制');
                                    setTimeout(function() {
                                        $btn.html(originalHtml);
                                    }, 2000);
                                });
                                
                                // 添加单个表复制按钮的点击事件
                                $('.copy-single-table-count').on('click', function() {
                                    const $btn = $(this);
                                    const tableName = $btn.data('table');
                                    const tableComment = $btn.data('comment');
                                    const tableCount = $btn.data('count');
                                    
                                    let copyText = tableName + ':' + tableComment + ':' + tableCount;
                                    copyToClipboard(copyText);
                                    
                                    // 显示复制成功提示
                                    const originalHtml = $btn.html();
                                    $btn.html('<i class="fa fa-check"></i>');
                                    setTimeout(function() {
                                        $btn.html(originalHtml);
                                    }, 2000);
                                });
                                
                                // 不再获取表结构信息
                                $('#tableInfoResult').html('');
                            } else {
                                $('#tableCount').html(
                                    '<div class="alert alert-danger">' + 
                                    '<i class="fa fa-times-circle"></i> ' + response.message + 
                                    '</div>'
                                );
                            }
                        },
                        error: function(xhr) {
                            let errorMsg = '获取表条数失败';
                            try {
                                const response = JSON.parse(xhr.responseText);
                                errorMsg = response.message || errorMsg;
                            } catch (e) {}
                            
                            $('#tableCount').html(
                                '<div class="alert alert-danger">' + 
                                '<i class="fa fa-times-circle"></i> ' + errorMsg + 
                                '</div>'
                            );
                        }
                    });
                    return;
                }
                
                // 获取所有表名，但不计算记录数
                $.ajax({
                    url: '/api/data_center/get_table_info',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify({
                        ...serverData,
                        table: ''
                    }),
                    success: function(response) {
                        if (response.success) {
                            const data = response.data;
                            
                            if (data.type === 'database' && data.tables.length > 0) {
                                const tables = data.tables;
                                let totalTables = tables.length;
                                let completedTables = 0;
                                let totalRecords = 0;
                                let tableResults = [];
                                
                                // 初始化进度显示
                                let progressHtml = '<div class="progress" style="height: 20px;">';
                                progressHtml += '<div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" ';
                                progressHtml += 'aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>';
                                progressHtml += '</div>';
                                progressHtml += '<div class="mt-2 small">已完成: 0/' + totalTables + ' 表</div>';
                                progressHtml += '<div id="tableCountList" class="mt-2"><ul class="list-group"></ul></div>';
                                
                                $('#progressContainer').html(progressHtml);
                                
                                // 逐个获取表的记录数
                                function getNextTableCount(index) {
                                    if (index >= tables.length) {
                                        // 所有表都已处理完成
                                        let finalHtml = '<div class="alert alert-success">';
                                        finalHtml += '<div class="d-flex justify-content-between align-items-center">';
                                        finalHtml += '<span><i class="fa fa-info-circle"></i> 数据库 <strong>' + serverData.database + '</strong> 中所有表共有 <strong>' + totalRecords + '</strong> 条记录</span>';
                                        finalHtml += '<button type="button" class="btn btn-sm btn-primary" id="copyTableCountResults"><i class="fa fa-copy"></i> 复制结果</button>';
                                        finalHtml += '</div>';
                                        finalHtml += '</div>';
                                        
                                        // 显示每个表的记录数（简洁版，不使用表格）
                                        finalHtml += '<div class="mt-3">';
                                        tables.forEach(function(table) {
                                            const formattedResult = table.name + ':' + (table.comment || table.name) + ':' + table.count;
                                            finalHtml += '<div class="card mb-2">';
                                            finalHtml += '<div class="card-body py-2 d-flex justify-content-between align-items-center">';
                                            finalHtml += '<div class="formatted-result">';
                                            finalHtml += '<strong>' + formattedResult + '</strong>';
                                            finalHtml += '</div>';
                                            finalHtml += '<button type="button" class="btn btn-sm btn-outline-primary copy-table-count" data-table="' + table.name + '" data-comment="' + (table.comment || table.name) + '" data-count="' + table.count + '"><i class="fa fa-copy"></i></button>';
                                            finalHtml += '</div>';
                                            finalHtml += '</div>';
                                        });
                                        finalHtml += '</div>';
                                        
                                        $('#tableCount').html(finalHtml);
                                        
                                        // 添加复制所有表记录数的点击事件
                                        $('#copyTableCountResults').on('click', function() {
                                            let copyText = '';
                                            tables.forEach(function(table) {
                                                copyText += table.name + ':' + (table.comment || table.name) + ':' + table.count + '\n';
                                            });
                                            copyToClipboard(copyText);
                                            
                                            // 显示复制成功提示
                                            const $btn = $(this);
                                            const originalHtml = $btn.html();
                                            $btn.html('<i class="fa fa-check"></i> 已复制');
                                            setTimeout(function() {
                                                $btn.html(originalHtml);
                                            }, 2000);
                                        });
                                        
                                        // 添加复制单个表记录数的点击事件
                                        $('.copy-table-count').on('click', function() {
                                            const $btn = $(this);
                                            const tableName = $btn.data('table');
                                            const tableComment = $btn.data('comment');
                                            const tableCount = $btn.data('count');
                                            
                                            let copyText = tableName + ':' + tableComment + ':' + tableCount;
                                            copyToClipboard(copyText);
                                            
                                            // 显示复制成功提示
                                            const originalHtml = $btn.html();
                                            $btn.html('<i class="fa fa-check"></i>');
                                            setTimeout(function() {
                                                $btn.html(originalHtml);
                                            }, 2000);
                                        });
                                        
                                        return;
                                    }
                                    
                                    const tableName = tables[index];
                                    
                                    // 调用API获取单个表的记录数
                                    $.ajax({
                                        url: '/api/data_center/get_table_count',
                                        type: 'POST',
                                        contentType: 'application/json',
                                        data: JSON.stringify({
                                            ...serverData,
                                            table: tableName
                                        }),
                                        success: function(response) {
                                            if (response.success) {
                                                const tableData = response.data;
                                                const count = tableData.count;
                                                
                                                // 更新总记录数
                                                totalRecords += count;
                                                
                                                // 保存表结果
                                                tableResults.push({
                                                    name: tableName,
                                                    comment: tableData.comment || '',
                                                    count: count
                                                });
                                                
                                                // 添加到列表显示
                                                $('#tableCountList ul').prepend(
                                                    '<li class="list-group-item">' +
                                                    tableName + ':' + (tableData.comment || tableName) + ':' + count +
                                                    '</li>'
                                                );
                                            } else {
                                                // 保存表结果（失败）
                                                tableResults.push({
                                                    name: tableName,
                                                    comment: '',
                                                    count: 0
                                                });
                                                
                                                // 添加到列表显示
                                                $('#tableCountList ul').prepend(
                                                    '<li class="list-group-item">' +
                                                    tableName + ':' + tableName + ':获取失败' +
                                                    '</li>'
                                                );
                                            }
                                            
                                            // 更新进度
                                            completedTables++;
                                            const progress = Math.round((completedTables / totalTables) * 100);
                                            $('.progress-bar').css('width', progress + '%');
                                            $('.progress-bar').attr('aria-valuenow', progress);
                                            $('.progress-bar').text(progress + '%');
                                            $('.small').text('已完成: ' + completedTables + '/' + totalTables + ' 表');
                                            
                                            // 处理下一个表
                                            getNextTableCount(index + 1);
                                        },
                                        error: function() {
                                            // 保存表结果（失败）
                                            tableResults.push({
                                                name: tableName,
                                                comment: '',
                                                count: 0
                                            });
                                            
                                            // 添加到列表显示
                                            $('#tableCountList ul').prepend(
                                                '<li class="list-group-item">' +
                                                tableName + ':' + tableName + ':获取失败' +
                                                '</li>'
                                            );
                                            
                                            // 更新进度
                                            completedTables++;
                                            const progress = Math.round((completedTables / totalTables) * 100);
                                            $('.progress-bar').css('width', progress + '%');
                                            $('.progress-bar').attr('aria-valuenow', progress);
                                            $('.progress-bar').text(progress + '%');
                                            $('.small').text('已完成: ' + completedTables + '/' + totalTables + ' 表');
                                            
                                            // 处理下一个表
                                            getNextTableCount(index + 1);
                                        }
                                    });
                                }
                                
                                // 开始处理第一个表
                                getNextTableCount(0);
                            } else {
                                $('#tableCount').html(
                                    '<div class="alert alert-warning">' + 
                                    '<i class="fa fa-exclamation-triangle"></i> 数据库中没有表' + 
                                    '</div>'
                                );
                            }
                        } else {
                            $('#tableCount').html(
                                '<div class="alert alert-danger">' + 
                                '<i class="fa fa-times-circle"></i> ' + response.message + 
                                '</div>'
                            );
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = '获取表列表失败';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg = response.message || errorMsg;
                        } catch (e) {}
                        
                        $('#tableCount').html(
                            '<div class="alert alert-danger">' + 
                            '<i class="fa fa-times-circle"></i> ' + errorMsg + 
                            '</div>'
                        );
                    }
                });
            });
            
            // 加载保存的数据库配置
            function loadSavedConfigs() {
                $.ajax({
                    url: '/api/data_center/get_saved_configs',
                    type: 'GET',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success && response.configs.length > 0) {
                            let configsHtml = '<div class="list-group">';
                            
                            response.configs.forEach(function(config) {
                                configsHtml += `
                                    <a href="#" class="list-group-item list-group-item-action saved-config" 
                                       data-id="${config.id}" 
                                       data-ip="${config.server_ip}" 
                                       data-port="${config.server_port}" 
                                       data-account="${config.server_account}" 
                                       data-password="${config.server_password}"
                                       data-database="${config.database_name || ''}">
                                        <div class="d-flex w-100 justify-content-between">
                                            <h6 class="mb-1">${config.server_ip}:${config.server_port}</h6>
                                            <small>${config.updated_at}</small>
                                        </div>
                                        <p class="mb-1">账号: ${config.server_account}</p>
                                        <small>${config.database_name ? '数据库: ' + config.database_name : '未指定数据库'}</small>
                                    </a>
                                `;
                            });
                            
                            configsHtml += '</div>';
                            $('#savedConfigs').html(configsHtml);
                            
                            // 点击保存的配置时填充表单
                            $('.saved-config').on('click', function(e) {
                                e.preventDefault();
                                
                                const $this = $(this);
                                const ip = $this.data('ip');
                                const port = $this.data('port');
                                const account = $this.data('account');
                                const password = $this.data('password');
                                const database = $this.data('database') || '';
                                
                                // 填充表单
                                $('#serverIp').val(ip);
                                $('#serverPort').val(port);
                                $('#serverAccount').val(account);
                                $('#serverPassword').val(password);
                                
                                // 更新服务器数据
                                serverData.ip = String(ip);
                                serverData.port = String(port);
                                serverData.account = String(account);
                                serverData.password = String(password);
                                if (database) {
                                    serverData.database = String(database);
                                }
                                
                                // 高亮选中的配置
                                $('.saved-config').removeClass('active');
                                $this.addClass('active');
                                
                                // 自动测试连接
                                $('#connection-result').html(
                                    '<div class="alert alert-info">' + 
                                    '<i class="fa fa-spinner fa-spin"></i> 正在测试连接...' + 
                                    '</div>'
                                );
                                
                                // 确保发送的数据正确
                                const connectionData = {
                                    ip: String(ip),
                                    port: String(port),
                                    account: String(account),
                                    password: String(password)
                                };
                                
                                $.ajax({
                                    url: '/api/data_center/test_connection',
                                    type: 'POST',
                                    contentType: 'application/json',
                                    data: JSON.stringify(connectionData),
                                    dataType: 'json',
                                    success: function(response) {
                                        if (response.success) {
                                            $('#connection-result').html(
                                                '<div class="alert alert-success">' + 
                                                '<i class="fa fa-check-circle"></i> 连接成功' + 
                                                '</div>'
                                            );
                                            
                                            // 自动获取数据库列表
                                            $('#getDatabases').click();
                                        } else {
                                            $('#connection-result').html(
                                                '<div class="alert alert-danger">' + 
                                                '<i class="fa fa-times-circle"></i> ' + response.message + 
                                                '</div>'
                                            );
                                        }
                                    },
                                    error: function(xhr) {
                                        let errorMsg = '连接失败';
                                        try {
                                            const response = JSON.parse(xhr.responseText);
                                            errorMsg = response.message || errorMsg;
                                        } catch (e) {}
                                        
                                        $('#connection-result').html(
                                            '<div class="alert alert-danger">' + 
                                            '<i class="fa fa-times-circle"></i> ' + errorMsg + 
                                            '</div>'
                                        );
                                    }
                                });
                            });
                        } else {
                            $('#savedConfigs').html('<p class="text-muted">暂无保存的数据库配置</p>');
                        }
                    },
                    error: function(xhr) {
                        let errorMsg = '获取配置失败';
                        try {
                            const response = JSON.parse(xhr.responseText);
                            errorMsg = response.message || errorMsg;
                        } catch (e) {}
                        
                        $('#savedConfigs').html('<p class="text-danger">' + errorMsg + '</p>');
                    }
                });
            }
            
            // 更新数据库名称
            function updateDatabaseName(serverData) {
                if (!serverData.ip || !serverData.port || !serverData.account || !serverData.database) {
                    return;
                }
                
                $.ajax({
                    url: '/api/data_center/update_database_name',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(serverData),
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // 静默成功，不显示消息
                        }
                    },
                    error: function() {
                        // 静默失败，不显示错误消息
                    }
                });
            }
            
            $('#copyTableInfo').on('click', copyTableInfo);
        });
    </script>
    
    <!-- 版本信息页脚 -->
    <footer class="mt-5 text-center text-muted">
        <p><small>2025 数据管理系统 | 版本 <span id="appVersion">v4</span>
            <a href="javascript:void(0)" class="btn btn-link p-0 ms-2" id="changelogBtn" style="font-size: 1em;vertical-align: baseline;">更新日志</a>
        </small></p>
    </footer>

    <!-- 版本更新日志模态框 -->
    {% include 'changelog_modal.html' %}

    <!-- 版本信息脚本 -->
    <script>
      $(function(){
        // 获取并更新版本号
        fetch('/api/version')
          .then(response => response.json())
          .then(data => {
            $('#appVersion').text(data.version);
          })
          .catch(error => {
            console.error('获取版本信息失败:', error);
          });
        
        // 更新日志按钮点击事件
        $('#changelogBtn').on('click', function(){
          var modal = new bootstrap.Modal(document.getElementById('changelogModal'));
          modal.show();
        });
      });
    </script>
</body>
</html>
