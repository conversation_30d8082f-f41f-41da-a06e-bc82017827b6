# src/core_routes/routes.py
from flask import Blueprint, render_template, redirect, url_for, current_app

core_bp = Blueprint(
    'core_bp',
    __name__,
    template_folder='../../templates',  # Path relative to this blueprint file's directory
    static_folder='../../static'
)

@core_bp.route('/')
def index():
    """重定向到卡片式导航页面"""
    return redirect(url_for('.dashboard')) # Use relative endpoint for blueprint

@core_bp.route('/dashboard')
def dashboard():
    """卡片式导航页面"""
    return render_template('dashboard.html')

# Potential conflicting route, assuming it's defined something like this:
# @app.route('/metrics_stats') # Or actual decorator used
# def metrics_stats():
#     # This simple render_template call is likely causing the Undefined error 
#     # because it doesn't pass the necessary context variables like 'indicators'.
#     return render_template('metrics_stats.html')

@core_bp.route('/data_center_page') # Renamed to avoid conflict if another /data_center exists
def data_center_page_route():
    """数据中台页面 (core blueprint version)"""
    # Accessing config values from current_app
    return render_template('data_center.html',
                         version=current_app.config.get('CURRENT_VERSION'),
                         show_version_notification=current_app.config.get('SHOW_VERSION_NOTIFICATION'),
                         version_release_notes=current_app.config.get('VERSION_RELEASE_NOTES'))

# We will move routes here in the next steps. 