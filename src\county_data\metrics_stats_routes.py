from flask import Blueprint, render_template, jsonify, request
from sqlalchemy import text
from src.utils.database_helpers import get_db_connection
from datetime import datetime, timedelta, timezone
from src.county_data.table_connection_rate import DBConnectionManager
from src.utils.data_calculation import (
    calculate_county_per_capita_total,
    calculate_county_per_capita_yearly,
    get_county_per_capita_ranking,
    get_county_population,
    get_data_tables,
    get_county_code_from_table
)

# Import new config
from .metrics_config import INDICATORS_CONFIG, REGIONS_CONFIG, PERIOD_OPTIONS

# Import the new calculator function
from .metrics_calculator import (
    calculate_metric_value, 
    get_city_total_population,
    calculate_city_connection_rate_data,
    get_display_page_city_total_records,
    get_display_page_city_yearly_increment
)

import logging # Import logging

logger = logging.getLogger(__name__) # Initialize logger for this module

# 创建蓝图
metrics_stats_bp = Blueprint('metrics_stats_bp', __name__)

def get_monday_of_week(date_obj, hour=11, minute=0, second=0, microsecond=0):
    """Helper to get Monday 11:00 AM of the week for a given date."""
    days_since_monday = date_obj.weekday() # Monday is 0, Sunday is 6
    monday = date_obj - timedelta(days=days_since_monday)
    return monday.replace(hour=hour, minute=minute, second=second, microsecond=microsecond)

def calculate_cutoff_date(period_option_id: str) -> datetime:
    """
    Calculates the cutoff_date based on the selected period option.
    """
    utc_now = datetime.now(timezone.utc) # Get current time in UTC
    beijing_tz = timezone(timedelta(hours=8)) # Define Beijing timezone (UTC+8)
    now = utc_now.astimezone(beijing_tz) # Convert UTC now to Beijing time

    selected_option = next((opt for opt in PERIOD_OPTIONS if opt['id'] == period_option_id), None)

    if not selected_option:
        return now # Default to current time if option not found

    offset_weeks = selected_option['offset_weeks']

    if offset_weeks is None: # 'current'
        return now
    
    # For '上一周', '前两周' etc. (offset_weeks = 0, -1, -2...)
    # offset_weeks = 0 means this Monday 11 AM
    # offset_weeks = -1 means last Monday 11 AM
    # The cutoff is the Monday 11 AM of the start of that week.
    # If today is Mon 10 AM, and we ask for '上一周' (offset 0), it should be last Mon 11 AM.
    # If today is Mon 12 PM, and we ask for '上一周' (offset 0), it should be this Mon 11 AM.
    
    current_week_monday_11am = get_monday_of_week(now)

    # Adjust if 'now' is before this Monday's 11 AM for offset 0
    if offset_weeks == 0 and now < current_week_monday_11am:
        target_monday = current_week_monday_11am - timedelta(weeks=1)
    else:
        target_monday = current_week_monday_11am + timedelta(weeks=offset_weeks)
        
    return target_monday


# --- Updated metrics_stats_page ---
@metrics_stats_bp.route('/metrics_stats', methods=['GET'])
def metrics_stats_page():
    """指标统计页面 - 重构版 (带上一期比较和动态权重)"""
    try:
        selected_period_id = request.args.get('period_id', PERIOD_OPTIONS[0]['id'])
        current_cutoff_date = calculate_cutoff_date(selected_period_id)
        
        previous_period_id = None
        previous_period_display_name = None
        previous_cutoff_date = None
        comparison_text = ""
        comparison_highlights = []  # 新增: 用于存储指标变化的摘要

        # 1. Determine Previous Period details
        prev_id, prev_name, _ = get_previous_period_details(selected_period_id, PERIOD_OPTIONS)
        if prev_id:
            previous_period_id = prev_id
            previous_period_display_name = prev_name
            previous_cutoff_date = calculate_cutoff_date(previous_period_id)

        current_period_display = next((opt['name'] for opt in PERIOD_OPTIONS if opt['id'] == selected_period_id), "未知周期")
        current_cutoff_date_display = current_cutoff_date.strftime('%Y-%m-%d %H:%M:%S')

        if previous_period_id and previous_cutoff_date:
            prev_cutoff_display = previous_cutoff_date.strftime('%Y-%m-%d %H:%M:%S')
            comparison_text = f"当前数据为 {current_period_display} ({current_cutoff_date_display})，与上期 {previous_period_display_name} ({prev_cutoff_display}) 数据比较。"
        else:
            comparison_text = f"当前数据周期为 {current_period_display} ({current_cutoff_date_display})。无上一期数据进行比较。"


        display_indicators = []
        main_db_engine = None
        catalog_db_engine = None

        # Get DB engines
        if any(ind['data_source_type'] in ['per_capita_data', 'yearly_increment_data', 'resource_usage_data'] for ind in INDICATORS_CONFIG) or \
           any(ind['data_source_type'] in ['per_capita_data', 'yearly_increment_data'] for ind in INDICATORS_CONFIG): # For city wide calcs
            main_db_engine = get_db_connection()
        if any(ind['data_source_type'] == 'connection_rate_data' for ind in INDICATORS_CONFIG):
            catalog_db_engine = DBConnectionManager.get_catalog_db_connection()

        # Fetch dynamic weights for the CURRENT period
        current_dynamic_weights = {}
        if main_db_engine: # Assuming weights table is in main_db
            current_dynamic_weights = get_dynamic_weights(selected_period_id, main_db_engine)
        
        # Fetch dynamic weights for the PREVIOUS period (if applicable)
        previous_dynamic_weights = {}
        if previous_period_id and main_db_engine:
            previous_dynamic_weights = get_dynamic_weights(previous_period_id, main_db_engine)

        # --- Calculate City-Wide base values for CURRENT period --- (Population first)
        city_total_pop = 0 # Assuming population is relatively static for the comparison window
        if main_db_engine:
            city_total_pop = get_city_total_population(main_db_engine)
        
        # --- MODIFIED: Calculate City-Wide base values for CURRENT period using NEW functions ---
        current_city_total_records_val_from_display_source = 0
        current_city_yearly_increment_val_from_display_source = 0
        if main_db_engine:
            current_city_total_records_val_from_display_source = get_display_page_city_total_records(current_cutoff_date, main_db_engine, REGIONS_CONFIG)
            current_city_yearly_increment_val_from_display_source = get_display_page_city_yearly_increment(current_cutoff_date, main_db_engine, REGIONS_CONFIG)

        # --- MODIFIED: Calculate City-Wide base values for PREVIOUS period (if applicable) using NEW functions ---
        previous_city_total_records_val_from_display_source = 0
        previous_city_yearly_increment_val_from_display_source = 0
        if previous_cutoff_date and main_db_engine:
            previous_city_total_records_val_from_display_source = get_display_page_city_total_records(previous_cutoff_date, main_db_engine, REGIONS_CONFIG)
            previous_city_yearly_increment_val_from_display_source = get_display_page_city_yearly_increment(previous_cutoff_date, main_db_engine, REGIONS_CONFIG)

        # --- Calculate all regional current values for CURRENT period ---
        all_regional_current_values = {}
        for indicator in INDICATORS_CONFIG:
            indicator_id = indicator['id']
            all_regional_current_values[indicator_id] = {}
            for region in REGIONS_CONFIG:
                region_id = region['id']
                current_value = calculate_metric_value(
                    indicator_config=indicator, region_config=region, cutoff_date=current_cutoff_date,
                    main_db_engine=main_db_engine, catalog_db_engine=catalog_db_engine
                )
                all_regional_current_values[indicator_id][region_id] = current_value if current_value is not None else 0.0
        
        # --- Calculate all regional current values for PREVIOUS period (if applicable) ---
        all_regional_previous_values = {}
        if previous_cutoff_date:
            for indicator in INDICATORS_CONFIG:
                indicator_id = indicator['id']
                all_regional_previous_values[indicator_id] = {}
                for region in REGIONS_CONFIG:
                    region_id = region['id']
                    previous_value = calculate_metric_value(
                        indicator_config=indicator, region_config=region, cutoff_date=previous_cutoff_date,
                        main_db_engine=main_db_engine, catalog_db_engine=catalog_db_engine
                    )
                    all_regional_previous_values[indicator_id][region_id] = previous_value if previous_value is not None else 0.0

            # 生成指标变化摘要 (如果存在上一期数据)
            comparison_highlights = generate_comparison_highlights(
                INDICATORS_CONFIG, 
                REGIONS_CONFIG, 
                all_regional_current_values, 
                all_regional_previous_values,
                city_total_pop,
                current_city_total_records_val_from_display_source,
                previous_city_total_records_val_from_display_source,
                current_city_yearly_increment_val_from_display_source,
                previous_city_yearly_increment_val_from_display_source,
                current_cutoff_date,
                previous_cutoff_date,
                catalog_db_engine,
                current_dynamic_weights, 
                previous_dynamic_weights 
            )

        year_from_cutoff = current_cutoff_date.year # Base year on current selection

        for indicator in INDICATORS_CONFIG:
            display_level2_name = indicator["level2_name"]
            if "{year}" in display_level2_name:
                display_level2_name = display_level2_name.format(year=year_from_cutoff)

            data_source_type = indicator.get("data_source_type")
            
            # --- Determine CURRENT city_wide_value ---
            current_city_wide_display_value = "-"
            current_city_wide_raw_value = None
            previous_city_wide_raw_value = None # 新增: 用于存储上一期全市原始值
            city_wide_change_indicator = "no_prev" # 新增: 全市指标变化指示

            if data_source_type == "per_capita_data":
                city_total_population = get_city_total_population(main_db_engine)
                city_total_records = get_display_page_city_total_records(current_cutoff_date, main_db_engine, REGIONS_CONFIG)
                val = round(city_total_records / city_total_population, 2) if city_total_population > 0 else 0.0
                current_city_wide_display_value = f"{val:.2f}"
                current_city_wide_raw_value = val
                if previous_cutoff_date:
                    prev_city_total_records = get_display_page_city_total_records(previous_cutoff_date, main_db_engine, REGIONS_CONFIG)
                    prev_val = round(prev_city_total_records / city_total_population, 2) if city_total_population > 0 else 0.0
                    previous_city_wide_raw_value = prev_val
            elif data_source_type == "yearly_increment_data":
                city_total_population = get_city_total_population(main_db_engine)
                city_yearly_increment = get_display_page_city_yearly_increment(current_cutoff_date, main_db_engine, REGIONS_CONFIG)
                val = round(city_yearly_increment / city_total_population, 2) if city_total_population > 0 else 0.0
                current_city_wide_display_value = f"{val:.2f}"
                current_city_wide_raw_value = val
                if previous_cutoff_date:
                    prev_city_yearly_increment = get_display_page_city_yearly_increment(previous_cutoff_date, main_db_engine, REGIONS_CONFIG)
                    prev_val = round(prev_city_yearly_increment / city_total_population, 2) if city_total_population > 0 else 0.0
                    previous_city_wide_raw_value = prev_val
            elif data_source_type == "connection_rate_data":
                if catalog_db_engine:
                    city_conn_rate = calculate_city_connection_rate_data(current_cutoff_date, catalog_db_engine)
                    if city_conn_rate is not None:
                        current_city_wide_display_value = f"{city_conn_rate:.2f}%"
                        current_city_wide_raw_value = city_conn_rate
                    if previous_cutoff_date:
                        prev_city_conn_rate = calculate_city_connection_rate_data(previous_cutoff_date, catalog_db_engine)
                        if prev_city_conn_rate is not None:
                            previous_city_wide_raw_value = prev_city_conn_rate
                            
            elif data_source_type == "resource_usage_data":
                raw_value_config = indicator.get("city_wide_value", "-") 
                try: 
                    if raw_value_config != "-":
                        if isinstance(raw_value_config, str) and "%" in raw_value_config:
                            current_city_wide_raw_value = float(raw_value_config.rstrip('%'))
                            current_city_wide_display_value = f"{current_city_wide_raw_value:.2f}%"
                        else:
                            current_city_wide_raw_value = float(raw_value_config)
                            current_city_wide_display_value = f"{current_city_wide_raw_value:.2f}"
                        # For resource_usage_data, assume previous is same as current from config if no other source
                        if previous_cutoff_date:
                             previous_city_wide_raw_value = current_city_wide_raw_value 
                    else:
                        current_city_wide_display_value = "-"
                        current_city_wide_raw_value = None
                        if previous_cutoff_date: previous_city_wide_raw_value = None
                except ValueError: 
                    current_city_wide_display_value = "-"
                    current_city_wide_raw_value = None
                    if previous_cutoff_date: previous_city_wide_raw_value = None

            # Determine city-wide change indicator
            if previous_cutoff_date and current_city_wide_raw_value is not None and previous_city_wide_raw_value is not None:
                # Add a small tolerance for float comparisons
                if abs(current_city_wide_raw_value - previous_city_wide_raw_value) < 0.001:
                    city_wide_change_indicator = "none"
                elif current_city_wide_raw_value > previous_city_wide_raw_value:
                    city_wide_change_indicator = "up"
                else:
                    city_wide_change_indicator = "down"
            elif previous_cutoff_date and (current_city_wide_raw_value is None or previous_city_wide_raw_value is None):
                 city_wide_change_indicator = "no_prev" # Or handle as "none" if one value is missing but other exists

            actual_weight_current = float(current_dynamic_weights.get(indicator["id"], indicator.get("weight", 0.0)))

            indicator_display_data = {
                "id": indicator["id"],
                "level1_name": indicator["level1_name"],
                "level2_name": display_level2_name,
                "weight": actual_weight_current,
                "provincial_best_value": indicator.get("provincial_best_value"),
                "city_wide_value": current_city_wide_display_value,
                "city_wide_change_indicator": city_wide_change_indicator, # 新增
                "unit": indicator.get("unit"),
                "regional_data": []
            }

            # Normalization logic for scoring (based on current values)
            current_values_for_indicator_raw = [
                all_regional_current_values[indicator['id']].get(r['id'])
                for r in REGIONS_CONFIG
            ]
            current_values_for_indicator_raw = [v for v in current_values_for_indicator_raw if v is not None]

            max_value_for_normalization = 0
            if current_values_for_indicator_raw:
                 max_value_for_normalization = max(current_values_for_indicator_raw) if current_values_for_indicator_raw else 0
            if max_value_for_normalization == 0: 
                max_value_for_normalization = 1


            for region in REGIONS_CONFIG:
                current_value_raw = all_regional_current_values[indicator['id']].get(region['id'])
                # 优化：县区人均相关指标直接用公共函数
                if data_source_type == "per_capita_data":
                    county_code = region.get('db_table_suffix')
                    if county_code:
                        current_value_raw = calculate_county_per_capita_total(county_code, year_from_cutoff)
                elif data_source_type == "yearly_increment_data":
                    county_code = region.get('db_table_suffix')
                    if county_code:
                        current_value_raw = calculate_county_per_capita_yearly(county_code, year_from_cutoff)
                # 修复: 获取指标原始值，确保有数值类型
                if current_value_raw is None:
                    current_value_raw = 0.0
                
                # 获取上一期原始值并确定变化指示
                previous_value_raw = None
                change_indicator = "no_prev" # 默认无前期数据

                if previous_cutoff_date and indicator['id'] in all_regional_previous_values and region['id'] in all_regional_previous_values[indicator['id']]:
                    previous_value_raw = all_regional_previous_values[indicator['id']].get(region['id'])
                    if previous_value_raw is None: # If explicitly None in previous data, treat as 0 for comparison
                        previous_value_raw = 0.0

                    if current_value_raw is not None and previous_value_raw is not None:
                         # Add a small tolerance for float comparisons
                        if abs(current_value_raw - previous_value_raw) < 0.001 :
                            change_indicator = "none"
                        elif current_value_raw > previous_value_raw:
                            change_indicator = "up"
                        else:
                            change_indicator = "down"
                elif previous_cutoff_date : # Previous period exists, but no data for this specific metric/region
                     change_indicator = "no_prev"

                # 得分计算
                score = 0.0
                # Score calculation based on current_value_raw
                if current_value_raw is not None:
                    weight = float(indicator.get("weight", 0))
                    if data_source_type in ["connection_rate_data", "resource_usage_data"]:
                        score = (float(current_value_raw) / 100.0) * weight
                    elif data_source_type in ["per_capita_data", "yearly_increment_data"]:
                        score = (float(current_value_raw) / max_value_for_normalization) * weight
                    else: score = 0.0
                
                # 确保正确格式化显示值
                current_display_str = ""
                if current_value_raw is not None:
                    unit_str = indicator.get('unit','')
                    if unit_str == '%':
                        current_display_str = f"{float(current_value_raw):.2f}%"
                    else:
                        current_display_str = f"{float(current_value_raw):.2f}"

                indicator_display_data["regional_data"].append({
                    "region_id": region["id"],
                    "region_name": region["name"],
                    "english_name": region["english_name"],
                    "current_value_display": current_display_str,
                    "current_value_raw": current_value_raw,
                    "score": round(score, 2),
                    "change_indicator": change_indicator # 新增
                })
            display_indicators.append(indicator_display_data)

        return render_template('metrics_stats.html',
                               indicators=display_indicators, 
                               regions_for_header=REGIONS_CONFIG, 
                               periods=PERIOD_OPTIONS, 
                               current_period_id=selected_period_id,
                               current_period_display_name=current_period_display,
                               current_cutoff_date_display=current_cutoff_date_display,
                               # 为比较提供的上下文
                               comparison_text=comparison_text,
                               comparison_highlights=comparison_highlights,
                               error=None)
    except Exception as e:
        import traceback
        traceback.print_exc()
        default_period_id = PERIOD_OPTIONS[0]['id'] if PERIOD_OPTIONS else None
        err_selected_period_id = selected_period_id if 'selected_period_id' in locals() else default_period_id
        err_current_period_display = current_period_display if 'current_period_display' in locals() else "加载错误"
        err_current_cutoff_display = current_cutoff_date_display if 'current_cutoff_date_display' in locals() else None

        return render_template('metrics_stats.html', 
                               error=str(e), 
                               indicators=[], 
                               regions_for_header=REGIONS_CONFIG, 
                               periods=PERIOD_OPTIONS, 
                               current_period_id=err_selected_period_id,
                               current_period_display_name=err_current_period_display,
                               current_cutoff_date_display=err_current_cutoff_display,
                               comparison_text="数据加载错误，无法生成比较信息。",
                               comparison_highlights=[],
                              )

# 新增辅助函数生成指标变化摘要
def generate_comparison_highlights(
    indicators_config, 
    regions_config, 
    current_values, 
    previous_values,
    city_pop,
    current_city_total_records_from_display_source,
    previous_city_total_records_from_display_source,
    current_city_yearly_increment_from_display_source,
    previous_city_yearly_increment_from_display_source,
    current_cutoff_date,
    previous_cutoff_date,
    catalog_db_engine,
    current_weights_map, 
    previous_weights_map
):
    """生成当前指标和上一期指标的变化摘要，按二级指标分组展示"""
    # 使用字典按指标分组存储变化
    indicator_changes = {}
    
    # 设置显著变化的阈值 (可以根据需要调整)
    SIGNIFICANT_RELATIVE_CHANGE_THRESHOLD = 0.05  # 5%
    SIGNIFICANT_ABSOLUTE_CHANGE_THRESHOLD = 0.5   # 0.5单位
    
    # 处理所有指标（包括全市数据和区县数据）
    for indicator in indicators_config:
        indicator_id = indicator.get("id")
        level2_name = indicator.get("level2_name")
        if "{year}" in level2_name:
            level2_name = level2_name.format(year=current_cutoff_date.year)
        unit = indicator.get("unit", "")
        data_source_type = indicator.get("data_source_type")
        
        # 为每个指标创建列表存储变化
        if level2_name not in indicator_changes:
            indicator_changes[level2_name] = []
        
        # 1. 处理全市数据
        current_city_value = None
        previous_city_value = None
        
        if data_source_type == "per_capita_data":
            if city_pop > 0:
                current_city_value = round(current_city_total_records_from_display_source / city_pop, 2)
                if previous_city_total_records_from_display_source is not None:
                    previous_city_value = round(previous_city_total_records_from_display_source / city_pop, 2)
        elif data_source_type == "yearly_increment_data":
            if city_pop > 0:
                current_city_value = round(current_city_yearly_increment_from_display_source / city_pop, 2)
                if previous_city_yearly_increment_from_display_source is not None:
                    previous_city_value = round(previous_city_yearly_increment_from_display_source / city_pop, 2)
        elif data_source_type == "connection_rate_data":
            if catalog_db_engine:
                current_city_value = calculate_city_connection_rate_data(current_cutoff_date, catalog_db_engine)
                if previous_cutoff_date and catalog_db_engine: # Check prev_cutoff and engine
                    previous_city_value = calculate_city_connection_rate_data(previous_cutoff_date, catalog_db_engine)
        elif data_source_type == "resource_usage_data":
            city_wide_value = indicator.get("city_wide_value", "-")
            if city_wide_value != "-":
                try:
                    current_city_value = float(str(city_wide_value).rstrip('%'))
                    previous_city_value = current_city_value
                except ValueError:
                    pass
        
        # 检查全市数据变化
        if current_city_value is not None and previous_city_value is not None:
            absolute_change = current_city_value - previous_city_value
            relative_change = absolute_change / previous_city_value if previous_city_value != 0 else float('inf')
            
            # 判断变化是否显著且值不为0
            significant_change = False
            if abs(relative_change) >= SIGNIFICANT_RELATIVE_CHANGE_THRESHOLD:
                significant_change = True
            elif abs(absolute_change) >= SIGNIFICANT_ABSOLUTE_CHANGE_THRESHOLD:
                significant_change = True
            
            if significant_change and abs(absolute_change) > 0.001:
                if unit == '%':
                    current_display = f"{current_city_value:.2f}%"
                    previous_display = f"{previous_city_value:.2f}%"
                else:
                    current_display = f"{current_city_value:.2f}"
                    previous_display = f"{previous_city_value:.2f}"
                
                change_text = f"全市: 从 {previous_display} 变为 {current_display}"
                
                if absolute_change > 0:
                    if unit == '%':
                        change_text += f"，增加了 {abs(absolute_change):.2f} 个百分点"
                    else:
                        change_text += f"，增加了 {abs(absolute_change):.2f}"
                else:
                    if unit == '%':
                        change_text += f"，减少了 {abs(absolute_change):.2f} 个百分点"
                    else:
                        change_text += f"，减少了 {abs(absolute_change):.2f}"
                
                indicator_changes[level2_name].append(change_text)
        
        # 2. 处理各区县数据
        for region in regions_config:
            region_id = region.get("id")
            region_name = region.get("name")
            
            current_value = current_values.get(indicator_id, {}).get(region_id)
            previous_value = previous_values.get(indicator_id, {}).get(region_id)
            
            if current_value is not None and previous_value is not None:
                absolute_change = current_value - previous_value
                relative_change = absolute_change / previous_value if previous_value != 0 else float('inf')
                
                # 判断变化是否显著且值不为0
                significant_change = False
                if abs(relative_change) >= SIGNIFICANT_RELATIVE_CHANGE_THRESHOLD:
                    significant_change = True
                elif abs(absolute_change) >= SIGNIFICANT_ABSOLUTE_CHANGE_THRESHOLD:
                    significant_change = True
                
                if significant_change and abs(absolute_change) > 0.001:
                    if unit == '%':
                        current_display = f"{current_value:.2f}%"
                        previous_display = f"{previous_value:.2f}%"
                    else:
                        current_display = f"{current_value:.2f}"
                        previous_display = f"{previous_value:.2f}"
                    
                    change_text = f"{region_name}: 从 {previous_display} 变为 {current_display}"
                    
                    if absolute_change > 0:
                        if unit == '%':
                            change_text += f"，增加了 {abs(absolute_change):.2f} 个百分点"
                        else:
                            change_text += f"，增加了 {abs(absolute_change):.2f}"
                    else:
                        if unit == '%':
                            change_text += f"，减少了 {abs(absolute_change):.2f} 个百分点"
                        else:
                            change_text += f"，减少了 {abs(absolute_change):.2f}"
                    
                    indicator_changes[level2_name].append(change_text)
    
    # 转换为最终格式的摘要列表
    highlights = []
    
    # 只保留有变化的指标
    indicator_changes = {k: v for k, v in indicator_changes.items() if v}
    
    # 限制显示的指标数量，如果太多
    MAX_INDICATORS = 5
    if len(indicator_changes) > MAX_INDICATORS:
        sorted_indicators = sorted(indicator_changes.items(), 
                                 key=lambda x: len(x[1]), 
                                 reverse=True)[:MAX_INDICATORS]
        indicator_changes = dict(sorted_indicators)
        highlights.append(f"（仅显示变化最多的 {MAX_INDICATORS} 个指标...）")
    
    # 将分组的变化转换为最终格式
    for indicator_name, changes in indicator_changes.items():
        if not changes:  # 跳过没有变化的指标
            continue
            
        # 添加指标标题
        highlights.append(f"<strong>{indicator_name}</strong>:")
        
        # 限制每个指标下显示的区县数量
        MAX_REGIONS_PER_INDICATOR = 6
        if len(changes) > MAX_REGIONS_PER_INDICATOR:
            changes = changes[:MAX_REGIONS_PER_INDICATOR]
            changes.append(f"（更多变化区县未显示...）")
        
        # 添加区县变化
        for change in changes:
            highlights.append(f"- {change}")
    
    return highlights

def get_previous_period_details(selected_period_id, period_options_list):
    """
    Determines the ID and name of the period immediately preceding the selected_period_id.
    """
    selected_opt_idx = -1
    selected_offset_weeks = None

    for i, opt in enumerate(period_options_list):
        if opt['id'] == selected_period_id:
            selected_opt_idx = i
            selected_offset_weeks = opt.get('offset_weeks')
            break
    
    if selected_opt_idx == -1:
        return None, None, None # Selected period not found

    # Logic based on offset_weeks:
    # 'current' (offset_weeks: None) -> prev is 'offset_weeks: 0'
    # 'offset_weeks: 0' -> prev is 'offset_weeks: -1'
    # 'offset_weeks: N' (N < 0) -> prev is 'offset_weeks: N-1'

    target_previous_offset = None
    if selected_offset_weeks is None: # Handle 'current'
        target_previous_offset = 0
    elif isinstance(selected_offset_weeks, int):
        target_previous_offset = selected_offset_weeks - 1
    else: # Should not happen if config is correct
        return None, None, None

    # Find the period that matches the target_previous_offset
    for opt in period_options_list:
        if opt.get('offset_weeks') == target_previous_offset:
            return opt['id'], opt['name'], opt.get('offset_weeks')
            
    return None, None, None # No previous period found with that offset

# --- Updated get_metrics_stats_data API ---
@metrics_stats_bp.route('/api/metrics_stats/data', methods=['GET'])
def get_metrics_stats_data():
    """获取指标统计数据的API - 重构版"""
    try:
        selected_period_id = request.args.get('period_id', PERIOD_OPTIONS[0]['id'])
        cutoff_date = calculate_cutoff_date(selected_period_id)
        api_indicators_data = []
        main_db_engine = None
        catalog_db_engine = None

        if any(ind['data_source_type'] in ['per_capita_data', 'yearly_increment_data', 'resource_usage_data'] for ind in INDICATORS_CONFIG) or \
           any(ind['data_source_type'] in ['per_capita_data', 'yearly_increment_data'] for ind in INDICATORS_CONFIG):
            main_db_engine = get_db_connection()
        if any(ind['data_source_type'] == 'connection_rate_data' for ind in INDICATORS_CONFIG):
            catalog_db_engine = DBConnectionManager.get_catalog_db_connection()

        # Fetch dynamic weights for the CURRENT period
        current_dynamic_weights = {}
        if main_db_engine:
            current_dynamic_weights = get_dynamic_weights(selected_period_id, main_db_engine)
        
        # Fetch dynamic weights for the PREVIOUS period (if applicable)
        previous_dynamic_weights = {}
        if previous_period_id and main_db_engine:
            previous_dynamic_weights = get_dynamic_weights(previous_period_id, main_db_engine)

        # --- Calculate City-Wide base values FIRST for API ---
        city_total_pop = 0
        # --- MODIFIED: Use NEW functions for city-wide totals in API ---
        city_total_records_val_api_source = 0
        city_yearly_increment_val_api_source = 0
        if main_db_engine:
            city_total_pop = get_city_total_population(main_db_engine)
            city_total_records_val_api_source = get_display_page_city_total_records(cutoff_date, main_db_engine, REGIONS_CONFIG)
            city_yearly_increment_val_api_source = get_display_page_city_yearly_increment(cutoff_date, main_db_engine, REGIONS_CONFIG)
        
        all_regional_current_values = {}
        for indicator in INDICATORS_CONFIG:
            indicator_id = indicator['id']
            all_regional_current_values[indicator_id] = {}
            for region in REGIONS_CONFIG:
                region_id = region['id']
                current_value = calculate_metric_value(
                    indicator_config=indicator,
                    region_config=region,
                    cutoff_date=cutoff_date,
                    main_db_engine=main_db_engine,
                    catalog_db_engine=catalog_db_engine
                )
                if current_value is None: current_value = 0.0
                all_regional_current_values[indicator_id][region_id] = current_value
        
        year_from_cutoff = cutoff_date.year

        for indicator_config in INDICATORS_CONFIG:
            display_level2_name = indicator_config["level2_name"]
            if "{year}" in display_level2_name:
                display_level2_name = display_level2_name.format(year=year_from_cutoff)

            # --- Determine city_wide_value for API ---
            city_wide_api_value = "-"
            data_source_type_api = indicator_config.get("data_source_type")

            if data_source_type_api == "per_capita_data":
                # 直接用公共函数
                val_api = get_city_per_capita_total(year_from_cutoff)
                city_wide_api_value = f"{val_api:.2f}"
            elif data_source_type_api == "yearly_increment_data":
                val_api = get_city_per_capita_yearly(year_from_cutoff)
                city_wide_api_value = f"{val_api:.2f}"
            elif data_source_type_api == "connection_rate_data":
                if catalog_db_engine:
                    city_conn_rate_api = calculate_city_connection_rate_data(cutoff_date, catalog_db_engine)
                    if city_conn_rate_api is not None:
                        city_wide_api_value = f"{city_conn_rate_api:.2f}%"
            elif data_source_type_api == "resource_usage_data":
                raw_value = indicator_config.get("city_wide_value", "-")
                try:
                    if raw_value != "-":
                        if isinstance(raw_value, str) and "%" in raw_value:
                            raw_value_float = float(raw_value.rstrip('%'))
                            city_wide_api_value = f"{raw_value_float:.2f}%"
                        else:
                            raw_value_float = float(raw_value)
                            city_wide_api_value = f"{raw_value_float:.2f}"
                except ValueError:
                    city_wide_api_value = "-"

            actual_weight_api = float(current_dynamic_weights.get(indicator_config["id"], indicator_config.get("weight", 0.0)))

            indicator_data_for_api = {
                "id": indicator_config["id"],
                "level1_name": indicator_config["level1_name"],
                "level2_name": display_level2_name,
                "weight": actual_weight_api,
                "provincial_best_value": indicator_config.get("provincial_best_value"),
                "city_wide_value": city_wide_api_value, # Use calculated or config value
                "unit": indicator_config.get("unit"),
                "regional_data": {}
            }
            current_values_for_indicator = [
                all_regional_current_values[indicator_config['id']][r['id']]
                for r in REGIONS_CONFIG if r['id'] in all_regional_current_values[indicator_config['id']]
            ]
            max_value_for_normalization = 0
            if current_values_for_indicator:
                max_value_for_normalization = max(v for v in current_values_for_indicator if v is not None) if any(v is not None for v in current_values_for_indicator) else 0
            if max_value_for_normalization == 0:
                max_value_for_normalization = 1

            for region_config_api in REGIONS_CONFIG:
                current_value_api = all_regional_current_values[indicator_config['id']].get(region_config_api['id'])
                score_api = 0.0
                if current_value_api is not None:
                    weight_api = float(indicator_config.get("weight", 0))
                    # data_source_type_api is already fetched
                    if data_source_type_api in ["connection_rate_data", "resource_usage_data"]:
                        score_api = (float(current_value_api) / 100.0) * weight_api
                    elif data_source_type_api in ["per_capita_data", "yearly_increment_data"]:
                        current_value_float_api = float(current_value_api) if current_value_api is not None else 0.0
                        score_api = (current_value_float_api / max_value_for_normalization) * weight_api
                    else:
                        score_api = 0.0
                
                display_value_str_api = ""
                if current_value_api is not None:
                    if indicator_config.get('unit') == '%':
                        display_value_str_api = f"{float(current_value_api):.2f}%"
                    else:
                        display_value_str_api = f"{float(current_value_api):.2f}"

                indicator_data_for_api["regional_data"][region_config_api["english_name"]] = {
                    "current_value": display_value_str_api, 
                    "score": round(score_api, 2)
                }
            api_indicators_data.append(indicator_data_for_api)
        
        current_period_info = next((opt for opt in PERIOD_OPTIONS if opt['id'] == selected_period_id), None)

        return jsonify({
            "success": True,
            "message": "数据获取成功",
            "data": {
                "indicators": api_indicators_data,
                "regions": REGIONS_CONFIG, 
                "periods": PERIOD_OPTIONS, 
                "current_period": { 
                    "id": current_period_info['id'] if current_period_info else selected_period_id,
                    "name": current_period_info['name'] if current_period_info else "未知",
                    "cutoff_date": cutoff_date.strftime('%Y-%m-%d %H:%M:%S')
                }
            }
        })
    except Exception as e:
        import traceback
        traceback.print_exc()
        return jsonify({"success": False, "message": str(e), "data": {}}), 500


def get_dynamic_weights(period_id: str, db_engine) -> dict:
    """
    Fetches dynamic weights for a given period_id from the database.
    Returns a dictionary mapping indicator_id to weight.
    """
    weights_map = {}
    if not db_engine:
        logger.warning(f"Database engine not provided for fetching dynamic weights for period_id: {period_id}")
        return weights_map
        
    query = text("""
        SELECT indicator_id, weight 
        FROM indicator_period_weights_metadata
        WHERE period_option_id = :period_id
    """)
    try:
        with db_engine.connect() as conn:
            result = conn.execute(query, {"period_id": period_id})
            for row in result:
                # Ensure indicator_id is int if it comes from DB as something else
                weights_map[int(row.indicator_id)] = float(row.weight)
        logger.info(f"Successfully fetched {len(weights_map)} dynamic weights for period_id: {period_id}")
    except Exception as e:
        logger.error(f"Error fetching dynamic weights for period_id {period_id}: {str(e)}")
    return weights_map