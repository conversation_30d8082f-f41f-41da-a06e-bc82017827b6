/**
 * Sidebar JavaScript 功能
 * 用于处理侧边栏的显示/隐藏和导航
 */

// 在文档加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    initSidebar();
});

// 初始化侧边栏功能
function initSidebar() {
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const contentWrapper = document.querySelector('.content-wrapper');
    
    // 侧边栏切换按钮点击事件
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('active');
            contentWrapper.classList.toggle('active');
        });
    }
    
    // 为每个侧边栏链接添加点击事件
    document.querySelectorAll('.sidebar .nav-link').forEach(link => {
        link.addEventListener('click', function(e) {
            // 移除所有链接的active类
            document.querySelectorAll('.sidebar .nav-link').forEach(l => {
                l.classList.remove('active');
            });
            
            // 为当前点击的链接添加active类
            this.classList.add('active');
            
            // 在移动设备上，点击导航链接后自动收起侧边栏
            if (window.innerWidth < 768) {
                sidebar.classList.remove('active');
                contentWrapper.classList.remove('active');
            }
        });
    });
} 