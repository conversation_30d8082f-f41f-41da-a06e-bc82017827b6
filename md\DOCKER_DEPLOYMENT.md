# Docker 部署脚本使用说明

## 概述

`docker_shell.sh` 脚本用于自动化将本地开发环境的应用打包为 Docker 镜像，并适配生产环境的配置。

## 功能特性

- ✅ 自动更新数据库配置（主机、端口、密码）
- ✅ 更新所有数据库连接URI
- ✅ 更新应用端口配置
- ✅ 更新 Dockerfile 配置
- ✅ 更新启动文件配置
- ✅ 自动备份原始文件
- ✅ 构建并保存 Docker 镜像
- ✅ 彩色日志输出和详细验证

## 配置说明

### 脚本配置变量

在 `docker_shell.sh` 中修改以下配置：

```bash
# 配置变量
VERSION=v4.9                    # Docker 镜像版本

# 数据库配置
OLD_HOST=**************         # 开发环境数据库主机
NEW_HOST=************           # 生产环境数据库主机
OLD_PORT_DB=3310                # 数据库端口（通常不变）
NEW_PORT_DB=3310
OLD_PASSWORD=123456             # 开发环境数据库密码
NEW_PASSWORD=Vp8bBVcKwKt8RRSJ   # 生产环境数据库密码

# 应用端口配置
OLD_PORT=5100                   # 开发环境应用端口
NEW_PORT=5000                   # 生产环境应用端口

# Ansible配置（如需要）
OLD_ANSIBLE_HOST=***********
NEW_ANSIBLE_HOST=***********
OLD_JUMP_HOST=************
NEW_JUMP_HOST=************
```

## 使用步骤

### 1. 准备工作

确保以下文件存在：
- `start.py` - 应用启动文件
- `app.py` - 主应用文件
- `Dockerfile` - Docker 配置文件
- `.env` - 环境配置文件

如果 `.env` 不存在，脚本会提示从 `.env.example` 复制。

### 2. 执行脚本

```bash
# 在 CentOS 7.9 服务器上执行
chmod +x docker_shell.sh
./docker_shell.sh
```

### 3. 脚本执行流程

1. **文件检查** - 验证必要文件存在且可写
2. **创建备份** - 备份所有将要修改的文件
3. **更新配置** - 按照配置变量更新各种配置
4. **验证更新** - 验证所有配置是否正确更新
5. **构建镜像** - 使用 Docker 构建镜像
6. **保存镜像** - 将镜像保存为 tar 文件
7. **显示摘要** - 显示更新摘要和部署说明

## 更新的文件和配置

### .env 文件更新项

- `DB_HOST` - 数据库主机地址
- `DB_PASSWORD` - 数据库密码
- `MAIN_DB_URI` - 主数据库连接字符串
- `MYSQL_AUDIT_DB_URI` - MySQL 审计数据库连接字符串
- `ANSIBLE_DB_URI` - Ansible 数据库连接字符串
- `PORT` - 应用端口（新增）
- `ANSIBLE_HOST` - Ansible 主机地址
- `JUMP_HOST` - 跳板机地址

### start.py 文件更新项

- 硬编码的端口号（如 `port=5100` → `port=5000`）
- 访问地址显示（如 `localhost:5100` → `localhost:5000`）

### Dockerfile 更新项

- `EXPOSE` 指令端口号
- `CMD` 启动命令（从 `app.py` 更新为 `start.py`）

## 生成的文件

- `myapp_v4.9.tar` - Docker 镜像文件（可上传到生产服务器）

## 备份文件

脚本会自动创建以下备份文件：
- `.env.bak` - 原始环境配置
- `start.py.bak` - 原始启动文件
- `app.py.bak` - 原始应用文件
- `Dockerfile.bak` - 原始 Dockerfile

## 部署到生产服务器

### 1. 上传镜像文件

```bash
# 将生成的 tar 文件上传到生产服务器
scp myapp_v4.9.tar user@production-server:/path/to/upload/
```

### 2. 在生产服务器加载镜像

```bash
# 加载 Docker 镜像
docker load -i myapp_v4.9.tar

# 查看加载的镜像
docker images | grep myapp
```

### 3. 运行容器

```bash
# 运行容器（端口映射根据实际需要调整）
docker run -d -p 5000:5000 --name myapp myapp:v4.9

# 查看容器状态
docker ps

# 查看容器日志
docker logs myapp
```

## 测试脚本

提供了测试脚本来验证配置更新功能：

```bash
# 运行测试脚本（不执行 Docker 构建）
chmod +x test_docker_shell_final.sh
./test_docker_shell_final.sh
```

## 故障排除

### 常见问题

1. **权限问题**
   ```bash
   chmod +x docker_shell.sh
   ```

2. **Docker 未安装**
   ```bash
   # CentOS 7.9 安装 Docker
   yum install -y docker
   systemctl start docker
   systemctl enable docker
   ```

3. **文件不存在**
   - 确保在项目根目录执行脚本
   - 检查 `.env` 文件是否存在

4. **配置验证失败**
   - 检查配置变量是否正确
   - 查看备份文件对比差异

### 日志说明

脚本使用彩色日志输出：
- 🔵 **[INFO]** - 信息提示
- 🟢 **[SUCCESS]** - 操作成功
- 🟡 **[WARNING]** - 警告信息
- 🔴 **[ERROR]** - 错误信息

## 注意事项

1. **备份重要性** - 脚本会自动备份，但建议手动备份重要文件
2. **配置验证** - 执行前请仔细检查配置变量
3. **网络要求** - 构建镜像时需要网络连接下载依赖
4. **磁盘空间** - 确保有足够空间存储 Docker 镜像
5. **权限要求** - 需要 Docker 操作权限

## 版本历史

- v4.9 - 适配新的 start.py 启动方式，完善配置更新逻辑
- v4.8 - 初始版本，支持基本的配置替换和 Docker 构建
