"""
数据管理路由模块
整合了数据管理相关的所有路由功能
"""
from flask import Blueprint, render_template, request, jsonify, send_file, current_app
from werkzeug.utils import secure_filename
import os
from datetime import datetime
import pandas as pd
from io import BytesIO
from sqlalchemy import text
import logging
from threading import Thread
import uuid as uuid_lib
import time
from io import StringIO
import traceback

from .services.excel_processing_service import ExcelProcessingService
from .services.data_persistence_service import DataPersistenceService
from src.utils.database_helpers import (
    get_db_connection, get_all_tables, get_table_comments, get_providers, 
    get_table_stats, get_column_comments, get_table_display_name
)
from src.county_data.metrics_calculator import (
    calculate_per_capita_data,
    calculate_yearly_increment_data,
    get_population_for_region
)
from src.county_data.metrics_config import REGIONS_CONFIG

# 配置日志
logger = logging.getLogger(__name__)
handler = logging.StreamHandler()
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
handler.setFormatter(formatter)
logger.addHandler(handler)
logger.setLevel(logging.INFO)

# 创建蓝图
data_management_bp = Blueprint('data_management', __name__, url_prefix='/data')

# 初始化服务
excel_service = ExcelProcessingService()
data_service = DataPersistenceService(get_db_connection)

# --- 异步任务全局存储 ---
tasks = {}

@data_management_bp.route('/upload', methods=['POST'])
def upload_excel():
    """处理Excel文件上传"""
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': '没有文件被上传'})
    
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'})
    
    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({'success': False, 'message': '只支持Excel文件'})
    
    try:
        # 处理Excel文件
        success, result = excel_service.process_excel_file(file, 'excel_data')
        
        if success:
            # 创建表并插入数据
            table_name = f"excel_data_{os.path.splitext(file.filename)[0].lower()}"
            data_service.create_table_if_not_exists(table_name)
            data_service.clear_table(table_name)
            insert_success, message = data_service.insert_data(table_name, result)
            
            if insert_success:
                return jsonify({
                    'success': True,
                    'message': f'文件上传并处理成功，共导入 {len(result)} 条记录'
                })
            else:
                return jsonify({'success': False, 'message': f'数据导入失败: {message}'})
        else:
            return jsonify({'success': False, 'message': f'文件处理失败: {result}'})
            
    except Exception as e:
        return jsonify({'success': False, 'message': f'处理文件时发生错误: {str(e)}'})

@data_management_bp.route('/export', methods=['GET'])
def export_data():
    """导出数据为Excel文件"""
    try:
        table_name = request.args.get('table')
        if not table_name:
            return jsonify({'success': False, 'message': '未指定表名'})
        
        # 获取数据
        engine = get_db_connection()
        with engine.connect() as conn:
            result = conn.execute(f"SELECT * FROM {table_name}")
            data = [dict(row) for row in result]
        
        if not data:
            return jsonify({'success': False, 'message': '没有数据可导出'})
        
        # 创建DataFrame
        df = pd.DataFrame(data)
        
        # 创建Excel文件
        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='Sheet1')
        
        output.seek(0)
        
        # 生成文件名
        filename = f"{table_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'导出数据时发生错误: {str(e)}'})

@data_management_bp.route('/backup', methods=['POST'])
def backup_data():
    """备份数据库"""
    try:
        success, message = data_service.backup_database()
        if success:
            return jsonify({'success': True, 'message': f'数据库备份成功: {message}'})
        else:
            return jsonify({'success': False, 'message': f'数据库备份失败: {message}'})
    except Exception as e:
        return jsonify({'success': False, 'message': f'备份过程中发生错误: {str(e)}'})

@data_management_bp.route('/entry')
def view_data_entry():
    """数据入口页面 - 主数据显示页"""
    try:
        start_time = datetime.now()
        logger.info("访问数据入口页面")
        
        engine = get_db_connection()
        
        table = request.args.get('table', '')
        provider = request.args.get('provider', '')
        org_code = request.args.get('org_code', '')
        page = request.args.get('page', 1, type=int)
        per_page = 10
        
        # 记录请求参数
        logger.info(f"请求参数: table={table}, provider={provider}, org_code={org_code}, page={page}")
        
        # 获取所有表格
        tables = get_all_tables(engine)
        table_comments = get_table_comments(engine)
        
        # 处理表显示名称和县名映射
        table_display_names = {}
        county_names = {}
        for t in tables:
            comment = table_comments.get(t, '')
            display_name = get_table_display_name(t)
            table_display_names[t] = f"{display_name} - {comment}" if comment else display_name
            
            # 处理县域名称
            if t.startswith('excel_data_'):
                if comment and '_' in comment:
                    county_name = comment.split('_')[1] if len(comment.split('_')) > 1 else comment
                    county_names[t] = county_name
                else:
                    county_names[t] = t.replace('excel_data_', '').replace('_', ' ')
                # 特殊情况处理
                if t == 'excel_data_tss_sz':
                    county_names[t] = '市直部门'

        # 如果没有指定表格，使用第一个表格
        if not table and tables:
            table = tables[0]
            logger.info(f"未指定表格，默认使用: {table}")
        
        # 初始化变量
        data = []
        columns = []
        column_comments = {}
        stats = {}
        providers = []
        total_count = 0
        total_pages = 1

        if table:
            # 获取数据提供者和表统计信息
            providers = get_providers(table, engine)
            stats = get_table_stats(table, engine, provider, org_code)
            
            # 构建基础查询
            base_query = f"""
            SELECT t.id, t.chinese_name, t.table_name, t.record_count, t.provide_time,
                   t.is_pushed, t.is_imported, m.org_name as unit_name
            FROM `{table}` t LEFT JOIN test_org_code_metadata m ON t.org_code = m.org_code
            """
            
            # 构建条件子句
            params = {}
            where_clauses = []
            
            # 处理组织机构代码过滤
            if org_code:
                where_clauses.append("t.org_code = :org_code")
                params['org_code'] = org_code
            # 处理提供者名称过滤（只有当org_code为空时）
            elif provider and provider.lower() != '全部':  # 忽略"全部"选项的过滤
                where_clauses.append("(t.data_provider = :provider OR m.org_name = :provider)")
                params['provider'] = provider
            
            # 添加WHERE子句（如果有条件）
            query_with_where = base_query
            if where_clauses:
                 query_with_where += " WHERE " + " AND ".join(where_clauses)

            # 获取总记录数
            count_query = f"SELECT COUNT(*) FROM ({query_with_where}) as count_table"
            try:
                with engine.connect() as conn:
                    total_count_res = conn.execute(text(count_query), params).scalar()
                    total_count = total_count_res if total_count_res is not None else 0
                
                # 计算总页数
                total_pages = (total_count + per_page - 1) // per_page if total_count > 0 else 1
                page = max(1, min(page, total_pages if total_pages > 0 else 1))
                offset = (page - 1) * per_page
                
                # 最终查询（添加分页）
                final_query = f"{query_with_where} ORDER BY t.provide_time DESC LIMIT {per_page} OFFSET {offset}"
                
                # 执行查询获取数据
                with engine.connect() as conn:
                    logger.debug(f"执行SQL: {final_query}")
                    result = conn.execute(text(final_query), params)
                    columns = list(result.keys())
                    data = [list(row) for row in result.fetchall()]
                
                # 获取列注释信息
                column_comments = get_column_comments(table, engine)
                
                logger.info(f"查询完成: 找到 {total_count} 条记录，返回 {len(data)} 条")
            
            except Exception as e:
                logger.error(f"查询数据时出错: {str(e)}")
                # 不抛出异常，继续处理，返回空数据
        
        # 处理编辑权限
        can_edit = True
        add_record_flag = request.args.get('add_record') is not None
            
        # 版本信息（示例值）
        version_info = {
             'version': 'v4.1.3',
             'show_version_notification': False,
             'version_release_notes': ''
        }

        # 统计卡片数据（县区）
        county_stats = []
        cutoff_date = datetime.now()
        for region in REGIONS_CONFIG:
            region_suffix = region['db_table_suffix']
            region_config = region
            try:
                # 使用统一的统计函数
                per_capita = calculate_per_capita_data(region_config, cutoff_date, engine)
                yearly_increment = calculate_yearly_increment_data(region_config, cutoff_date, engine)
                population = get_population_for_region(region_suffix, engine)
                
                # 计算总条数（使用人均汇聚量和人口数）
                total_records = int(per_capita * population) if per_capita and population else 0
                
                county_stats.append({
                    'county_name': region['name'],
                    'total_records': total_records,
                    'per_capita': round(per_capita, 2) if per_capita else 0,
                    'yearly_increment': int(yearly_increment) if yearly_increment else 0,
                    'population': int(population) if population else 0
                })
            except Exception as e:
                logger.error(f"计算县区 {region['name']} 统计数据时出错: {str(e)}")
                # 添加错误数据，避免前端显示异常
                county_stats.append({
                    'county_name': region['name'],
                    'total_records': 0,
                    'per_capita': 0,
                    'yearly_increment': 0,
                    'population': 0
                })

        elapsed_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"数据入口页面加载完成，耗时: {elapsed_time:.2f}秒")

        return render_template('index.html', 
                            data=data,
                            columns=columns,
                            column_comments=column_comments,
                            tables=tables,
                            table_display_names=table_display_names,
                            county_names=county_names,
                            current_table=table,
                            table_stats=stats,
                            providers=providers,
                            current_provider=provider,
                            current_org_code=org_code,
                            can_edit=can_edit,
                            add_record=add_record_flag,
                            page=page,
                            total_pages=total_pages,
                            total_count=total_count,
                            version=version_info['version'],
                            show_version_notification=version_info['show_version_notification'],
                            version_release_notes=version_info['version_release_notes'],
                            county_stats=county_stats)
    except Exception as e:
        logger.error(f"处理数据入口页面时出错: {str(e)}")
        return render_template('error.html', error=f"发生错误: {str(e)}"), 500 

# --- /data_import/upload ---
@data_management_bp.route('/data_import/upload', methods=['POST'])
def data_import_upload():
    if 'file' not in request.files:
        return jsonify({'success': False, 'message': '没有文件被上传'})
    file = request.files['file']
    if file.filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'})
    if not file.filename.endswith(('.xlsx', '.xls')):
        return jsonify({'success': False, 'message': '只支持Excel文件'})
    try:
        file_bytes = file.read()
        task_id = str(uuid_lib.uuid4())
        tasks[task_id] = {
            'id': task_id,
            'file_name': file.filename,
            'status': 'pending',
            'logs': '',
            'start_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'result': None,
            'error': None
        }
        # 启动异步处理线程，传递 file_bytes
        thread = Thread(target=process_import_task, args=(task_id, file_bytes))
        thread.start()
        return jsonify({'success': True, 'task_id': task_id})
    except Exception as e:
        return jsonify({'success': False, 'message': f'上传文件时发生错误: {str(e)}\n{traceback.format_exc()}'})

# --- 任务处理函数 ---
def process_import_task(task_id, file_bytes):
    task = tasks[task_id]
    task['status'] = 'running'
    log_buffer = StringIO()
    start_time = time.time()
    try:
        excel_service = ExcelProcessingService()
        data_service = DataPersistenceService(get_db_connection)
        # 每次解析都新建 BytesIO
        success, result = excel_service.read_excel_with_openpyxl(BytesIO(file_bytes))
        if not success:
            task['status'] = 'failed'
            task['error'] = result
            task['logs'] += f"读取Excel失败: {result}\n"
            return
        sheets_data = result
        total_sheets = len(sheets_data)
        success_count = 0
        error_count = 0
        total_records = 0
        for idx, (sheet_name, data) in enumerate(sheets_data.items(), 1):
            log_buffer.write(f"\n--- [{idx}/{total_sheets}] 处理工作表: {sheet_name} ---\n")
            table_name = f"excel_data_{sheet_name.lower()}"
            
            # 增加创建表日志
            create_success, create_msg = data_service.create_table_if_not_exists(table_name)
            log_buffer.write(f"创建表 {table_name} : {'成功' if create_success else '失败'} - {create_msg}\n")

            # 增加清空表日志
            clear_success, clear_msg = data_service.clear_table(table_name)
            log_buffer.write(f"清空表 {table_name} : {'成功' if clear_success else '失败'} - {clear_msg}\n")

            if data:
                insert_success, msg = data_service.insert_data(table_name, data)
                if insert_success:
                    log_buffer.write(f"✓ 表 {table_name} 数据导入成功 ({len(data)} 条记录)\n")
                    success_count += 1
                    total_records += len(data)
                else:
                    log_buffer.write(f"✗ 表 {table_name} 数据导入失败: {msg}\n")
                    error_count += 1
            else:
                log_buffer.write(f"✗ 表 {table_name} 没有数据需要导入\n")
                error_count += 1

        duration = round(time.time() - start_time, 2)
        task['status'] = 'completed'
        task['logs'] += log_buffer.getvalue()
        task['result'] = {
            'total_sheets': total_sheets,
            'success_count': success_count,
            'error_count': error_count,
            'total_records': total_records,
            'duration': duration
        }
    except Exception as e:
        task['status'] = 'failed'
        task['error'] = str(e)
        task['logs'] += f"处理任务时发生错误: {str(e)}\n{traceback.format_exc()}\n"

# --- /data_import/status/<task_id> ---
@data_management_bp.route('/data_import/status/<task_id>')
def data_import_status(task_id):
    task = tasks.get(task_id)
    if not task:
        return jsonify({'success': False, 'message': '任务不存在'})
    return jsonify({'success': True, 'task': task})

# --- /data_import/logs/<task_id> ---
@data_management_bp.route('/data_import/logs/<task_id>')
def data_import_logs(task_id):
    task = tasks.get(task_id)
    if not task:
        return jsonify({'success': False, 'message': '任务不存在'})
    return jsonify({'success': True, 'logs': task['logs'], 'status': task['status']})

# --- /data_import/list ---
@data_management_bp.route('/data_import/list')
def data_import_list():
    # 返回所有任务，按时间倒序
    all_tasks = sorted(tasks.values(), key=lambda t: t['start_time'], reverse=True)
    return jsonify({'success': True, 'tasks': all_tasks})

@data_management_bp.route('/data_import')
def data_import_page():
    return render_template('upload.html') 