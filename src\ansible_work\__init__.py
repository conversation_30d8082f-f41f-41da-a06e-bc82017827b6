import os
from flask import Blueprint

# Get the absolute path to the directory containing this __init__.py file
blueprint_root_path = os.path.dirname(os.path.abspath(__file__))
# Construct the absolute path to the templates folder
template_folder_path = os.path.join(blueprint_root_path, 'templates')
# Construct the absolute path to the static folder
static_folder_path = os.path.join(blueprint_root_path, 'static')

# 创建蓝图实例
# url_prefix='/ansible' 让所有该模块的路由都以 /ansible 开头
# template_folder='templates' 指向蓝图自己的模板文件夹
# static_folder='static' 指向蓝图自己的静态文件夹
ansible_bp = Blueprint(
    'ansible_work',
    __name__,
    # Use absolute paths
    template_folder=template_folder_path,
    static_folder=static_folder_path,
    url_prefix='/ansible'
)

# 导入路由，这样蓝图创建时路由就被注册了
# 注意：我们接下来需要修改 app.py 文件以适应蓝图
from . import app as routes 