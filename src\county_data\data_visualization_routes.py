from flask import Blueprint, render_template, request, jsonify
from sqlalchemy import text
from datetime import datetime
import traceback

# 假设这些辅助函数能被正确导入
from src.utils.database_helpers import (
    get_db_connection, get_all_tables, get_table_comments, 
    get_providers, get_table_stats
)

visualization_bp = Blueprint('visualization', __name__)

@visualization_bp.route('/visualization')
def visualization():
    """数据可视化页面"""
    try:
        engine = get_db_connection()
        tables = get_all_tables(engine)
        county_data = {}
        city_unit_data = {}
        current_year = datetime.now().year
        table_comments = get_table_comments(engine)
        
        for table in tables:
            table_comment = table_comments.get(table, '')
            is_city_unit = '市直单位' in table_comment
            county_name = table_comment.split('_')[1] if table_comment and '_' in table_comment else table
            stats = get_table_stats(table, engine) # Assuming get_table_stats calculates yearly_increment correctly
            
            units_data = []
            try:
                with engine.connect() as conn:
                    units_query = text(f"""
                        SELECT data_provider, COUNT(*) as count
                        FROM `{table}`
                        GROUP BY data_provider
                        ORDER BY count DESC
                        LIMIT 10
                    """)
                    units_result = conn.execute(units_query).fetchall()
                    units_data = [{'name': row[0], 'count': row[1]} for row in units_result]
            except Exception as e:
                print(f"Error getting units data for {table}: {str(e)}")
            
            data_entry = {
                'total_records': stats.get('total_records', 0),
                'yearly_increment': stats.get('yearly_increment', 0), # This should be from get_table_stats
                'provider_count': stats.get('provider_count', 0),
                'units': units_data
            }
            if not is_city_unit:
                 data_entry['per_capita'] = stats.get('per_capita', 0)
                 data_entry['total_population'] = stats.get('total_population', 0)


            if is_city_unit:
                city_unit_data[county_name] = data_entry
            else:
                county_data[county_name] = data_entry
        
        total_data_sum = sum(county['total_records'] for county in county_data.values()) + sum(unit['total_records'] for unit in city_unit_data.values())
        total_increment_sum = sum(county['yearly_increment'] for county in county_data.values()) + sum(unit['yearly_increment'] for unit in city_unit_data.values())
        
        def format_large_number(num):
            if num >= 100000000:
                return f"{num / 100000000:.2f}亿"
            elif num >= 10000:
                return f"{num / 10000:.2f}万"
            else:
                return f"{num:,}"
        
        formatted_total_data_sum = format_large_number(total_data_sum)
        formatted_total_increment_sum = format_large_number(total_increment_sum)
        
        max_data_county = max(county_data.items(), key=lambda x: x[1]['total_records'])[0] if county_data else "无数据"
        max_increment_county = max(county_data.items(), key=lambda x: x[1]['yearly_increment'])[0] if county_data else "无数据"
        
        return render_template('visualization.html', 
                              county_data=county_data, 
                              city_unit_data=city_unit_data, 
                              total_data_sum=formatted_total_data_sum,
                              total_increment_sum=formatted_total_increment_sum,
                              county_count=len(county_data),
                              max_data_county=max_data_county,
                              max_increment_county=max_increment_county)
    
    except Exception as e:
        print(f"Error in visualization: {str(e)}")
        traceback.print_exc()
        return render_template('error.html', error=str(e))

@visualization_bp.route('/api/county_top_units/<county_name>')
def get_county_top_units(county_name):
    """获取指定区县的前10个单位数据"""
    try:
        data_type = request.args.get('data_type', 'total')
        is_increment_data = data_type == 'increment'
        
        # print(f"获取区县 {county_name} 的单位数据，数据类型: {data_type}") # Commented out
        
        engine = get_db_connection()
        tables = get_all_tables(engine)
        table_comments = get_table_comments(engine)
        
        county_tables = []
        for table in tables:
            table_comment = table_comments.get(table, '')
            comment_county = table_comment.split('_')[1] if table_comment and '_' in table_comment else ''
            is_city_unit = '市直单位' in table_comment
            
            if (is_city_unit and county_name == '市直单位') or \
               (comment_county and comment_county == county_name) or \
               (table == county_name): # Simplified condition
                county_tables.append(table)
        
        if not county_tables:
            # print(f"未找到区县: {county_name} 的相关表") # Commented out
            return jsonify({'error': f'未找到区县: {county_name}'}), 404
        
        # print(f"找到区县 {county_name} 的相关表: {county_tables}") # Commented out
        
        all_units_data = {}
        total_value_for_type = 0 # This will be total_count or yearly_increment based on data_type
        
        current_year = datetime.now().year
        year_start_str = f"{current_year}-01-01" # Renamed to avoid conflict
        
        with engine.connect() as conn:
            for table in county_tables:
                provider_column = 'data_provider'
                
                # Check for necessary columns once
                cols_info_query = text("""
                    SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
                    WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = :table_name
                    AND COLUMN_NAME IN ('record_count', 'provide_time') 
                """) # Assuming 'excel' schema is default or set in connection
                
                cols_present = {row[0] for row in conn.execute(cols_info_query, {"table_name": table})}
                has_record_count = 'record_count' in cols_present
                has_provide_time = 'provide_time' in cols_present

                # print(f"Table {table} structure: record_count: {has_record_count}, provide_time: {has_provide_time}") # Commented out

                query_parts = []
                params = {}

                if is_increment_data:
                    if has_provide_time:
                        value_expression = "SUM(CAST(REPLACE(REPLACE(record_count, ',', ''),' ','') AS DECIMAL(20,0)))" if has_record_count else "COUNT(*)"
                        query_parts.append(f"SELECT {provider_column}, {value_expression} as amount")
                        query_parts.append(f"FROM `{table}`")
                        query_parts.append(f"WHERE STR_TO_DATE(provide_time, '%Y-%m-%d %H:%i:%s') >= :year_start_str")
                        params['year_start_str'] = year_start_str
                    else:
                        # print(f"Table {table} has no provide_time column, cannot calculate increment accurately.") # Commented out
                        continue 
                else: # Total data
                    value_expression = "SUM(CAST(REPLACE(REPLACE(record_count, ',', ''),' ','') AS DECIMAL(20,0)))" if has_record_count else "COUNT(*)"
                    query_parts.append(f"SELECT {provider_column}, {value_expression} as amount")
                    query_parts.append(f"FROM `{table}`")

                if not query_parts: continue

                query_parts.append(f"GROUP BY {provider_column}")
                # query_parts.append(f"ORDER BY amount DESC") # Ordering done in Python later

                final_query = " ".join(query_parts)
                
                try:
                    result = conn.execute(text(final_query), params).fetchall()
                    # print(f"Table {table} query result for {data_type}: {len(result)} rows") # Commented out
                    for row in result:
                        unit_name = row[0]
                        unit_amount = float(row[1] or 0)
                        all_units_data[unit_name] = all_units_data.get(unit_name, 0) + unit_amount
                        total_value_for_type += unit_amount
                except Exception as e:
                    print(f"Querying table {table} for {data_type} data failed: {str(e)}")
        
        if is_increment_data and not all_units_data:
            return jsonify({
                'county': county_name, 'data_type': data_type, 'tables': county_tables,
                'total_count': 0, 'yearly_increment': 0, 'units': [],
                'message': '该区县没有年度新增数据记录'
            })

        top_units = [{'name': name, 'count': count} for name, count in sorted(all_units_data.items(), key=lambda x: x[1], reverse=True)[:10]]
        
        # For 'total' data_type, we also need total yearly_increment for the summary
        # This part is a bit tricky as 'get_table_stats' in visualization route calculates it per table
        # Here, we'd need to re-calculate or fetch it. For now, let's use the total_value_for_type.
        # If data_type is 'total', total_value_for_type is the total_count.
        # If data_type is 'increment', total_value_for_type is the yearly_increment.
        
        response_total_count = total_value_for_type if not is_increment_data else sum(u['count'] for u in top_units) # Approximation for total if only showing top 10 increments
        response_yearly_increment = total_value_for_type if is_increment_data else 0 # Needs a better way if data_type is total


        # To get a more accurate yearly_increment when data_type is 'total', we might need another pass
        # or rely on a helper that calculates this sum across tables if performance allows.
        # For simplicity, if data_type is 'total', we'll leave yearly_increment as the sum of increments of the top units,
        # or find a way to sum it across all units if needed for display.
        # The current `total_value_for_type` is the sum of the primary metric being queried.

        actual_yearly_increment = 0
        if not is_increment_data: # If we are fetching total, we need to calculate total increment separately for display
            with engine.connect() as conn:
                for table in county_tables:
                    cols_info_query = text("""
                        SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS
                        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = :table_name
                        AND COLUMN_NAME IN ('record_count', 'provide_time')
                    """)
                    cols_present = {row[0] for row in conn.execute(cols_info_query, {"table_name": table})}
                    has_record_count = 'record_count' in cols_present
                    has_provide_time = 'provide_time' in cols_present

                    if has_provide_time:
                        value_expression = "SUM(CAST(REPLACE(REPLACE(record_count, ',', ''),' ','') AS DECIMAL(20,0)))" if has_record_count else "COUNT(*)"
                        inc_query_str = f"""
                            SELECT {value_expression} FROM `{table}`
                            WHERE STR_TO_DATE(provide_time, '%Y-%m-%d %H:%i:%s') >= :year_start_str
                        """
                        inc_params = {'year_start_str': year_start_str}
                        try:
                            table_increment = conn.execute(text(inc_query_str), inc_params).scalar_one_or_none()
                            if table_increment:
                                actual_yearly_increment += float(table_increment)
                        except Exception as e:
                            print(f"Error calculating increment for table {table} in total mode: {str(e)}")
            response_yearly_increment = actual_yearly_increment


        return jsonify({
            'county': county_name,
            'data_type': data_type,
            'tables': county_tables, # Included for debugging or context
            'total_count': response_total_count,
            'yearly_increment': response_yearly_increment,
            'units': top_units
        })
        
    except Exception as e:
        print(f"获取区县 {county_name} 的单位数据时出错: {str(e)}")
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500 