<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据可视化展示</title>
    <!-- 引入Bootstrap CSS -->
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <!-- 引入Font Awesome -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }}">
    <!-- 引入ECharts -->
    <script src="{{ url_for('static', filename='js/echarts.min.js') }}"></script>
    <style>
        body {
            font-family: "Microsoft YaHei", sans-serif;
            background-color: #f8f9fa;
        }
        .page-title {
            color: #0d6efd;
            margin-top: 20px;
            margin-bottom: 30px;
            font-weight: bold;
            text-align: center;
        }
        .chart-container {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .chart-title {
            color: #0d6efd;
            font-weight: bold;
            margin-bottom: 20px;
            text-align: center;
        }
        .chart {
            height: 400px;
            width: 100%;
        }
        .data-summary {
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .summary-title {
            color: #0d6efd;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .summary-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .summary-item:last-child {
            border-bottom: none;
        }
        .summary-label {
            font-weight: bold;
        }
        .summary-value {
            color: #0d6efd;
            font-weight: bold;
        }
        .btn-back {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="page-title">
            <i class="fas fa-chart-bar me-2"></i>数据可视化展示
        </h1>
        
        <div class="d-flex justify-content-between mb-4">
            <a href="/" class="btn btn-primary btn-back">
                <i class="fas fa-arrow-left me-2"></i>返回主页
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary" id="totalDataBtn">总数据</button>
                <button type="button" class="btn btn-outline-primary" id="incrementDataBtn">新增数据</button>
            </div>
        </div>
        
        <div class="row">
            <!-- 总数据统计 -->
            <div class="col-md-4">
                <div class="data-summary">
                    <h3 class="summary-title"><i class="fas fa-info-circle me-2"></i>数据概览</h3>
                    <div class="summary-item">
                        <span class="summary-label">总数据量：</span>
                        <span class="summary-value">{{ total_data_sum }}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">年度新增：</span>
                        <span class="summary-value">{{ total_increment_sum }}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">区县数量：</span>
                        <span class="summary-value">{{ county_count }}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">最大数据区县：</span>
                        <span class="summary-value">{{ max_data_county }}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">最大新增区县：</span>
                        <span class="summary-value">{{ max_increment_county }}</span>
                    </div>
                </div>
            </div>
            
            <!-- 柱形图 -->
            <div class="col-md-8">
                <div class="chart-container">
                    <h3 class="chart-title"><i class="fas fa-chart-bar me-2"></i><span id="barChartTitle">各区县数据总量</span></h3>
                    <div id="barChart" class="chart"></div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- 扇形图 -->
            <div class="col-md-6">
                <div class="chart-container">
                    <h3 class="chart-title"><i class="fas fa-chart-pie me-2"></i><span id="pieChartTitle">各区县数据占比</span></h3>
                    <div id="pieChart" class="chart"></div>
                </div>
            </div>
            
            <!-- 环形图 -->
            <div class="col-md-6">
                <div class="chart-container">
                    <h3 class="chart-title"><i class="fas fa-chart-pie me-2"></i><span id="ringChartTitle">区县与市直单位数据对比</span></h3>
                    <div id="ringChart" class="chart"></div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 单位Top10弹窗 -->
    <div class="modal fade" id="unitsModal" tabindex="-1" aria-labelledby="unitsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="unitsModalLabel">区县单位数据排名</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3" id="modalLoading">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p class="mt-2">正在加载数据...</p>
                    </div>
                    <div id="modalContent" style="display: none;">
                        <h4 id="countyTitle" class="text-center mb-3"></h4>
                        <div class="row mb-4">
                            <div class="col-md-6 text-center" id="totalDataSection">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h5 class="card-title text-primary" id="totalDataLabel">总数据量</h5>
                                        <h3 class="card-text" id="countyTotalCount">0</h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 text-center" id="incrementDataSection">
                                <div class="card bg-light">
                                    <div class="card-body">
                                        <h5 class="card-title text-success">年度新增量</h5>
                                        <h3 class="card-text" id="countyYearlyIncrement">0</h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-primary">
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">单位名称</th>
                                        <th scope="col">数据量</th>
                                        <th scope="col">占比</th>
                                    </tr>
                                </thead>
                                <tbody id="unitsTableBody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div id="modalError" class="alert alert-danger" style="display: none;"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 引入jQuery和Bootstrap JS -->
    <script src="{{ url_for('static', filename='js/lib/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/lib/bootstrap.bundle.min.js') }}"></script>
    
    <script>
        // 当前数据类型（总数据或新增数据）
        let currentDataType = 'total';
        
        // 格式化数字的函数（添加单位：亿或万）
        function formatLargeNumber(num) {
            if (!num) return '0';
            
            if (num >= 100000000) { // 1亿及以上
                return (num / 100000000).toFixed(2) + '亿';
            } else if (num >= 10000) { // 1万及以上
                return (num / 10000).toFixed(2) + '万';
            } else {
                return num.toLocaleString();
            }
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 格式化数据概览中的数字
            const totalDataSum = document.querySelector('.summary-item:nth-child(1) .summary-value');
            const totalIncrementSum = document.querySelector('.summary-item:nth-child(2) .summary-value');
            
            if (totalDataSum) {
                const rawValue = parseInt(totalDataSum.textContent.trim().replace(/[^0-9]/g, ''));
                totalDataSum.textContent = formatLargeNumber(rawValue);
            }
            
            if (totalIncrementSum) {
                const rawValue = parseInt(totalIncrementSum.textContent.trim().replace(/[^0-9]/g, ''));
                totalIncrementSum.textContent = formatLargeNumber(rawValue);
            }
        });
        
        // 初始化图表
        const barChart = echarts.init(document.getElementById('barChart'));
        const pieChart = echarts.init(document.getElementById('pieChart'));
        const ringChart = echarts.init(document.getElementById('ringChart'));
        
        // 数据
        const countyData = {{ county_data|tojson }};
        const cityUnitData = {{ city_unit_data|tojson }};
        
        // 更新图表函数
        function updateCharts() {
            // 确定要使用的数据字段
            const dataField = currentDataType === 'total' ? 'total_records' : 'yearly_increment';
            const titleSuffix = currentDataType === 'total' ? '数据总量' : '年度新增数据';
            
            // 准备柱状图数据
            const barData = [];
            const countyNames = [];
            const countyValues = [];
            
            // 区县数据
            for (const county in countyData) {
                if (countyData[county][dataField] > 0) {
                    countyNames.push(county);
                    countyValues.push(countyData[county][dataField]);
                    barData.push({
                        name: county,
                        value: countyData[county][dataField],
                        itemStyle: {
                            color: '#5470c6'
                        }
                    });
                }
            }
            
            // 市直单位数据
            for (const unit in cityUnitData) {
                if (cityUnitData[unit][dataField] > 0) {
                    countyNames.push(unit);
                    countyValues.push(cityUnitData[unit][dataField]);
                    barData.push({
                        name: unit,
                        value: cityUnitData[unit][dataField],
                        itemStyle: {
                            color: '#91cc75'
                        }
                    });
                }
            }
            
            // 排序
            barData.sort((a, b) => b.value - a.value);
            
            // 更新柱状图
            barChart.setOption({
                title: {
                    text: `各区县${titleSuffix}`,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        const name = params[0].name;
                        const value = params[0].value;
                        return `${name}: ${formatLargeNumber(value)}`;
                    }
                },
                xAxis: {
                    type: 'category',
                    data: barData.map(item => item.name),
                    axisLabel: {
                        interval: 0,
                        rotate: 30
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: function(value) {
                            return formatLargeNumber(value);
                        }
                    }
                },
                series: [
                    {
                        name: titleSuffix,
                        type: 'bar',
                        data: barData,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function(params) {
                                return formatLargeNumber(params.value);
                            }
                        }
                    }
                ]
            });
            
            // 更新饼图
            pieChart.setOption({
                title: {
                    text: `各区县${titleSuffix}占比`,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        return `${params.name}: ${formatLargeNumber(params.value)} (${params.percent}%)`;
                    }
                },
                legend: {
                    orient: 'vertical',
                    left: 'left',
                    type: 'scroll',
                    pageIconSize: 12,
                    pageTextStyle: {
                        color: '#888'
                    }
                },
                series: [
                    {
                        name: titleSuffix,
                        type: 'pie',
                        radius: '60%',
                        center: ['50%', '50%'],
                        data: barData,
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        label: {
                            formatter: '{b}: {d}%'
                        }
                    }
                ]
            });
            
            // 准备环形图数据（区县与市直单位对比）
            const countySum = Object.values(countyData).reduce((sum, county) => sum + county[dataField], 0);
            const cityUnitSum = Object.values(cityUnitData).reduce((sum, unit) => sum + unit[dataField], 0);
            
            ringChart.setOption({
                title: {
                    text: `区县与市直单位${titleSuffix}对比`,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'item',
                    formatter: function(params) {
                        return `${params.name}: ${formatLargeNumber(params.value)} (${params.percent}%)`;
                    }
                },
                legend: {
                    orient: 'vertical',
                    left: 'left'
                },
                series: [
                    {
                        name: titleSuffix,
                        type: 'pie',
                        radius: ['40%', '70%'],
                        avoidLabelOverlap: false,
                        itemStyle: {
                            borderRadius: 10,
                            borderColor: '#fff',
                            borderWidth: 2
                        },
                        label: {
                            show: false,
                            position: 'center'
                        },
                        emphasis: {
                            label: {
                                show: true,
                                fontSize: '18',
                                fontWeight: 'bold'
                            }
                        },
                        labelLine: {
                            show: false
                        },
                        data: [
                            { value: countySum, name: '区县数据' },
                            { value: cityUnitSum, name: '市直单位数据' }
                        ]
                    }
                ]
            });
            
            // 更新标题
            document.getElementById('barChartTitle').textContent = `各区县${titleSuffix}`;
            document.getElementById('pieChartTitle').textContent = `各区县${titleSuffix}占比`;
            document.getElementById('ringChartTitle').textContent = `区县与市直单位${titleSuffix}对比`;
        }
        
        // 初始化图表
        updateCharts();
        
        // 监听按钮点击
        document.getElementById('totalDataBtn').addEventListener('click', function() {
            currentDataType = 'total';
            updateCharts();
            document.getElementById('totalDataBtn').classList.add('active');
            document.getElementById('incrementDataBtn').classList.remove('active');
        });
        
        document.getElementById('incrementDataBtn').addEventListener('click', function() {
            currentDataType = 'increment';
            updateCharts();
            document.getElementById('incrementDataBtn').classList.add('active');
            document.getElementById('totalDataBtn').classList.remove('active');
        });
        
        // 设置默认选中的按钮
        document.getElementById('totalDataBtn').classList.add('active');
        
        // 窗口大小变化时重新调整图表大小
        window.addEventListener('resize', function() {
            barChart.resize();
            pieChart.resize();
            ringChart.resize();
        });
        
        // 点击图表区域显示区县单位前10数据的功能
        barChart.on('click', function(params) {
            showCountyTopUnits(params.name);
        });
        
        pieChart.on('click', function(params) {
            showCountyTopUnits(params.name);
        });
        
        ringChart.on('click', function(params) {
            if (params.name === '区县数据' || params.name === '市直单位数据') {
                const countyName = params.name === '区县数据' ? '区县' : '市直单位';
                alert(`请点击具体的${countyName}名称查看详细数据`);
                return;
            }
            showCountyTopUnits(params.name);
        });
        
        // 显示区县单位前10数据的函数
        function showCountyTopUnits(countyName) {
            // 显示弹窗
            const unitsModal = new bootstrap.Modal(document.getElementById('unitsModal'));
            const modalContent = document.getElementById('modalContent');
            const modalLoading = document.getElementById('modalLoading');
            const modalError = document.getElementById('modalError');
            const countyTitle = document.getElementById('countyTitle');
            const unitsTableBody = document.getElementById('unitsTableBody');
            const countyTotalCount = document.getElementById('countyTotalCount');
            const countyYearlyIncrement = document.getElementById('countyYearlyIncrement');
            
            // 清空表格内容
            unitsTableBody.innerHTML = '';
            
            // 显示加载动画
            modalLoading.style.display = 'block';
            modalContent.style.display = 'none';
            modalError.style.display = 'none';
            
            // 显示弹窗
            unitsModal.show();
            
            // 从API获取数据，传递当前数据类型
            fetch(`/api/county_top_units/${encodeURIComponent(countyName)}?data_type=${currentDataType}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('获取数据失败');
                    }
                    return response.json();
                })
                .then(data => {
                    // 隐藏加载动画
                    modalLoading.style.display = 'none';
                    
                    // 检查是否有消息
                    if (data.message) {
                        // 显示错误信息
                        modalError.textContent = data.message;
                        modalError.style.display = 'block';
                        return;
                    }
                    
                    // 显示表格内容
                    modalContent.style.display = 'block';
                    
                    // 设置区县标题
                    const titleSuffix = currentDataType === 'total' ? '数据总量' : '年度新增数据';
                    countyTitle.textContent = `${data.county} - 单位${titleSuffix}排名Top10`;
                    
                    // 根据当前数据类型显示不同的数据
                    if (currentDataType === 'total') {
                        // 总数据模式：显示总数据量和年度新增量
                        document.getElementById('totalDataSection').style.display = 'block';
                        document.getElementById('incrementDataSection').style.display = 'block';
                        document.getElementById('totalDataLabel').textContent = '总数据量';
                        countyTotalCount.textContent = formatLargeNumber(data.total_count);
                        countyYearlyIncrement.textContent = formatLargeNumber(data.yearly_increment);
                    } else {
                        // 新增数据模式：只显示年度新增量（作为总量）
                        document.getElementById('totalDataSection').style.display = 'block';
                        document.getElementById('incrementDataSection').style.display = 'none';
                        document.getElementById('totalDataLabel').textContent = '年度新增量';
                        countyTotalCount.textContent = formatLargeNumber(data.total_count); // 在新增数据模式下，total_count已经设置为yearly_increment
                    }
                    
                    // 渲染表格内容
                    data.units.forEach((unit, index) => {
                        const row = document.createElement('tr');
                        row.innerHTML = `
                            <th scope="row">${index + 1}</th>
                            <td>${unit.name || '未知单位'}</td>
                            <td>${formatLargeNumber(unit.count)}</td>
                            <td>${(unit.count / data.total_count * 100).toFixed(2)}%</td>
                        `;
                        unitsTableBody.appendChild(row);
                    });
                })
                .catch(error => {
                    // 隐藏加载动画
                    modalLoading.style.display = 'none';
                    
                    // 显示错误信息
                    modalError.textContent = `获取数据失败: ${error.message}`;
                    modalError.style.display = 'block';
                });
        }
    </script>

    <!-- 版本信息页脚 -->
    <footer class="mt-5 text-center text-muted">
        <p><small>2025 数据管理系统 | 版本 <span id="appVersion">v4</span>
            <a href="javascript:void(0)" class="btn btn-link p-0 ms-2" id="changelogBtn" style="font-size: 1em;vertical-align: baseline;">更新日志</a>
        </small></p>
    </footer>

    <!-- 版本更新日志模态框 -->
    {% include 'changelog_modal.html' %}

    <!-- 版本信息脚本 -->
    <script>
      $(function(){
        // 获取并更新版本号
        fetch('/api/version')
          .then(response => response.json())
          .then(data => {
            $('#appVersion').text(data.version);
          })
          .catch(error => {
            console.error('获取版本信息失败:', error);
          });
        
        // 更新日志按钮点击事件
        $('#changelogBtn').on('click', function(){
          var modal = new bootstrap.Modal(document.getElementById('changelogModal'));
          modal.show();
        });
      });
    </script>
</body>
</html>
