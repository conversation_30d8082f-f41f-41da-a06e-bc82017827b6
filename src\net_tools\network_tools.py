import subprocess
import socket
import re
import platform
import locale
from scapy.all import sr1, IP, ICMP
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import psutil
import json

def is_valid_ip(ip: str) -> bool:
    """
    初步校验IP地址或域名。
    注意：这个校验非常基础，实际生产环境需要更健壮的校验库或逻辑。
    例如，可以使用 'ipaddress' 模块进行更严格的IP校验，
    或者使用正则表达式同时覆盖域名。
    """
    if not ip:
        return False
    # 允许域名中包含字母、数字、点和连字符
    # 简单的域名和IPv4校验
    if re.match(r"^[a-zA-Z0-9.-]+$", ip):
        # 进一步判断是否是合法的IPv4 (非常粗略)
        parts = ip.split('.')
        if len(parts) == 4:
            try:
                return all(0 <= int(part) <= 255 for part in parts)
            except ValueError:
                # 如果包含非数字部分，则可能是域名或无效IP
                pass # 继续判断为域名
        return True # 认为是域名或可解析的主机名
    return False

def ping_ip(ip_address: str, packets: int = 4, timeout_seconds: int = 2) -> dict:
    """
    并发多线程+scapy实现的ping，速度接近原生命令。
    """
    result = {"ip": ip_address, "status": "unknown", "details": "", "rtt_avg_ms": None, "packet_loss_percent": None}
    if not is_valid_ip(ip_address):
        result["status"] = "error"
        result["details"] = "无效的 IP 地址或主机名格式"
        return result

    rtts = []
    loss = 0
    details = [None] * packets

    def single_ping(seq):
        try:
            pkt = IP(dst=ip_address) / ICMP()
            start = time.time()
            reply = sr1(pkt, verbose=0, timeout=timeout_seconds)
            rtt = int((time.time() - start) * 1000)
            if reply is None:
                return seq, None, f"第{seq+1}包: 超时"
            else:
                return seq, rtt, f"第{seq+1}包: 来自 {reply.src}，耗时 {rtt} ms"
        except Exception as e:
            return seq, None, f"第{seq+1}包: 执行失败: {str(e)}"

    try:
        with ThreadPoolExecutor(max_workers=packets) as executor:
            futures = [executor.submit(single_ping, i) for i in range(packets)]
            for future in as_completed(futures):
                seq, rtt, msg = future.result()
                details[seq] = msg
                if rtt is not None:
                    rtts.append(rtt)
                else:
                    loss += 1
        if len(rtts) > 0:
            avg_rtt = sum(rtts) // len(rtts)
        else:
            avg_rtt = None
        result["status"] = "success" if loss < packets else "failed"
        result["rtt_avg_ms"] = avg_rtt
        result["packet_loss_percent"] = int(loss * 100 / packets)
        result["details"] = "\n".join(details)
    except Exception as e:
        result["status"] = "error"
        result["details"] = f"Ping 执行失败: {str(e)}"
    return result

def telnet_port(ip_address: str, port: int, timeout_seconds: int = 5) -> dict:
    """
    测试指定 IP 和端口的连通性。
    """
    result = {"ip": ip_address, "port": port, "status": "unknown", "details": ""}

    if not is_valid_ip(ip_address):
        result["status"] = "error"
        result["details"] = "无效的 IP 地址或主机名格式"
        return result
    
    if not (0 < port < 65536):
        result["status"] = "error"
        result["details"] = "无效的端口号 (必须在 1-65535 之间)"
        return result

    try:
        # socket.create_connection 支持域名解析
        with socket.create_connection((ip_address, port), timeout=timeout_seconds) as sock:
            result["status"] = "open"
            result["details"] = f"端口 {port} 在 {ip_address} 上是开放的"
    except socket.timeout:
        result["status"] = "timeout"
        result["details"] = f"连接 {ip_address}:{port} 超时 ({timeout_seconds} 秒)"
    except socket.gaierror: # Address-related error
        result["status"] = "error"
        result["details"] = f"无法解析主机名或地址: {ip_address}"
    except ConnectionRefusedError:
        result["status"] = "closed"
        result["details"] = f"连接到 {ip_address}:{port} 被拒绝"
    except OSError as e: # Catch other OS-level errors like "No route to host"
        result["status"] = "error"
        result["details"] = f"网络错误: {e.strerror} (连接到 {ip_address}:{port})"
    except Exception as e:
        result["status"] = "error"
        result["details"] = f"发生未知错误: {str(e)}"
        
    return result

def traceroute_ip(ip_address: str, max_hops: int = 30, timeout_seconds: int = 2) -> dict:
    """
    优化版：每一跳收到响应立即进入下一跳，减少整体等待时间。
    """
    result = {"ip": ip_address, "status": "unknown", "details": ""}
    if not is_valid_ip(ip_address):
        result["status"] = "error"
        result["details"] = "无效的 IP 地址或主机名格式"
        return result

    hops = []
    try:
        for ttl in range(1, max_hops + 1):
            pkt = IP(dst=ip_address, ttl=ttl) / ICMP()
            start = time.time()
            reply = sr1(pkt, verbose=0, timeout=timeout_seconds)
            rtt = int((time.time() - start) * 1000)
            if reply is None:
                hops.append(f"{ttl}\t*\t请求超时")
            else:
                hops.append(f"{ttl}\t{reply.src}\t{rtt} ms")
                # 一旦到达目标IP，立即结束，不必等满max_hops
                if reply.src == ip_address:
                    break
        result["status"] = "success"
        result["details"] = "\n".join(hops)
    except Exception as e:
        result["status"] = "error"
        result["details"] = f"Traceroute 执行失败: {str(e)}"
    return result

# 常见端口与服务名映射
PORT_SERVICE_MAP = {
    21: 'ftp', 22: 'ssh', 23: 'telnet', 25: 'smtp', 53: 'dns', 80: 'http', 110: 'pop3', 111: 'rpcbind',
    135: 'msrpc', 139: 'netbios-ssn', 143: 'imap', 443: 'https', 445: 'microsoft-ds', 465: 'smtps',
    993: 'imaps', 995: 'pop3s', 1433: 'mssql', 1521: 'oracle', 1723: 'pptp', 3306: 'mysql',
    3389: 'rdp', 5432: 'postgresql', 5900: 'vnc', 6379: 'redis', 8080: 'http-proxy', 9000: 'smb',
    27017: 'mongodb', 11211: 'memcached', 7001: 'weblogic', 2181: 'zookeeper', 50070: 'hdfs',
    2049: 'nfs', 873: 'rsync', 6000: 'x11', 8888: 'jupyter', 9200: 'elasticsearch', 5601: 'kibana',
    69: 'tftp', 123: 'ntp', 161: 'snmp', 179: 'bgp', 389: 'ldap', 514: 'syslog', 587: 'smtp-submission',
    636: 'ldaps', 3000: 'grafana', 3001: 'grafana-alt', 3128: 'squid-proxy', 3260: 'iscsi', 3690: 'svn',
    4000: 'docker-registry', 4369: 'erlang-epmd', 4444: 'metasploit', 4505: 'saltstack',
    4506: 'saltstack', 4730: 'gearman', 5000: 'flask', 5001: 'flask-ssl', 5355: 'llmnr',
    5500: 'vnc-web', 5666: 'nagios-nrpe', 5672: 'rabbitmq', 5938: 'teamviewer', 5984: 'couchdb',
    6666: 'irc', 6667: 'irc-alt', 7077: 'spark', 7474: 'neo4j', 7547: 'cwmp', 8000: 'http-alt',
    8009: 'ajp13', 8081: 'http-alt2', 8140: 'puppet', 8443: 'https-alt',
    9042: 'cassandra', 9090: 'prometheus', 9092: 'kafka',
    9100: 'prometheus-node', 9300: 'elasticsearch-nodes', 9418: 'git',
    10000: 'webmin', 10050: 'zabbix-agent', 10051: 'zabbix-server',
    27018: 'mongodb-shard', 27019: 'mongodb-config', 28017: 'mongodb-web', 50000: 'sap',
    50075: 'hdfs-datanode', 50090: 'hdfs-secondary',
    # 常见云服务和容器平台端口
    2375: 'docker', 2376: 'docker-tls', 2377: 'docker-swarm', 2380: 'etcd', 
    4243: 'docker-old', 5473: 'influxdb', 6443: 'kubernetes-api', 8472: 'kubernetes-flannel',
    10250: 'kubernetes-kubelet', 10251: 'kubernetes-scheduler', 10252: 'kubernetes-controller',
    10255: 'kubernetes-readonly', 30000: 'kubernetes-nodeport-min', 32767: 'kubernetes-nodeport-max',
    # 常见Windows服务端口
    88: 'kerberos', 464: 'kpasswd', 593: 'http-rpc', 636: 'ldaps', 1433: 'mssql', 
    1434: 'mssql-monitor', 3268: 'globalcatalog', 3269: 'globalcatalog-ssl',
    5985: 'winrm', 5986: 'winrm-ssl', 9389: 'adws',
    # 常见安全与监控服务
    9100: 'node-exporter', 9090: 'prometheus', 3000: 'grafana', 8086: 'influxdb-http',
    8088: 'influxdb-rpc', 9200: 'elasticsearch', 9300: 'elasticsearch-node', 5601: 'kibana',
    # 常见中间件端口
    8161: 'activemq-admin', 61616: 'activemq', 61613: 'activemq-stomp', 1099: 'jmx',
    8080: 'tomcat', 8005: 'tomcat-shutdown', 8009: 'tomcat-ajp', 9043: 'websphere-admin',
    9060: 'websphere-admin-unsecured', 9080: 'websphere-http', 9443: 'websphere-https',
    7001: 'weblogic', 7002: 'weblogic-ssl'
}

def get_service_name(port):
    """获取端口对应的服务名称"""
    return PORT_SERVICE_MAP.get(port, '')

def is_host_reachable(ip_address, timeout=1):
    try:
        pkt = IP(dst=ip_address) / ICMP()
        reply = sr1(pkt, verbose=0, timeout=timeout)
        return reply is not None
    except Exception:
        return False

def identify_process_by_port(ip_address, port):
    """
    尝试识别指定IP和端口上运行的进程
    注意：这个功能主要在本地扫描时有效，远程扫描时通常无法获取进程信息
    """
    process_info = {"name": "", "pid": None, "cmdline": ""}
    
    # 如果是本地地址，尝试获取进程信息
    if ip_address in ('localhost', '127.0.0.1', '::1') or ip_address == socket.gethostbyname(socket.gethostname()):
        try:
            for conn in psutil.net_connections(kind='inet'):
                if conn.laddr.port == port:
                    try:
                        process = psutil.Process(conn.pid)
                        process_info["name"] = process.name()
                        process_info["pid"] = process.pid
                        process_info["cmdline"] = " ".join(process.cmdline())
                        break
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        pass
        except Exception as e:
            pass
    
    return process_info

def get_port_service_info(ip_address, port, timeout=2):
    """
    获取端口服务的详细信息，包括服务名称、进程信息和连接状态
    """
    result = {
        "port": port,
        "service": get_service_name(port),
        "status": "unknown",
        "process": {"name": "", "pid": None, "cmdline": ""},
        "banner": ""
    }
    
    # 检查端口连通性
    try:
        with socket.create_connection((ip_address, port), timeout=timeout) as sock:
            result["status"] = "open"
            
            # 尝试获取服务banner
            try:
                sock.settimeout(1)
                # 针对不同服务的特殊处理
                if port == 3306 or result["service"] == 'mysql':  # MySQL
                    # MySQL特殊处理
                    try:
                        # 只读取前64字节的握手包
                        raw_banner = sock.recv(64)
                        # MySQL版本信息通常在固定位置
                        version_info = ""
                        # 跳过协议头几个字节，从第5个字节开始尝试提取版本号
                        for i in range(5, min(64, len(raw_banner))):
                            if raw_banner[i] >= 32 and raw_banner[i] <= 126:  # 可打印ASCII字符
                                version_info += chr(raw_banner[i])
                            else:
                                # 遇到不可打印字符则停止
                                break
                        # 查找常见MySQL版本字符串
                        if "MySQL" in version_info:
                            result["banner"] = version_info.strip()
                        else:
                            # 尝试从原始数据中匹配版本号格式
                            match = re.search(r'(\d+\.\d+\.\d+)', version_info)
                            if match:
                                result["banner"] = f"MySQL {match.group(1)}"
                            else:
                                result["banner"] = "MySQL 服务"
                    except Exception:
                        result["banner"] = "MySQL 服务"
                elif port == 5432 or result["service"] == 'postgresql':  # PostgreSQL
                    # 发送空查询，获取PostgreSQL响应
                    sock.send(b'\x00\x00\x00\x08\x04\xd2\x16\x2f')
                    raw_banner = sock.recv(64)
                    result["banner"] = "PostgreSQL 服务"
                elif port == 1433 or result["service"] == 'mssql':  # MSSQL
                    result["banner"] = "SQL Server"
                elif port == 27017 or result["service"] == 'mongodb':  # MongoDB
                    result["banner"] = "MongoDB 服务"
                elif port == 6379 or result["service"] == 'redis':  # Redis
                    sock.send(b'INFO\r\n')
                    raw_banner = sock.recv(1024)
                    try:
                        redis_info = raw_banner.decode('utf-8', errors='ignore')
                        version_match = re.search(r'redis_version:(\d+\.\d+\.\d+)', redis_info)
                        if version_match:
                            result["banner"] = f"Redis {version_match.group(1)}"
                        else:
                            result["banner"] = "Redis 服务"
                    except:
                        result["banner"] = "Redis 服务"
                elif port == 22 or result["service"] == 'ssh':  # SSH
                    # SSH通常直接发送版本信息
                    raw_banner = sock.recv(64)
                    try:
                        banner = raw_banner.decode('ascii', errors='ignore').strip()
                        if banner:
                            result["banner"] = banner
                        else:
                            result["banner"] = "SSH 服务"
                    except:
                        result["banner"] = "SSH 服务"
                else:
                    # 对于其他服务，尝试不同编码方式
                    raw_banner = sock.recv(1024)
                    # 尝试多种编码方式
                    decoded = False
                    for encoding in ['utf-8', 'ascii', 'latin1', 'gbk', 'gb2312']:
                        try:
                            banner = raw_banner.decode(encoding, errors='ignore').strip()
                            if banner:
                                # 过滤掉不可打印字符
                                banner = ''.join(c for c in banner if c.isprintable())
                                if len(banner) > 100:
                                    banner = banner[:97] + '...'
                                result["banner"] = banner
                                decoded = True
                                break
                        except:
                            continue
                    
                    # 如果无法解码，尝试进行基本分析
                    if not decoded:
                        # 检查是否包含HTTP头
                        if raw_banner.startswith(b'HTTP/'):
                            try:
                                first_line = raw_banner.split(b'\r\n')[0].decode('ascii', errors='ignore')
                                result["banner"] = first_line
                            except:
                                result["banner"] = "HTTP 服务"
                        elif any(proto in raw_banner for proto in [b'SMTP', b'POP3', b'IMAP', b'FTP', b'SSH']):
                            # 常见文本协议识别
                            for proto in [b'SMTP', b'POP3', b'IMAP', b'FTP', b'SSH']:
                                if proto in raw_banner:
                                    try:
                                        banner = raw_banner.split(b'\r\n')[0].decode('ascii', errors='ignore')
                                        result["banner"] = banner
                                    except:
                                        result["banner"] = f"{proto.decode('ascii')} 服务"
                                    break
                        else:
                            # 二进制服务，仅显示协议名
                            result["banner"] = f"{result['service'] or '未知'} 服务"
            except:
                # 如果获取banner失败，至少显示可能的服务名
                if result["service"]:
                    result["banner"] = f"{result['service']} 服务"
            
            # 尝试识别进程
            process_info = identify_process_by_port(ip_address, port)
            if process_info["name"]:
                result["process"] = process_info
                # 如果获取到了真实进程名，用它作为服务名，覆盖 PORT_SERVICE_MAP 的结果
                result["service"] = process_info["name"]
            # 如果 process_info["name"] 为空，result["service"] 保持 PORT_SERVICE_MAP 的结果 (或者为空，如果端口不在MAP中)
    except socket.timeout:
        result["status"] = "timeout"
    except ConnectionRefusedError:
        result["status"] = "closed"
    except Exception as e:
        result["status"] = "error"
    
    return result

def scan_ports(ip_address: str, ports, timeout: int = 1) -> dict:
    """
    多线程并发端口扫描，返回开放端口列表及服务名。
    ports: 可为list或range
    """
    result = {"ip": ip_address, "status": "unknown", "open_ports": [], "details": ""}
    if not is_valid_ip(ip_address):
        result["status"] = "error"
        result["details"] = "无效的 IP 地址或主机名格式"
        return result
    
    # 尝试解析域名到IP (供显示)
    try:
        resolved_ip = socket.gethostbyname(ip_address)
        if resolved_ip != ip_address:
            result["resolved_ip"] = resolved_ip
    except Exception:
        pass
    
    # 先检测主机可达
    if not is_host_reachable(ip_address, timeout=timeout):
        result["status"] = "warning"  # 改为warning，因为即使ping不通，端口也可能开放(防火墙可能禁了ICMP)
        result["details"] = "注意: 目标主机ICMP不可达，但仍将继续扫描端口"
    
    # 限制并发连接数，避免过载
    max_workers = min(100, len(ports))
    open_ports = []
    
    def check_port(port):
        try:
            with socket.create_connection((ip_address, port), timeout=timeout):
                return port
        except socket.timeout:
            return None  # 超时，可能是防火墙过滤
        except ConnectionRefusedError:
            return None  # 明确拒绝，端口关闭
        except (socket.gaierror, OSError):
            return None  # 其他网络错误
    
    try:
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(check_port, port): port for port in ports}
            for future in as_completed(futures):
                port = future.result()
                if port:
                    open_ports.append(port)
        
        if result["status"] == "unknown":
            result["status"] = "success"
        
        # 返回端口和服务名
        open_ports_info = [
            {"port": p, "service": get_service_name(p)} for p in sorted(open_ports)
        ]
        result["open_ports"] = open_ports_info
        
        # 生成统计信息
        result["scanned_ports_count"] = len(ports)
        result["open_ports_count"] = len(open_ports)
        
        if open_ports_info:
            details_lines = []
            # 添加扫描摘要
            details_lines.append(f"扫描完成: 共扫描 {len(ports)} 个端口，发现 {len(open_ports)} 个开放端口")
            details_lines.append("----------------------------------------")
            # 端口详情
            for item in open_ports_info:
                if item['service']:
                    details_lines.append(f"{item['port']} ({item['service']})")
                else:
                    details_lines.append(f"{item['port']} (未知服务)")
            result["details"] = "\n".join(details_lines)
        else:
            result["details"] = f"扫描完成: 共扫描 {len(ports)} 个端口，未发现开放端口"
    except Exception as e:
        result["status"] = "error"
        result["details"] = f"端口扫描失败: {str(e)}"
    
    return result

def scan_ports_with_details(ip_address, ports, timeout=1):
    """
    增强版端口扫描，返回开放端口列表、服务名及进程信息
    """
    result = {"ip": ip_address, "status": "unknown", "open_ports": [], "details": ""}
    
    if not is_valid_ip(ip_address):
        result["status"] = "error"
        result["details"] = "无效的 IP 地址或主机名格式"
        return result
    
    # 尝试解析域名到IP (供显示)
    try:
        resolved_ip = socket.gethostbyname(ip_address)
        if resolved_ip != ip_address:
            result["resolved_ip"] = resolved_ip
    except Exception:
        pass
    
    # 先检测主机可达
    is_reachable = is_host_reachable(ip_address, timeout=timeout)
    if not is_reachable:
        result["status"] = "warning"  # 改为warning，因为即使ping不通，端口也可能开放(防火墙可能禁了ICMP)
        result["details"] = "注意: 目标主机ICMP不可达，但仍将继续扫描端口"
    
    # 判断是否为本地扫描
    is_local = ip_address in ('localhost', '127.0.0.1', '::1')
    try:
        if ip_address == socket.gethostbyname(socket.gethostname()):
            is_local = True
    except Exception:
        pass
    result["is_local_scan"] = is_local
    
    # 限制并发连接数，避免过载
    max_workers = min(50, len(ports))
    open_ports = []
    port_details = []
    
    def check_port_with_details(port):
        service_info = get_port_service_info(ip_address, port, timeout)
        if service_info["status"] == "open":
            return service_info
        return None
    
    try:
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            futures = {executor.submit(check_port_with_details, port): port for port in ports}
            for future in as_completed(futures):
                port_info = future.result()
                if port_info:
                    open_ports.append(port_info["port"])
                    port_details.append(port_info)
        scan_duration = time.time() - start_time
        
        if result["status"] == "unknown":
            result["status"] = "success"
        
        result["open_ports"] = sorted(port_details, key=lambda x: x["port"])
        
        # 添加扫描统计信息
        result["scan_statistics"] = {
            "total_ports": len(ports),
            "open_ports": len(open_ports),
            "scan_duration_seconds": round(scan_duration, 2)
        }
        
        if port_details:
            details_lines = []
            # 添加扫描摘要
            details_lines.append(f"扫描完成: {ip_address}" + 
                              (f" ({result.get('resolved_ip', '')})" if result.get('resolved_ip') else ""))
            details_lines.append(f"共扫描 {len(ports)} 个端口，发现 {len(open_ports)} 个开放端口，耗时 {round(scan_duration, 2)} 秒")
            
            if is_local:
                details_lines.append("本地扫描模式: 将显示进程信息")
            else:
                details_lines.append("远程扫描模式: 无法获取进程信息")
                
            details_lines.append("----------------------------------------")
            
            # 端口详情表格头
            details_lines.append(f"{'端口':<8}{'服务':<15}{'状态':<8}{'详细信息'}")
            details_lines.append("----------------------------------------")
            
            for port_info in sorted(port_details, key=lambda x: x["port"]):
                port = port_info['port']
                
                # 优先使用进程名，只有在没有进程名时才使用预定义服务
                if port_info['process']['name']:
                    service_display = port_info['process']['name']
                else:
                    service_display = port_info['service'] or '未知服务'
                
                # 添加进程PID信息
                if port_info['process']['pid']:
                    pid_info = f"PID:{port_info['process']['pid']}"
                else:
                    pid_info = ""
                
                # 状态列
                status_display = "开放"
                
                # 详细信息列 - 包含banner信息
                details_display = port_info['banner'] if port_info['banner'] else ""
                
                details_lines.append(f"{port:<8}{service_display:<15}{status_display:<8}{details_display}")
                
                # 如果有PID信息，另起一行显示
                if pid_info:
                    details_lines.append(f"{'':>8}{'':>15}{'':>8}{pid_info}")
            
            result["details"] = "\n".join(details_lines)
        else:
            result["details"] = f"扫描完成: 共扫描 {len(ports)} 个端口，未发现开放端口，耗时 {round(scan_duration, 2)} 秒"
    except Exception as e:
        result["status"] = "error"
        result["details"] = f"端口扫描失败: {str(e)}"
    
    return result

if __name__ == '__main__':
    # 测试代码
    print("--- 测试 Ping ---")
    # 注意：请替换为你可以访问的IP或域名进行测试
    test_ip = "*******" # Google DNS
    
    ping_result1 = ping_ip(test_ip)
    print(f"Ping {test_ip}: {ping_result1['status']}, RTT avg: {ping_result1['rtt_avg_ms']}ms, Loss: {ping_result1['packet_loss_percent']}%")

    print("\n--- 测试 Telnet ---")
    test_telnet_ip = "www.baidu.com"
    test_telnet_port_open = 80
    test_telnet_port_closed = 81 # 假设81端口通常是关闭的
    
    telnet_result1 = telnet_port(test_telnet_ip, test_telnet_port_open)
    print(f"Telnet {test_telnet_ip}:{test_telnet_port_open}: {telnet_result1['status']}")

    telnet_result2 = telnet_port(test_telnet_ip, test_telnet_port_closed)
    print(f"Telnet {test_telnet_ip}:{test_telnet_port_closed}: {telnet_result2['status']}")
    
    print("\n--- 测试 端口扫描 ---")
    test_scan_ip = "localhost"
    test_ports = [80, 443, 3306, 3310, 8080]
    scan_result = scan_ports_with_details(test_scan_ip, test_ports)
    print(f"端口扫描 {test_scan_ip}: {scan_result['status']}")
    print(f"开放端口: {scan_result['details']}")
    
    print("如果测试时出现编码问题（特别是Windows），请确保您的终端和Python环境使用UTF-8。") 