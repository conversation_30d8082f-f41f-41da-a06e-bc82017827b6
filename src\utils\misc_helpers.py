import os
import urllib.request
import configparser # Added for download_static_files

def download_static_files():
    """确保静态文件存在"""
    try:
        # 检查是否存在配置文件
        # Assuming config.ini is in the root of the Flask app, if not, path needs adjustment
        app_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))) # project_root/src/utils -> project_root
        config_path = os.path.join(app_root, 'config.ini')
        skip_download = False
        
        # 如果配置文件存在，读取配置
        if os.path.exists(config_path):
            config = configparser.ConfigParser()
            config.read(config_path, encoding='utf-8')
            if 'SETTINGS' in config and 'skip_static_download' in config['SETTINGS']:
                skip_download = config['SETTINGS'].getboolean('skip_static_download')
        
        # 如果配置为跳过下载，则直接返回
        if skip_download:
            print("根据配置，跳过静态文件下载")
            return
        
        # Base path for static files, assuming this script is in src/utils
        static_base_path = os.path.join(app_root, 'static')

        # 创建目录
        os.makedirs(os.path.join(static_base_path, 'css'), exist_ok=True)
        os.makedirs(os.path.join(static_base_path, 'js'), exist_ok=True)
        os.makedirs(os.path.join(static_base_path, 'js/lib'), exist_ok=True)
        os.makedirs(os.path.join(static_base_path, 'fonts'), exist_ok=True) # Changed from webfonts to fonts to match usage in files_to_download
        os.makedirs(os.path.join(static_base_path, 'webfonts'), exist_ok=True) # Added for webfonts as it's used for fa-solid etc

        # 定义需要下载的文件
        files_to_download = {
            # CSS文件
            'css/bootstrap.min.css': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css',
            'css/bootstrap-datepicker.min.css': 'https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css',
            'css/jquery-ui.min.css': 'https://cdn.bootcdn.net/ajax/libs/jqueryui/1.12.1/jquery-ui.min.css',
            'css/all.min.css': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
            
            # JavaScript文件
            'js/lib/jquery-3.6.0.min.js': 'https://code.jquery.com/jquery-3.6.0.min.js',
            'js/lib/jquery-ui.min.js': 'https://cdn.bootcdn.net/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js',
            'js/lib/bootstrap.bundle.min.js': 'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
            'js/lib/bootstrap-datepicker.min.js': 'https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js',
            'js/lib/bootstrap-datepicker.zh-CN.min.js': 'https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/locales/bootstrap-datepicker.zh-CN.min.js',
            'js/echarts.min.js': 'https://cdn.jsdelivr.net/npm/echarts@5.3.2/dist/echarts.min.js',
            'js/chart.min.js': 'https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js',
            
            # Font Awesome字体文件
            'webfonts/fa-solid-900.woff2': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/webfonts/fa-solid-900.woff2',
            'webfonts/fa-regular-400.woff2': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/webfonts/fa-regular-400.woff2',
            'webfonts/fa-brands-400.woff2': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/webfonts/fa-brands-400.woff2'
        }
        
        # 下载文件
        for relative_path, url in files_to_download.items():
            local_path = os.path.join(static_base_path, relative_path)
            # Ensure directory for the file exists
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            if not os.path.exists(local_path):
                print(f"下载静态文件: {local_path} 从 {url}")
                try:
                    urllib.request.urlretrieve(url, local_path)
                    print(f"成功下载: {local_path}")
                except Exception as e:
                    print(f"下载 {local_path} 失败: {str(e)}")
                    print(f"请手动下载 {url} 并保存到 {local_path}")
    except Exception as e:
        print(f"下载静态文件时出错: {str(e)}") 