/**
 * 服务器管理系统搜索功能
 */

$(document).ready(function() {
    // 搜索服务器功能
    $('#searchServerBtn').click(function() {
        performServerSearch();
    });

    // 搜索端口功能
    $('#searchPortBtn').click(function() {
        performPortSearch();
    });

    // 直接搜索服务器功能
    $('#directServerSearchBtn').click(function() {
        const query = $('#directServerSearch').val().trim();
        if (query) {
            directServerSearch(query);
        } else {
            alert('请输入搜索关键字');
        }
    });

    // 直接搜索端口功能
    $('#directPortSearchBtn').click(function() {
        const query = $('#directPortSearch').val().trim();
        if (query) {
            directPortSearch(query);
        } else {
            alert('请输入搜索关键字');
        }
    });

    // 回车键触发直接搜索
    $('#directServerSearch').keypress(function(e) {
        if (e.which === 13) {
            $('#directServerSearchBtn').click();
        }
    });

    $('#directPortSearch').keypress(function(e) {
        if (e.which === 13) {
            $('#directPortSearchBtn').click();
        }
    });

    // 回车键触发模态框搜索
    $('#searchServerQuery').keypress(function(e) {
        if (e.which === 13) {
            performServerSearch();
        }
    });

    $('#searchPortQuery').keypress(function(e) {
        if (e.which === 13) {
            performPortSearch();
        }
    });

    // 查看服务器详情
    $(document).on('click', '.view-server-details', function(e) {
        e.preventDefault();
        const ip = $(this).data('ip');
        
        // 关闭搜索模态框
        $('#searchServerModal').modal('hide');
        
        // 在主表格中高亮显示对应的行
        highlightServerRow(ip);
    });

    // 查看端口详情
    $(document).on('click', '.view-port-details', function(e) {
        e.preventDefault();
        const ip = $(this).data('ip');
        
        // 关闭搜索模态框
        $('#searchPortModal').modal('hide');
        
        // 在主表格中高亮显示对应的行
        highlightPortRow(ip);
    });
});

// 执行直接服务器搜索
function directServerSearch(query) {
    // 显示加载状态
    $('#serverTableBody').html('<tr><td colspan="9" class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在搜索...</p></td></tr>');
    
    // 发送搜索请求
    $.ajax({
        url: '/api/server/search',
        method: 'GET',
        data: { query: query },
        success: function(response) {
            if (response.success) {
                const servers = response.servers;
                
                if (servers.length === 0) {
                    $('#serverTableBody').html('<tr><td colspan="9" class="text-center py-3">未找到匹配的服务器</td></tr>');
                    return;
                }
                
                // 清空表格
                $('#serverTableBody').empty();
                
                // 填充搜索结果
                servers.forEach(server => {
                    // 构建表格行
                    const row = createServerTableRow(server);
                    $('#serverTableBody').append(row);
                });
                
                // 显示搜索结果提示
                const resultCount = servers.length;
                const alertHtml = `
                    <div class="alert alert-success alert-dismissible fade show mt-2 mb-3" role="alert">
                        <i class="fas fa-check-circle me-2"></i>找到 ${resultCount} 个匹配的服务器
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                
                // 在表格前插入提示
                if ($('#serverSearchAlert').length) {
                    $('#serverSearchAlert').remove();
                }
                
                $('<div id="serverSearchAlert"></div>').insertBefore('#serverManagement .table-responsive').html(alertHtml);
                
                // 重新绑定服务器事件
                bindServerEvents();
            } else {
                $('#serverTableBody').html(`<tr><td colspan="9" class="text-center py-3 text-danger">搜索失败: ${response.message}</td></tr>`);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error searching servers:', error);
            $('#serverTableBody').html('<tr><td colspan="9" class="text-center py-3 text-danger">搜索请求失败，请重试</td></tr>');
        }
    });
}

// 执行直接端口搜索
function directPortSearch(query) {
    // 显示加载状态
    $('#portTableBody').html('<tr><td colspan="5" class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在搜索...</p></td></tr>');
    
    // 发送搜索请求
    $.ajax({
        url: '/api/port/search',
        method: 'GET',
        data: { query: query },
        success: function(response) {
            if (response.success) {
                const ports = response.ports;
                
                if (ports.length === 0) {
                    $('#portTableBody').html('<tr><td colspan="5" class="text-center py-3">未找到匹配的端口信息</td></tr>');
                    return;
                }
                
                // 清空表格
                $('#portTableBody').empty();
                
                // 填充搜索结果
                ports.forEach(port => {
                    // 构建表格行
                    const row = createPortTableRow(port);
                    $('#portTableBody').append(row);
                });
                
                // 显示搜索结果提示
                const resultCount = ports.length;
                const alertHtml = `
                    <div class="alert alert-success alert-dismissible fade show mt-2 mb-3" role="alert">
                        <i class="fas fa-check-circle me-2"></i>找到 ${resultCount} 个匹配的端口信息
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
                
                // 在表格前插入提示
                if ($('#portSearchAlert').length) {
                    $('#portSearchAlert').remove();
                }
                
                $('<div id="portSearchAlert"></div>').insertBefore('#portManagement .table-responsive').html(alertHtml);
                
                // 重新绑定端口事件
                bindPortEvents();
            } else {
                $('#portTableBody').html(`<tr><td colspan="5" class="text-center py-3 text-danger">搜索失败: ${response.message}</td></tr>`);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error searching ports:', error);
            $('#portTableBody').html('<tr><td colspan="5" class="text-center py-3 text-danger">搜索请求失败，请重试</td></tr>');
        }
    });
}

// 创建服务器表格行
function createServerTableRow(server) {
    return `
        <tr data-ip="${server.ip}" data-platform="${server.platform || '未分类'}">
            <td class="text-center">${server.ip || '-'}</td>
            <td class="text-center">${server.hostname || '-'}</td>
            <td class="text-center">${server.cpu || '-'}</td>
            <td class="text-center">${server.memory || '-'}</td>
            <td class="text-center">${server.disk || '-'}</td>
            <td class="text-center">
                <span class="action-link view-usage" 
                      data-ip="${server.ip}" 
                      style="color: blue; cursor: pointer;">
                    详情
                </span>
            </td>
            <td class="text-center">
                <span class="action-link view-directory" 
                      data-ip="${server.ip}" 
                      style="color: blue; cursor: pointer;">
                    详情
                </span>
            </td>
            <td class="text-center">${server.os_system || '-'}</td>
            <td class="text-center">
                <span class="action-link view-components" 
                      data-components="${server.deploy_components || ''}" 
                      data-ip="${server.ip}" 
                      style="color: blue; cursor: pointer;">
                    详情
                </span>
            </td>
            <td class="text-center">
                <span class="action-link edit-server" data-ip="${server.ip}" style="color: blue; cursor: pointer;">编辑</span>&nbsp;
                <span class="action-link delete-server" data-ip="${server.ip}" style="color: red; cursor: pointer;">删除</span>
            </td>
        </tr>
    `;
}

// 创建端口表格行
function createPortTableRow(port) {
    // 格式化端口显示，将多个端口用逗号分隔显示
    const formatPort = (portValue) => {
        if (portValue === null || portValue === undefined || portValue === '') return '-';
        // 确保转换为字符串
        return String(portValue).split(',').map(p => p.trim()).filter(p => p).join(', ');
    };

    return `
        <tr data-ip="${port.ip}">
            <td class="text-center">${port.ip || '-'}</td>
            <td class="text-center">${port.deploy_component || '-'}</td>
            <td class="text-center">${formatPort(port.app_port)}</td>
            <td class="text-center">${formatPort(port.dubbo_server_port)}</td>
            <td class="text-center">${formatPort(port.xxl_job_port)}</td>
            <td class="text-center">
                <span class="action-link edit" data-ip="${port.ip}" style="color: blue; cursor: pointer;">编辑</span>&nbsp;
                <span class="action-link delete" data-ip="${port.ip}" style="color: red; cursor: pointer;">删除</span>
            </td>
        </tr>
    `;
}

// 执行服务器搜索
function performServerSearch() {
    const query = $('#searchServerQuery').val().trim();
    
    if (!query) {
        alert('请输入搜索关键字');
        return;
    }
    
    // 显示加载提示
    $('#searchServerResults').html('<div class="loading-indicator"><div class="spinner-border" role="status"></div><p>正在搜索...</p></div>');
    
    // 发送搜索请求
    $.ajax({
        url: '/api/server/search',
        method: 'GET',
        data: { query: query },
        success: function(response) {
            if (response.success) {
                const servers = response.servers;
                
                if (servers.length === 0) {
                    $('#searchServerResults').html(`
                        <div class="no-results">
                            <i class="fas fa-search"></i>
                            <h5>未找到匹配的服务器</h5>
                            <p>请尝试使用其他关键字搜索</p>
                        </div>
                    `);
                    return;
                }
                
                // 构建结果表格
                let resultsHtml = `
                    <div class="search-results-header">
                        <i class="fas fa-check-circle me-2 text-success"></i>找到 ${servers.length} 个匹配的服务器
                    </div>
                    <table class="search-results-table">
                        <thead>
                            <tr>
                                <th>IP地址</th>
                                <th>主机名</th>
                                <th>CPU</th>
                                <th>内存</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                servers.forEach(server => {
                    resultsHtml += `
                        <tr>
                            <td>${server.ip}</td>
                            <td>${server.hostname || '-'}</td>
                            <td>${server.cpu || '-'}</td>
                            <td>${server.memory || '-'}</td>
                            <td>
                                <a href="#" class="action-link view-server-details" data-ip="${server.ip}" data-components="${server.deploy_components || ''}">
                                    <i class="fas fa-external-link-alt me-1"></i>查看详情
                                </a>
                                <a href="#" class="action-link view-directory" data-ip="${server.ip}">
                                    <i class="fas fa-external-link-alt me-1"></i>查看目录树
                                </a>
                                <a href="#" class="action-link edit-server" data-ip="${server.ip}">
                                    <i class="fas fa-edit me-1"></i>编辑
                                </a>
                                <a href="#" class="action-link delete-server" data-ip="${server.ip}">
                                    <i class="fas fa-trash-alt me-1"></i>删除
                                </a>
                            </td>
                        </tr>
                    `;
                });
                
                resultsHtml += `
                        </tbody>
                    </table>
                `;
                
                $('#searchServerResults').html(resultsHtml);
                
                // 绑定查看详情事件
                bindSearchResultEvents();
            } else {
                $('#searchServerResults').html(`
                    <div class="no-results">
                        <i class="fas fa-exclamation-circle text-danger"></i>
                        <h5>搜索失败</h5>
                        <p>${response.message}</p>
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error searching servers:', error);
            $('#searchServerResults').html(`
                <div class="no-results">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    <h5>搜索请求失败</h5>
                    <p>请检查网络连接并重试</p>
                </div>
            `);
        }
    });
}

// 执行端口搜索
function performPortSearch() {
    const query = $('#searchPortQuery').val().trim();
    
    if (!query) {
        alert('请输入搜索关键字');
        return;
    }
    
    // 显示加载提示
    $('#searchPortResults').html('<div class="loading-indicator"><div class="spinner-border" role="status"></div><p>正在搜索...</p></div>');
    
    // 发送搜索请求
    $.ajax({
        url: '/api/port/search',
        method: 'GET',
        data: { query: query },
        success: function(response) {
            if (response.success) {
                const ports = response.ports;
                
                if (ports.length === 0) {
                    $('#searchPortResults').html(`
                        <div class="no-results">
                            <i class="fas fa-search"></i>
                            <h5>未找到匹配的端口信息</h5>
                            <p>请尝试使用其他关键字搜索</p>
                        </div>
                    `);
                    return;
                }
                
                // 构建结果表格
                let resultsHtml = `
                    <div class="search-results-header">
                        <i class="fas fa-check-circle me-2 text-success"></i>找到 ${ports.length} 个匹配的端口信息
                    </div>
                    <table class="search-results-table">
                        <thead>
                            <tr>
                                <th>IP地址</th>
                                <th>部署组件</th>
                                <th>服务端口</th>
                                <th>Dubbo端口</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                `;
                
                ports.forEach(port => {
                    resultsHtml += `
                        <tr>
                            <td>${port.ip}</td>
                            <td>${port.deploy_component || '-'}</td>
                            <td>${port.app_port || '-'}</td>
                            <td>${port.dubbo_server_port || '-'}</td>
                            <td>
                                <a href="#" class="action-link view-port-details" 
                                   data-ip="${port.ip}" 
                                   data-component="${port.deploy_component || ''}"
                                   data-app-port="${port.app_port || ''}"
                                   data-dubbo-port="${port.dubbo_server_port || ''}"
                                   data-xxl-job-port="${port.xxl_job_port || ''}">
                                    <i class="fas fa-external-link-alt me-1"></i>查看详情
                                </a>
                                <a href="#" class="action-link edit" data-ip="${port.ip}">
                                    <i class="fas fa-edit me-1"></i>编辑
                                </a>
                                <a href="#" class="action-link delete" data-ip="${port.ip}">
                                    <i class="fas fa-trash-alt me-1"></i>删除
                                </a>
                            </td>
                        </tr>
                    `;
                });
                
                resultsHtml += `
                        </tbody>
                    </table>
                `;
                
                $('#searchPortResults').html(resultsHtml);
                
                // 绑定查看详情事件
                bindSearchResultEvents();
            } else {
                $('#searchPortResults').html(`
                    <div class="no-results">
                        <i class="fas fa-exclamation-circle text-danger"></i>
                        <h5>搜索失败</h5>
                        <p>${response.message}</p>
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            console.error('Error searching ports:', error);
            $('#searchPortResults').html(`
                <div class="no-results">
                    <i class="fas fa-exclamation-triangle text-warning"></i>
                    <h5>搜索请求失败</h5>
                    <p>请检查网络连接并重试</p>
                </div>
            `);
        }
    });
}

// 绑定端口表格事件
function bindPortEvents() {
    // 绑定编辑事件
    $('#portTableBody .action-link.edit').click(function(e) {
        const ip = $(this).data('ip');
        const row = $(this).closest('tr');
        
        // 获取当前行的数据，并去除占位符
        $('#edit_port_ip').val(ip);
        $('#edit_deploy_component').val(row.find('td:eq(1)').text().trim() === '-' ? '' : row.find('td:eq(1)').text());
        $('#edit_app_port').val(row.find('td:eq(2)').text().trim() === '-' ? '' : row.find('td:eq(2)').text());
        $('#edit_dubbo_port').val(row.find('td:eq(3)').text().trim() === '-' ? '' : row.find('td:eq(3)').text());
        $('#edit_xxl_job_port').val(row.find('td:eq(4)').text().trim() === '-' ? '' : row.find('td:eq(4)').text());
        
        $('#editPortModal').modal('show');
    });

    // 绑定删除事件
    $('#portTableBody .action-link.delete').click(function(e) {
        const ip = $(this).data('ip');
        if (confirm('确定要删除该端口信息吗？')) {
            $.ajax({
                url: '/api/port',
                method: 'DELETE',
                data: JSON.stringify({ ip: ip }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.success) {
                        // 重新加载端口数据
                        loadPortData();
                    } else {
                        alert('删除失败：' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error deleting port:', error);
                    alert('删除请求失败，请重试');
                }
            });
        }
    });
}

// 绑定服务器表格事件
function bindServerEvents() {
    // 先解绑所有事件，防止重复绑定
    $('#serverTableBody .action-link.view-components').off('click');
    $('#serverTableBody .action-link.view-directory').off('click');
    $('#serverTableBody .action-link.view-usage').off('click');
    $('#serverTableBody .action-link.edit-server').off('click');
    $('#serverTableBody .action-link.delete-server').off('click');
    
    // 绑定查看组件事件
    $('#serverTableBody .action-link.view-components').click(function() {
        // 防止多次打开模态框
        if ($('#componentListModal').hasClass('show')) {
            return;
        }
        
        const ip = $(this).data('ip');
        const components = $(this).data('components');
        
        // 显示组件列表
        $('#componentListModalTitle').text('服务器 ' + ip + ' 的部署组件');
        
        if (components && components.length > 0) {
            let componentHtml = '<ul class="list-group">';
            components.split(',').forEach(component => {
                componentHtml += `<li class="list-group-item">${component.trim()}</li>`;
            });
            componentHtml += '</ul>';
            $('#componentListModalBody').html(componentHtml);
        } else {
            $('#componentListModalBody').html('<div class="alert alert-info text-center"><i class="fas fa-info-circle me-2"></i>该服务器没有部署组件</div>');
        }
        
        $('#componentListModal').modal('show');
    });
    
    // 绑定查看目录事件
    $('#serverTableBody .action-link.view-directory').click(function() {
        const ip = $(this).data('ip');
        showServerDirectory(ip);
    });
    
    // 绑定查看使用率事件
    $('#serverTableBody .action-link.view-usage').click(function() {
        const ip = $(this).data('ip');
        showServerUsage(ip);
    });
    
    // 绑定编辑服务器事件
    $('#serverTableBody .action-link.edit-server').click(function() {
        const ip = $(this).data('ip');
        // 加载服务器详情并打开编辑模态框
        $.get('/api/server/detail?ip=' + ip, function(response) {
            if (response.success) {
                const server = response.server;
                
                // 填充表单
                $('#edit_server_ip').val(server.ip);
                $('#edit_hostname').val(server.hostname || '');
                $('#edit_cpu').val(server.cpu || '');
                $('#edit_memory').val(server.memory || '');
                $('#edit_disk').val(server.disk || '');
                $('#edit_os_system').val(server.os_system || '');
                $('#edit_platform').val(server.platform || '');
                $('#edit_deploy_components').val(server.deploy_components || '');
                
                // 打开模态框
                $('#editServerModal').modal('show');
            } else {
                alert('获取服务器详情失败：' + response.message);
            }
        });
    });
    
    // 绑定删除服务器事件
    $('#serverTableBody .action-link.delete-server').click(function() {
        const ip = $(this).data('ip');
        if (confirm('确定要删除该服务器吗？')) {
            $.ajax({
                url: '/api/server',
                method: 'DELETE',
                data: JSON.stringify({ ip: ip }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.success) {
                        // 重新加载服务器数据
                        loadServerData();
                    } else {
                        alert('删除失败：' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error deleting server:', error);
                    alert('删除请求失败，请重试');
                }
            });
        }
    });
}

// 绑定搜索结果事件
function bindSearchResultEvents() {
    // 先解绑所有事件，防止重复绑定
    $('#searchServerResults .action-link.view-server-details').off('click');
    $('#searchServerResults .action-link.view-directory').off('click');
    $('#searchServerResults .action-link.edit-server').off('click');
    $('#searchServerResults .action-link.delete-server').off('click');
    $('#searchPortResults .action-link.view-port-details').off('click');
    $('#searchPortResults .action-link.edit').off('click');
    $('#searchPortResults .action-link.delete').off('click');
    
    // 绑定查看详情事件
    $('#searchServerResults .action-link.view-server-details').click(function(e) {
        e.preventDefault();
        const ip = $(this).data('ip');
        const components = $(this).data('components');
        
        // 关闭搜索模态框
        $('#searchServerModal').modal('hide');
        
        // 在主表格中高亮显示对应的行
        highlightServerRow(ip);
        
        // 显示资源使用率
        showServerUsage(ip);
    });
    
    // 绑定查看目录树事件
    $('#searchServerResults .action-link.view-directory').click(function(e) {
        e.preventDefault();
        const ip = $(this).data('ip');
        showServerDirectory(ip);
    });
    
    // 绑定编辑服务器事件
    $('#searchServerResults .action-link.edit-server').click(function(e) {
        e.preventDefault();
        const ip = $(this).data('ip');
        
        // 关闭搜索模态框
        $('#searchServerModal').modal('hide');
        
        // 加载服务器详情并打开编辑模态框
        $.get('/api/server/detail?ip=' + ip, function(response) {
            if (response.success) {
                const server = response.server;
                
                // 填充表单
                $('#edit_server_ip').val(server.ip);
                $('#edit_hostname').val(server.hostname || '');
                $('#edit_cpu').val(server.cpu || '');
                $('#edit_memory').val(server.memory || '');
                $('#edit_disk').val(server.disk || '');
                $('#edit_os_system').val(server.os_system || '');
                $('#edit_platform').val(server.platform || '');
                $('#edit_deploy_components').val(server.deploy_components || '');
                
                // 打开模态框
                $('#editServerModal').modal('show');
            } else {
                alert('获取服务器详情失败：' + response.message);
            }
        });
    });
    
    // 绑定删除服务器事件
    $('#searchServerResults .action-link.delete-server').click(function(e) {
        e.preventDefault();
        const ip = $(this).data('ip');
        
        // 关闭搜索模态框
        $('#searchServerModal').modal('hide');
        
        if (confirm('确定要删除该服务器吗？')) {
            $.ajax({
                url: '/api/server',
                method: 'DELETE',
                data: JSON.stringify({ ip: ip }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.success) {
                        // 重新加载服务器数据
                        loadServerData();
                    } else {
                        alert('删除失败：' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error deleting server:', error);
                    alert('删除请求失败，请重试');
                }
            });
        }
    });
    
    // 绑定端口详情查看事件
    $('#searchPortResults .action-link.view-port-details').click(function(e) {
        e.preventDefault();
        const ip = $(this).data('ip');
        const component = $(this).data('component');
        const appPort = $(this).data('app-port');
        const dubboPort = $(this).data('dubbo-port');
        const xxlJobPort = $(this).data('xxl-job-port');
        
        // 关闭搜索模态框
        $('#searchPortModal').modal('hide');
        
        // 在主表格中高亮显示对应的行
        highlightPortRow(ip);
        
        // 显示端口详情
        showPortDetails(ip, component, appPort, dubboPort, xxlJobPort);
    });
    
    // 绑定编辑端口事件
    $('#searchPortResults .action-link.edit').click(function(e) {
        e.preventDefault();
        const ip = $(this).data('ip');
        
        // 关闭搜索模态框
        $('#searchPortModal').modal('hide');
        
        // 获取端口详情
        $.get('/api/port/detail?ip=' + ip, function(response) {
            if (response.success) {
                const port = response.port;
                
                // 填充表单
                $('#edit_port_ip').val(port.ip);
                $('#edit_deploy_component').val(port.deploy_component || '');
                $('#edit_app_port').val(port.app_port || '');
                $('#edit_dubbo_port').val(port.dubbo_server_port || '');
                $('#edit_xxl_job_port').val(port.xxl_job_port || '');
                
                // 打开模态框
                $('#editPortModal').modal('show');
            } else {
                alert('获取端口详情失败：' + response.message);
            }
        });
    });
    
    // 绑定删除端口事件
    $('#searchPortResults .action-link.delete').click(function(e) {
        e.preventDefault();
        const ip = $(this).data('ip');
        
        // 关闭搜索模态框
        $('#searchPortModal').modal('hide');
        
        if (confirm('确定要删除该端口信息吗？')) {
            $.ajax({
                url: '/api/port',
                method: 'DELETE',
                data: JSON.stringify({ ip: ip }),
                contentType: 'application/json',
                success: function(response) {
                    if (response.success) {
                        // 重新加载端口数据
                        loadPortData();
                    } else {
                        alert('删除失败：' + response.message);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error deleting port:', error);
                    alert('删除请求失败，请重试');
                }
            });
        }
    });
}

// 显示服务器资源使用率
function showServerUsage(ip) {
    // 防止多次打开模态框
    if ($('#usageModal').hasClass('show')) {
        return;
    }
    
    // 显示加载状态
    $('#usageModalTitle').text('服务器 ' + ip + ' 的资源使用率');
    $('#usageModalBody').html('<div class="text-center py-3"><div class="spinner-border text-primary" role="status"></div><p class="mt-2">正在加载资源使用率...</p></div>');
    $('#usageModal').modal('show');
    
    // 加载资源使用率
    $.ajax({
        url: '/api/server/usage',
        method: 'GET',
        data: { ip: ip },
        success: function(response) {
            console.log('Server usage response:', response); // 添加日志
            
            if (response.success) {
                // 注意：后端返回的数据格式是 response.data，而不是 response.usage
                const usage = response.data;
                console.log('Usage data:', usage); // 添加日志
                
                if (usage && (usage.cpu_usage !== undefined || usage.mem_usage !== undefined || usage.disk_usage !== undefined)) {
                    let usageHtml = '<div class="row">';
                    
                    // CPU使用率
                    usageHtml += `
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">CPU使用率</h5>
                                    <div class="progress mb-2">
                                        <div class="progress-bar" role="progressbar" style="width: ${usage.cpu_usage || 0}%;" aria-valuenow="${usage.cpu_usage || 0}" aria-valuemin="0" aria-valuemax="100">${usage.cpu_usage || 0}%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // 内存使用率
                    usageHtml += `
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">内存使用率</h5>
                                    <div class="progress mb-2">
                                        <div class="progress-bar" role="progressbar" style="width: ${usage.mem_usage || 0}%;" aria-valuenow="${usage.mem_usage || 0}" aria-valuemin="0" aria-valuemax="100">${usage.mem_usage || 0}%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // 磁盘使用率
                    usageHtml += `
                        <div class="col-md-4 mb-3">
                            <div class="card">
                                <div class="card-body text-center">
                                    <h5 class="card-title">磁盘使用率</h5>
                                    <div class="progress mb-2">
                                        <div class="progress-bar" role="progressbar" style="width: ${usage.disk_usage || 0}%;" aria-valuenow="${usage.disk_usage || 0}" aria-valuemin="0" aria-valuemax="100">${usage.disk_usage || 0}%</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                    
                    // 添加更新时间信息
                    if (usage.timestamp) {
                        usageHtml += `
                            <div class="col-12 mt-3">
                                <p class="text-muted text-center">最后更新时间: ${new Date(usage.timestamp).toLocaleString()}</p>
                            </div>
                        `;
                    }
                    
                    usageHtml += '</div>';
                    $('#usageModalBody').html(usageHtml);
                } else {
                    $('#usageModalBody').html('<div class="alert alert-info text-center"><i class="fas fa-info-circle me-2"></i>该服务器暂无资源使用率数据</div>');
                }
            } else {
                $('#usageModalBody').html(`<div class="alert alert-info text-center"><i class="fas fa-info-circle me-2"></i>该服务器暂无资源使用率数据</div>`);
            }
        },
        error: function(xhr, status, error) {
            // 不在控制台输出错误信息
            $('#usageModalBody').html('<div class="alert alert-info text-center"><i class="fas fa-info-circle me-2"></i>该服务器暂无资源使用率数据</div>');
        }
    });
}

// 显示端口详情
function showPortDetails(ip, component, appPort, dubboPort, xxlJobPort) {
    $('#portDetailModalTitle').text('端口 ' + ip + ' 的详情');
    $('#portDetailModalBody').html(`
        <div class="table-responsive">
            <table class="table table-bordered">
                <tr>
                    <th class="bg-light">IP</th>
                    <td>${ip || '-'}</td>
                </tr>
                <tr>
                    <th class="bg-light">部署组件</th>
                    <td>${component || '-'}</td>
                </tr>
                <tr>
                    <th class="bg-light">服务端口</th>
                    <td>${appPort || '-'}</td>
                </tr>
                <tr>
                    <th class="bg-light">Dubbo端口</th>
                    <td>${dubboPort || '-'}</td>
                </tr>
                <tr>
                    <th class="bg-light">XXL Job端口</th>
                    <td>${xxlJobPort || '-'}</td>
                </tr>
            </table>
        </div>
    `);
    $('#portDetailModal').modal('show');
}

// 高亮显示服务器表格中的特定行
function highlightServerRow(ip) {
    // 移除所有行的高亮
    $('#serverTableBody tr').removeClass('table-primary');
    
    // 高亮匹配IP的行
    $('#serverTableBody tr[data-ip="' + ip + '"]').addClass('table-primary');
    
    // 滚动到该行
    const row = $('#serverTableBody tr[data-ip="' + ip + '"]');
    if (row.length > 0) {
        $('html, body').animate({
            scrollTop: row.offset().top - 100
        }, 500);
    }
}

// 高亮显示端口表格中的特定行
function highlightPortRow(ip) {
    // 移除所有行的高亮
    $('#portTableBody tr').removeClass('table-primary');
    
    // 高亮匹配IP的行
    $('#portTableBody tr[data-ip="' + ip + '"]').addClass('table-primary');
    
    // 滚动到该行
    const row = $('#portTableBody tr[data-ip="' + ip + '"]');
    if (row.length > 0) {
        $('html, body').animate({
            scrollTop: row.offset().top - 100
        }, 500);
    }
}

// 从JSON构建目录树
function buildTreeFromJson(jsonData) {
    if (!jsonData) {
        return '<div class="alert alert-info">目录数据为空</div>';
    }
    
    console.log('Building tree from JSON data:', jsonData);
    
    // 检查是否是path格式的数据
    if (Array.isArray(jsonData)) {
        console.log('JSON data is an array');
        // 如果是数组但没有path属性，尝试将其视为目录列表
        if (jsonData.length > 0) {
            if (jsonData[0].path) {
                console.log('Array contains path objects');
                // 将path格式转换为树形结构
                return buildTreeFromPaths(jsonData);
            } else if (jsonData[0].name || jsonData[0].type) {
                console.log('Array contains file/directory objects');
                // 构建文件/目录列表
                return buildFileDirectoryList(jsonData);
            } else {
                console.log('Array contains unknown format');
                // 尝试直接显示数组内容
                return buildSimpleList(jsonData);
            }
        }
    } else if (typeof jsonData === 'string') {
        // 尝试解析JSON字符串
        try {
            console.log('JSON data is a string, parsing...');
            const parsedData = JSON.parse(jsonData);
            return buildTreeFromJson(parsedData);
        } catch (e) {
            console.error('JSON parse error:', e);
            return `<div class="alert alert-danger">JSON解析错误: ${e}</div>`;
        }
    }
    
    // 原来的树形结构处理逻辑
    console.log('Using default tree node builder');
    return buildTreeNode(jsonData);
}

// 构建简单列表
function buildSimpleList(items) {
    if (!items || items.length === 0) {
        return '<div class="alert alert-info">目录数据为空</div>';
    }
    
    let html = '<ul class="list-group">';
    
    items.forEach(item => {
        let itemText = '';
        if (typeof item === 'string') {
            itemText = item;
        } else if (typeof item === 'object') {
            itemText = JSON.stringify(item);
        } else {
            itemText = String(item);
        }
        
        html += `<li class="list-group-item">${itemText}</li>`;
    });
    
    html += '</ul>';
    return html;
}

// 构建文件/目录列表
function buildFileDirectoryList(items) {
    if (!items || items.length === 0) {
        return '<div class="alert alert-info">目录数据为空</div>';
    }
    
    let html = '<ul class="tree" style="list-style-type: none; padding-left: 20px;">';
    
    items.forEach(item => {
        const isDir = item.type === 'directory';
        const icon = isDir ? 'fa-folder' : 'fa-file';
        const name = item.name || '未命名';
        
        html += `<li>`;
        html += `<span>
                   <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                   ${name}
                 </span>`;
        html += `</li>`;
    });
    
    html += '</ul>';
    return html;
}

// 构建目录树节点
function buildTreeNode(node) {
    if (!node) return '';
    
    let html = '<ul class="tree" style="list-style-type: none; padding-left: 20px;">';
    
    // 处理根节点
    if (node.name && node.type) {
        const isDir = node.type === 'directory';
        const icon = isDir ? 'fa-folder' : 'fa-file';
        const hasChildren = node.children && node.children.length > 0;
        
        html += `<li>`;
        if (isDir && hasChildren) {
            html += `<a href="#" class="tree-toggler">
                       <i class="fas fa-caret-right mr-1"></i>
                       <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                       ${node.name}
                     </a>`;
            html += buildTreeNodeChildren(node.children);
        } else {
            html += `<span>
                       <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                       ${node.name}
                     </span>`;
        }
        html += `</li>`;
    } 
    // 处理根对象包含children的情况
    else if (node.children) {
        node.children.forEach(child => {
            const isDir = child.type === 'directory';
            const icon = isDir ? 'fa-folder' : 'fa-file';
            const hasChildren = child.children && child.children.length > 0;
            
            html += `<li>`;
            if (isDir && hasChildren) {
                html += `<a href="#" class="tree-toggler">
                           <i class="fas fa-caret-right mr-1"></i>
                           <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                           ${child.name}
                         </a>`;
                html += `<ul class="tree" style="display: none; list-style-type: none; padding-left: 20px;">`;
                html += buildTreeNodeChildren(child.children);
                html += `</ul>`;
            } else {
                html += `<span>
                           <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                           ${child.name}
                         </span>`;
            }
            html += `</li>`;
        });
    }
    
    html += '</ul>';
    return html;
}

// 构建子节点
function buildTreeNodeChildren(children) {
    if (!children || children.length === 0) return '';
    
    let html = '<ul class="tree" style="display: none; list-style-type: none; padding-left: 20px;">';
    
    children.forEach(child => {
        const isDir = child.type === 'directory';
        const icon = isDir ? 'fa-folder' : 'fa-file';
        const hasChildren = child.children && child.children.length > 0;
        
        html += `<li>`;
        if (isDir && hasChildren) {
            html += `<a href="#" class="tree-toggler">
                       <i class="fas fa-caret-right mr-1"></i>
                       <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                       ${child.name}
                     </a>`;
            html += buildTreeNodeChildren(child.children);
        } else {
            html += `<span>
                       <i class="fas ${icon} mr-1" style="color: ${isDir ? '#ffc107' : '#6c757d'};"></i>
                       ${child.name}
                     </span>`;
        }
        html += `</li>`;
    });
    
    html += '</ul>';
    return html;
}

// 从路径数组构建目录树
function buildTreeFromPaths(pathsArray) {
    if (!pathsArray || pathsArray.length === 0) {
        return '<div class="alert alert-info">目录数据为空</div>';
    }
    
    // 构建路径树
    const pathTree = {};
    
    pathsArray.forEach((item, index) => {
        if (!item.path) {
            return;
        }
        
        const pathParts = item.path.split('/').filter(part => part);
        
        let currentLevel = pathTree;
        
        pathParts.forEach((part, idx) => {
            if (!currentLevel[part]) {
                currentLevel[part] = {};
            }
            currentLevel = currentLevel[part];
        });
    });
    
    // 生成HTML
    return buildPathTreeHtml(pathTree);
}

// 构建路径树HTML
function buildPathTreeHtml(node, path = '') {
    if (!node || Object.keys(node).length === 0) return '';
    
    let html = '<ul class="tree" style="list-style-type: none; padding-left: 20px;">';
    
    Object.keys(node).forEach(key => {
        const currentPath = path ? `${path}/${key}` : `/${key}`;
        const hasChildren = Object.keys(node[key]).length > 0;
        
        html += `<li>`;
        if (hasChildren) {
            html += `<a href="#" class="tree-toggler">
                       <i class="fas fa-caret-right mr-1"></i>
                       <i class="fas fa-folder mr-1" style="color: #ffc107;"></i>
                       ${key}
                     </a>`;
            html += `<ul class="tree" style="display: none; list-style-type: none; padding-left: 20px;">`;
            html += buildPathTreeHtml(node[key], currentPath);
            html += `</ul>`;
        } else {
            html += `<span>
                       <i class="fas fa-folder mr-1" style="color: #ffc107;"></i>
                       ${key}
                     </span>`;
        }
        html += `</li>`;
    });
    
    html += '</ul>';
    return html;
}

// 显示服务器目录树
function showServerDirectory(ip) {
    // 防止多次打开模态框
    if ($('#directoryTreeModal').hasClass('show')) {
        return;
    }
    
    // 显示加载状态
    $('#directoryTreeModal .modal-title').text('服务器 ' + ip + ' 的目录结构');
    $('#directoryTreeContent').html('<div class="text-center py-3"><div class="spinner-border" role="status"></div><p class="mt-2">正在加载目录结构...</p></div>');
    $('#directoryTreeModal').modal('show');
    
    // 加载目录结构
    $.ajax({
        url: '/api/server/directory',
        method: 'GET',
        data: { ip: ip },
        beforeSend: function() {
            // 在请求发送前清除之前的错误处理器
            $(document).off('ajaxError.directoryTree');
        },
        success: function(response) {
            if (response.success) {
                // 注意：后端返回的数据格式是 response.data，而不是 response.directory_tree
                const directoryTree = response.data;
                
                if (directoryTree && Object.keys(directoryTree).length > 0) {
                    try {
                        // 尝试解析JSON（如果是字符串）
                        let treeData = directoryTree;
                        if (typeof directoryTree === 'string') {
                            try {
                                treeData = JSON.parse(directoryTree);
                            } catch (parseError) {
                                // 如果解析失败，使用原始字符串
                            }
                        }
                        
                        // 生成树HTML
                        const treeHtml = buildTreeFromJson(treeData);
                        
                        // 更新模态框内容
                        $('#directoryTreeContent').html(treeHtml);
                        
                        // 绑定树节点展开/折叠事件
                        $('#directoryTreeContent .tree-toggler').click(function() {
                            $(this).parent().children('ul.tree').toggle(300);
                            $(this).find('i.fas').first().toggleClass('fa-caret-right fa-caret-down');
                            $(this).find('i.fas').eq(1).toggleClass('fa-folder fa-folder-open');
                        });
                    } catch (e) {
                        $('#directoryTreeContent').html('<div class="alert alert-info text-center"><i class="fas fa-info-circle me-2"></i>目录结构数据格式不正确</div>');
                    }
                } else {
                    $('#directoryTreeContent').html('<div class="alert alert-info text-center"><i class="fas fa-info-circle me-2"></i>该服务器暂无目录结构数据</div>');
                }
            } else {
                $('#directoryTreeContent').html('<div class="alert alert-info text-center"><i class="fas fa-info-circle me-2"></i>该服务器暂无目录结构数据</div>');
            }
        },
        error: function(xhr, status, error) {
            // 不在控制台输出错误信息
            $('#directoryTreeContent').html('<div class="alert alert-info text-center"><i class="fas fa-info-circle me-2"></i>该服务器暂无目录结构数据</div>');
        }
    });
}
