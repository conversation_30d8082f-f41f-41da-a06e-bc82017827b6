// 使用 $(document).ready 确保 DOM 加载完毕后再执行脚本
$(document).ready(function() {
    // --- 全局变量 ---
    let currentPage = 1;
    const limit = 50;
    let currentStatsData = null;
    let currentRiskRules = null;
    let currentWriteRiskLevels = null;
    let currentServerToDelete = null;

    // --- 中文映射 ---
    const riskLevelMap = { 'Low': '低危', 'Medium': '中危', 'High': '高危' };

    // --- 初始化日期范围选择器 ---
    const defaultStartDate = moment().subtract(6, 'days'); 
    const defaultEndDate = moment(); 
    try { 
        $('#daterange').daterangepicker({ 
            startDate: defaultStartDate, 
            endDate: defaultEndDate, 
            locale: { 
                format: 'YYYY-MM-DD', 
                applyLabel: '确定', 
                cancelLabel: '取消', 
                fromLabel: '从', 
                toLabel: '到', 
                customRangeLabel: '自定义范围', 
                daysOfWeek: ['日', '一', '二', '三', '四', '五', '六'], 
                monthNames: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'], 
                firstDay: 1 
            }, 
            ranges: { 
                '今天': [moment(), moment()], 
                '昨天': [moment().subtract(1, 'days'), moment().subtract(1, 'days')], 
                '最近 7 天': [moment().subtract(6, 'days'), moment()], 
                '最近 30 天': [moment().subtract(29, 'days'), moment()], 
                '本月': [moment().startOf('month'), moment().endOf('month')], 
                '上个月': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')] 
            } 
        }, function(start, end, label) { 
            fetchData(); 
        }); 
        $('#daterange').val(defaultStartDate.format('YYYY-MM-DD') + ' - ' + defaultEndDate.format('YYYY-MM-DD')); 
    } catch (e) { 
        $('#daterange').val('日期组件加载失败'); 
    }

    // --- 数据获取函数 ---
    function getFilters() { 
        const serverSelect = document.getElementById('server-select');
        const serverId = serverSelect ? serverSelect.value : '';
        
        const operationType = $('#operation-type-select').val(); 
        const riskLevel = $('#risk-level-select').val(); 
        const userName = $('#username-input').val() ? $('#username-input').val().trim() : ''; 
        
        let startDate = ''; 
        let endDate = ''; 
        try { 
            const picker = $('#daterange').data('daterangepicker'); 
            if (picker && picker.startDate && picker.startDate.isValid()) { 
                startDate = picker.startDate.format('YYYY-MM-DD'); 
            }
            if (picker && picker.endDate && picker.endDate.isValid()) { 
                endDate = picker.endDate.format('YYYY-MM-DD'); 
            }
        } catch (e) { } 
        
        return { 
            serverId, 
            startDate, 
            endDate, 
            operationType, 
            riskLevel, 
            userName 
        }; 
    }

    function fetchActivities(page = 1) {
        const filters = getFilters();
        currentPage = page;
        const params = new URLSearchParams({
            server_id: filters.serverId || '',
            start_date: filters.startDate || '',
            end_date: filters.endDate || '',
            operation_type: filters.operationType || '',
            risk_level: filters.riskLevel || '',
            user_name: filters.userName || '',
            page: currentPage,
            limit: limit
        });

        const apiUrl = `/mysql_audit/api/activities?${params.toString()}`;
        
        showLoadingIndicator('activities-table-body');
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`网络错误: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                if (data.data && Array.isArray(data.data)) {
                    renderActivitiesTable(data.data);
                    renderPagination(data.count || 0);
                } else {
                    showError('加载活动记录失败: 数据格式不正确');
                    renderActivitiesTable([]);
                    renderPagination(0);
                }
                
                hideLoadingIndicator('activities-table-body');
            })
            .catch(error => {
                showError(`加载活动记录失败: ${error.message}`);
                renderActivitiesTable([]);
                renderPagination(0);
                hideLoadingIndicator('activities-table-body');
            });
    }

    // --- 渲染函数 ---
    function renderActivitiesTable(activities) {
        const tableBody = document.getElementById('activities-table-body');
        
        if (!tableBody) {
            return;
        }
        
        tableBody.innerHTML = '';
        
        if (!Array.isArray(activities)) {
            tableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-gray-500">数据格式错误</td></tr>';
            return;
        }
        
        if (activities.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="8" class="text-center py-4 text-gray-500">暂无数据</td></tr>';
            return;
        }
        
        let htmlContent = '';
        
        activities.forEach((activity, index) => {
            let rowHtml = '<tr>';
            
            rowHtml += `<td class="px-4 py-2 border-b border-gray-200 text-gray-700">${escapeHtml(activity.id || '')}</td>`;
            rowHtml += `<td class="px-4 py-2 border-b border-gray-200 text-gray-700">${escapeHtml(activity.activity_time || '')}</td>`;
            rowHtml += `<td class="px-4 py-2 border-b border-gray-200 text-gray-700">${escapeHtml(activity.user || '')}</td>`;
            rowHtml += `<td class="px-4 py-2 border-b border-gray-200 text-gray-700">${escapeHtml(activity.client_host || '')}</td>`;
            rowHtml += `<td class="px-4 py-2 border-b border-gray-200 text-gray-700">${escapeHtml(activity.db_name || '')}</td>`;
            rowHtml += `<td class="px-4 py-2 border-b border-gray-200 text-gray-700">${escapeHtml(activity.operation_type || '')}</td>`;
            
            rowHtml += `<td class="px-4 py-2 border-b border-gray-200 text-xs text-gray-600 break-all">`;
            rowHtml += `<span title="${escapeHtml(activity.argument || '')}">${escapeHtml(truncateString(activity.argument || '', 100))}</span>`;
            
            if (activity.details) {
                rowHtml += `<button class="show-details-btn ml-2 text-indigo-600 hover:text-indigo-800" data-details="${escapeHtml(activity.details)}">`;
                rowHtml += `<i data-lucide="info" class="h-4 w-4 inline"></i>`;
                rowHtml += `</button>`;
            }
            rowHtml += `</td>`;
            
            const riskLevel = activity.risk_level || 'unknown';
            const levelLower = riskLevel.toLowerCase();
            const riskText = riskLevel === 'High' ? '高危' : 
                         riskLevel === 'Medium' ? '中危' : 
                         riskLevel === 'Low' ? '低危' : '未知';
            
            rowHtml += `<td class="px-4 py-2 border-b border-gray-200">`;
            rowHtml += `<span class="risk-badge risk-badge-${levelLower}">${riskText}</span>`;
            rowHtml += `</td>`;
            
            rowHtml += '</tr>';
            
            htmlContent += rowHtml;
        });
        
        tableBody.innerHTML = htmlContent;
        
        setTimeout(() => {
            lucide.createIcons();
            bindActivityDetailsHandlers();
        }, 0);
    }

    // --- 数据获取函数 ---
    function fetchStats() {
        try {
            const filters = getFilters();
            const params = new URLSearchParams();
            
            if (filters.serverId && filters.serverId !== '') {
                params.append('server_id', filters.serverId);
            }
            if (filters.startDate) params.append('start_date', filters.startDate);
            if (filters.endDate) params.append('end_date', filters.endDate);
            
            const url = `/mysql_audit/api/stats?${params.toString()}`;
            
            showLoadingIndicator('stats-container');
            clearDashboardCharts();
            
            fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`网络错误: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                
                currentStatsData = data;
                    
                const activeTab = document.querySelector('nav a[data-active="true"]');
                const activeTabId = activeTab ? activeTab.getAttribute('data-tab') : null;
                    
                if (activeTabId === 'dashboard') {
                    setTimeout(() => {
                        renderDashboardCharts();
                    }, 10);
                }
                
                hideLoadingIndicator('stats-container');
            })
            .catch(error => {
                currentStatsData = null;
                clearDashboardCharts();
                showError(`加载统计数据失败: ${error.message}`);
                hideLoadingIndicator('stats-container');
            });
        } catch (e) {
            showError('应用筛选条件时发生错误，请刷新页面重试');
        }
    }

    // --- 渲染函数 ---
    function renderDashboardCharts() { 
        if (!currentStatsData) { 
            clearDashboardCharts(); 
            return; 
        }
        
        clearDashboardCharts();
        
        setTimeout(() => {
            renderSingleChartSet(currentStatsData);
            
            setTimeout(() => {
                if (window.opChart) window.opChart.resize();
                if (window.riskChart) window.riskChart.resize();
                if (window.hourlyChart) window.hourlyChart.resize();
            }, 50);
        }, 100);
    }
    function renderSingleChartSet(stats) {
        const riskChartId = `risk-levels-chart`;
        const opChartId = `op-types-chart`;
        const hourlyChartId = `hourly-chart`;
        const topUsersListId = `top-users-list`;
        
        try {
            if (!stats || typeof stats !== 'object') {
                clearDashboardCharts();
                return;
            }

            renderOperationTypesChart(stats.operation_types || [], opChartId);
            renderRiskLevelsChart(stats.risk_levels || [], riskChartId);
            renderHourlyChart(stats.hourly_distribution || {}, hourlyChartId);
            renderTopUsersList(stats.top_users || [], topUsersListId);
            
        } catch(e) {
            showErrorAlert(`渲染图表时出错: ${e.message}`);
        }
    }
    
    function renderOperationTypesChart(opTypesDataRaw, opChartId) {
        try {
            const opChartElement = document.getElementById(opChartId);
            if (!opChartElement) {
                return;
            }
            
            $(opChartElement).empty();
            
            if (opTypesDataRaw.length > 0) {
                try {
                    if (opChartElement.offsetHeight === 0) {
                        $(opChartElement).css('height', '280px');
                    }
                    
                    if (window.opChart) {
                        try {
                            window.opChart.dispose();
                        } catch (e) { }
                    }
                    
                    setTimeout(() => {
                        window.opChart = echarts.init(opChartElement);
                        window.opChart.setOption({
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b}: {c} ({d}%)'
                            },
                            series: [{
                                name: '操作类型',
                                type: 'pie',
                                radius: ['40%', '70%'],
                                avoidLabelOverlap: true,
                                label: {
                                    show: true,
                                    formatter: '{b}: {d}%'
                                },
                                emphasis: {
                                    label: {
                                        show: true,
                                        fontWeight: 'bold'
                                    }
                                },
                                data: opTypesDataRaw.map(item => ({
                                    name: item.operation_type || 'UNKNOWN',
                                    value: item.count || 0
                                }))
                            }]
                        });
                    }, 0);
                } catch (err) {
                    $(opChartElement).html('<p class="text-center text-gray-500 py-4">图表渲染失败</p>');
                }
            } else {
                $(opChartElement).html('<p class="text-center text-gray-500 py-4">无操作类型数据</p>');
            }
        } catch (e) {
            $(`#${opChartId}`).html('<p class="text-center text-gray-500 py-4">图表渲染失败</p>');
        }
    }
    
    function renderRiskLevelsChart(riskLevelsData, riskChartId) {
        try {
            // 确保DOM元素存在
            const riskChartElement = document.getElementById(riskChartId);
            if (!riskChartElement) {
                console.error(`未找到元素: #${riskChartId}`);
                return;
            }
            
            // 先清空元素内容
            $(riskChartElement).empty();
            
            if (riskLevelsData.length > 0) {
                try {
                    // 确保容器有尺寸
                    if (riskChartElement.offsetHeight === 0) {
                        $(riskChartElement).css('height', '280px');
                    }
                    
                    // 检查是否有已存在的图表实例
                    if (window.riskChart) {
                        try {
                            window.riskChart.dispose();
                        } catch (e) {
                            console.error('销毁旧图表实例失败:', e);
                        }
                    }
                    
                    // 创建新的图表实例
                    setTimeout(() => {
                        window.riskChart = echarts.init(riskChartElement);
                        window.riskChart.setOption({
                        tooltip: {
                            trigger: 'axis',
                            axisPointer: {
                                type: 'shadow'
                            }
                        },
                        xAxis: {
                            type: 'category',
                            data: riskLevelsData.map(item => riskLevelMap[item.risk_level] || item.risk_level || '未知')
                        },
                        yAxis: {
                            type: 'value',
                            name: '次数'
                        },
                        series: [{
                            data: riskLevelsData.map(item => ({
                                value: item.count,
                                itemStyle: {
                                    color: item.risk_level === 'High' ? '#ef4444' : 
                                        item.risk_level === 'Medium' ? '#f59e0b' : '#10b981'
                                }
                            })),
                            type: 'bar'
                        }]
                    });
                    }, 0);
                } catch (err) {
                    console.error(`初始化风险等级图表失败:`, err);
                    $(riskChartElement).html('<p class="text-center text-gray-500 py-4">图表渲染失败</p>');
                }
            } else {
                $(riskChartElement).html('<p class="text-center text-gray-500 py-4">无风险等级数据</p>');
            }
        } catch (e) {
            console.error('渲染风险等级图表失败:', e);
            $(`#${riskChartId}`).html('<p class="text-center text-gray-500 py-4">图表渲染失败</p>');
        }
            }
            
    function renderHourlyChart(hourlyData, hourlyChartId) {
        try {
            const hours = Object.keys(hourlyData).map(h => parseInt(h)).sort((a, b) => a - b);
            const hourlyCounts = hours.map(h => hourlyData[h]);
            
            // 确保DOM元素存在
            const hourlyChartElement = document.getElementById(hourlyChartId);
            if (!hourlyChartElement) {
                console.error(`未找到元素: #${hourlyChartId}`);
                return;
            }
            
            // 先清空元素内容
            $(hourlyChartElement).empty();
            
            if (hours.length > 0 && hourlyCounts.some(c => c > 0)) {
                try {
                    // 确保容器有尺寸
                    if (hourlyChartElement.offsetHeight === 0) {
                        $(hourlyChartElement).css('height', '280px');
                    }
                    
                    // 检查是否有已存在的图表实例
                    if (window.hourlyChart) {
                        try {
                            window.hourlyChart.dispose();
                        } catch (e) {
                            console.error('销毁旧图表实例失败:', e);
                        }
                    }
                    
                    // 创建新的图表实例
                    setTimeout(() => {
                        window.hourlyChart = echarts.init(hourlyChartElement);
                        window.hourlyChart.setOption({
                        tooltip: {
                            trigger: 'axis'
                        },
                        xAxis: {
                            type: 'category',
                            name: '时间（小时）',
                            data: hours.map(h => `${h}:00`)
                        },
                        yAxis: {
                            type: 'value',
                            name: '次数'
                        },
                        series: [{
                            data: hourlyCounts,
                            type: 'line',
                            smooth: true,
                            symbol: 'circle',
                            symbolSize: 5,
                            lineStyle: {
                                width: 3,
                                color: '#4f46e5'
                            },
                            itemStyle: {
                                color: '#4f46e5'
                            }
                        }]
                    });
                    
                        // 添加窗口大小变化监听
                    window.addEventListener('resize', function() {
                            if (window.hourlyChart) {
                                window.hourlyChart.resize();
                            }
                    });
                    }, 0);
                } catch (err) {
                    console.error(`初始化小时分布图表失败:`, err);
                    $(hourlyChartElement).html('<p class="text-center text-gray-500 py-4">图表渲染失败</p>');
                }
            } else {
                $(hourlyChartElement).html('<p class="text-center text-gray-500 py-4">无小时分布数据</p>');
            }
        } catch (e) {
            console.error('渲染小时分布图表失败:', e);
            $(`#${hourlyChartId}`).html('<p class="text-center text-gray-500 py-4">图表渲染失败</p>');
        }
            }
            
    function renderTopUsersList(topUsersData, topUsersListId) {
        try {
            const topUsersContainer = $(`#${topUsersListId}`);
            topUsersContainer.empty();
            
            if (topUsersData.length > 0) {
                topUsersData.forEach((user, index) => {
                    topUsersContainer.append(`<li class="py-1 flex justify-between"><span>${index + 1}. ${escapeHtml(user.user_name)}</span> <span class="font-medium">${user.count} 次</span></li>`);
                });
            } else {
                topUsersContainer.html('<li class="text-center text-gray-500 py-4">无活跃用户数据</li>');
            }
        } catch (e) {
            console.error('渲染用户列表失败:', e);
            $(`#${topUsersListId}`).html('<li class="text-center text-gray-500 py-4">用户数据渲染失败</li>');
        }
    }

    function clearDashboardCharts() {
        // 尝试销毁现有的图表实例
        try {
            if (window.opChart) {
                try {
                    window.opChart.dispose();
                } catch (e) {
                    console.error('销毁操作类型图表时出错:', e);
                } finally {
                    window.opChart = null;
                }
            }
        } catch (e) {
            console.error('销毁操作类型图表时出错:', e);
        }
        
        try {
            if (window.riskChart) {
                try {
                    window.riskChart.dispose();
                } catch (e) {
                    console.error('销毁风险等级图表时出错:', e);
                } finally {
                    window.riskChart = null;
                }
            }
        } catch (e) {
            console.error('销毁风险等级图表时出错:', e);
        }
        
        try {
            if (window.hourlyChart) {
                try {
                    window.hourlyChart.dispose();
                } catch (e) {
                    console.error('销毁小时分布图表时出错:', e);
                } finally {
                    window.hourlyChart = null;
                }
            }
        } catch (e) {
            console.error('销毁小时分布图表时出错:', e);
        }
        
        // 清空容器内容并重置高度
        $('#op-types-chart').empty().css('height', '280px').html('<p class="text-center text-gray-500 py-4">等待加载数据...</p>');
        $('#risk-levels-chart').empty().css('height', '280px').html('<p class="text-center text-gray-500 py-4">等待加载数据...</p>');
        $('#hourly-chart').empty().css('height', '280px').html('<p class="text-center text-gray-500 py-4">等待加载数据...</p>');
        $('#top-users-list').empty().html('<li class="text-center text-gray-500 py-4">等待加载数据...</li>');
    }

    // --- 工具函数 ---
    // (保持不变)
    function showLoadingIndicator(elementId) { $(`#${elementId}`).css('opacity', 0.5); }
    function hideLoadingIndicator(elementId) { $(`#${elementId}`).css('opacity', 1); }
    function showErrorAlert(message) { 
        showError(message); 
    }
    function escapeHtml(unsafe) { 
        if (unsafe === null || unsafe === undefined) return ''; 
        return String(unsafe).replace(/&/g, "&amp;")
            .replace(/</g, "&lt;")
            .replace(/>/g, "&gt;")
            .replace(/"/g, "&quot;")
            .replace(/'/g, "&#039;"); 
    }
    function truncateString(str, num) { if (!str) return ''; if (str.length <= num) return str; return str.slice(0, num) + '...'; }
    
    // 显示错误信息函数
    function showError(message) {
        const errorDiv = document.createElement('div');
        errorDiv.className = 'fixed bottom-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded z-50';
        errorDiv.innerHTML = `
            <span class="block sm:inline">${message}</span>
            <button class="absolute top-0 bottom-0 right-0 px-4 py-3">
                <i data-lucide="x" class="h-4 w-4"></i>
            </button>
        `;
        
        document.body.appendChild(errorDiv);
        lucide.createIcons();
        
        errorDiv.querySelector('button').addEventListener('click', () => {
            document.body.removeChild(errorDiv);
        });
        
        setTimeout(() => {
            if (document.body.contains(errorDiv)) {
                document.body.removeChild(errorDiv);
            }
        }, 5000);
    }

    // --- 服务器配置相关函数 ---
    function fetchServers() {
        showLoadingIndicator('servers-table-body');
        fetch('/mysql_audit/api/servers')
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.error || `HTTP error ${response.status}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                renderServersTable(data.servers || []);
                hideLoadingIndicator('servers-table-body');
            })
            .catch(error => {
                showErrorAlert(error.message || '加载服务器配置失败，请刷新页面重试。');
                renderServersTable([]);
                hideLoadingIndicator('servers-table-body');
            });
    }

    function renderServersTable(servers) {
        const tableBody = $('#servers-table-body');
        tableBody.empty();

        if (!servers || servers.length === 0) {
            tableBody.append('<tr><td colspan="8" class="text-center text-gray-500 py-10">暂无服务器配置，请点击 "添加服务器" 按钮添加</td></tr>');
            return;
        }

        servers.forEach(server => {
            let authDisplay;
            if (server.has_password) {
                authDisplay = '<span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">密码</span>';
            } else if (server.has_ssh_key) {
                authDisplay = '<span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">SSH密钥</span>';
            } else {
                authDisplay = '<span class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">未配置</span>';
            }

            let logScanDisplay = [];
            if (server.enable_general_log) {
                logScanDisplay.push('<span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded text-xs mr-1">General</span>');
            }
            if (server.enable_binlog) {
                logScanDisplay.push('<span class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">Binlog</span>');
            }
            if (logScanDisplay.length === 0) {
                logScanDisplay.push('<span class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">未启用</span>');
            }

            const row = `
                <tr>
                    <td class="px-4 py-2 border-b border-gray-200 text-gray-700">${server.server_id || 'N/A'}</td>
                    <td class="px-4 py-2 border-b border-gray-200 text-gray-700">${escapeHtml(server.name || 'N/A')}</td>
                    <td class="px-4 py-2 border-b border-gray-200 text-gray-700">${escapeHtml(server.host || 'N/A')}</td>
                    <td class="px-4 py-2 border-b border-gray-200 text-gray-700">${server.port || 'N/A'}</td>
                    <td class="px-4 py-2 border-b border-gray-200 text-gray-700">${escapeHtml(server.user || 'N/A')}</td>
                    <td class="px-4 py-2 border-b border-gray-200">${authDisplay}</td>
                    <td class="px-4 py-2 border-b border-gray-200">${logScanDisplay.join('')}</td>
                    <td class="px-4 py-2 border-b border-gray-200">
                        <div class="flex space-x-2">
                            <button class="edit-server-btn text-blue-600 hover:text-blue-800" data-server-id="${server.server_id}">
                                <i data-lucide="edit" class="h-4 w-4"></i>
                            </button>
                            <button class="delete-server-btn text-red-600 hover:text-red-800" data-server-id="${server.server_id}">
                                <i data-lucide="trash-2" class="h-4 w-4"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `;
            tableBody.append(row);
        });
        
        lucide.createIcons();
    }

    function fetchServerDetails(serverId) {
        return fetch(`/mysql_audit/api/server/${serverId}`)
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.error || `HTTP error ${response.status}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                return data.server;
            });
    }

    function saveServer(formData, isNew) {
        const url = isNew ? '/mysql_audit/api/server' : `/mysql_audit/api/server/${formData.server_id}`;
        const method = isNew ? 'POST' : 'PUT';

        return fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData),
        })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.error || `HTTP error ${response.status}`);
                    });
                }
                return response.json();
            });
    }

    function deleteServer(serverId) {
        return fetch(`/mysql_audit/api/server/${serverId}`, {
            method: 'DELETE',
        })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.error || `HTTP error ${response.status}`);
                    });
                }
                return response.json();
            });
    }

    function openServerModal(serverId = null) {
        $('#server-form')[0].reset();
        $('#server-id').val('');
        
        $('#password-auth-container').show();
        $('#ssh-key-auth-container').hide();
        
        if (serverId) {
            $('#server-modal-title').text('编辑服务器');
            
            fetchServerDetails(serverId)
                .then(server => {
                    $('#server-id').val(server.server_id);
                    $('#server-name').val(server.name);
                    $('#server-host').val(server.host);
                    $('#server-port').val(server.port);
                    $('#server-user').val(server.user);
                    
                    if (server.has_ssh_key) {
                        $('#server-auth-type').val('ssh_key');
                        $('#password-auth-container').hide();
                        $('#ssh-key-auth-container').show();
                    } else {
                        $('#server-auth-type').val('password');
                        $('#password-auth-container').show();
                        $('#ssh-key-auth-container').hide();
                    }
                    
                    $('#server-general-log').val(server.general_log_path);
                    $('#server-binlog').val(server.binlog_path);
                    $('#server-enable-general').prop('checked', server.enable_general_log);
                    $('#server-enable-binlog').prop('checked', server.enable_binlog);
                    
                    $('#server-modal').removeClass('hidden');
                })
                .catch(error => {
                    showErrorAlert(error.message || '获取服务器详情失败，请重试。');
                });
        } else {
            $('#server-modal-title').text('添加服务器');
            $('#server-modal').removeClass('hidden');
        }
    }

    function closeServerModal() {
        $('#server-modal').addClass('hidden');
    }

    // --- 风险规则相关函数 ---
    function fetchRiskRules() {
        fetch('/mysql_audit/api/risk_rules')
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.error || `HTTP error ${response.status}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    currentRiskRules = data.risk_rules;
                    renderRiskRules(currentRiskRules);
                }
            })
            .catch(error => {
                showErrorAlert(error.message || '加载风险规则失败，请刷新页面重试。');
            });
    }

    function renderRiskRules(riskRules) {
        renderRiskRuleList('high-risk-rules', riskRules.High || []);
        renderRiskRuleList('medium-risk-rules', riskRules.Medium || []);
        renderRiskRuleList('low-risk-rules', riskRules.Low || []);
    }

    function renderRiskRuleList(containerId, rules) {
        const container = $(`#${containerId}`);
        container.empty();
        
        if (!rules || rules.length === 0) {
            container.html('<p class="text-gray-500">暂无规则配置</p>');
            return;
        }
        
        let html = '<ul class="list-disc pl-5 space-y-1">';
        rules.forEach(rule => {
            if (rule.type) {
                html += `<li><strong>类型：</strong>${escapeHtml(rule.type)}</li>`;
            } else if (rule.keyword) {
                html += `<li><strong>关键字：</strong>${escapeHtml(rule.keyword)}</li>`;
            }
        });
        html += '</ul>';
        
        container.html(html);
    }

    function openRiskRulesModal() {
        if (!currentRiskRules) {
            fetchRiskRules();
            return;
        }
        
        $('#high-risk-items, #medium-risk-items, #low-risk-items').empty();
        
        fillRiskRuleItems('high-risk-items', currentRiskRules.High || []);
        fillRiskRuleItems('medium-risk-items', currentRiskRules.Medium || []);
        fillRiskRuleItems('low-risk-items', currentRiskRules.Low || []);
        
        $('#risk-rules-modal').removeClass('hidden');
    }

    function fillRiskRuleItems(containerId, rules) {
        const container = $(`#${containerId}`);
        
        rules.forEach((rule, index) => {
            addRuleItem(container, rule, index);
        });
    }

    function addRuleItem(container, rule = null, index = null) {
        const itemId = index !== null ? index : Date.now();
        let ruleType = '';
        let ruleKeyword = '';
        
        if (rule) {
            ruleType = rule.type || '';
            ruleKeyword = rule.keyword || '';
        }
        
        const html = `
            <div class="rule-item flex items-center gap-2 mb-2 p-2 bg-white rounded border border-gray-200">
                <div class="flex-1">
                    <div class="mb-1">
                        <label class="inline-flex items-center">
                            <input type="radio" name="rule-type-${itemId}" class="rule-type-radio" value="type" ${ruleType ? 'checked' : ''}>
                            <span class="ml-1 text-sm">类型</span>
                        </label>
                        <input type="text" class="rule-type-input ml-2 px-2 py-1 border border-gray-300 rounded text-sm ${!ruleType ? 'hidden' : ''}" value="${escapeHtml(ruleType)}">
                    </div>
                    <div>
                        <label class="inline-flex items-center">
                            <input type="radio" name="rule-type-${itemId}" class="rule-type-radio" value="keyword" ${ruleKeyword ? 'checked' : ''}>
                            <span class="ml-1 text-sm">关键字</span>
                        </label>
                        <input type="text" class="rule-keyword-input ml-2 px-2 py-1 border border-gray-300 rounded text-sm ${!ruleKeyword ? 'hidden' : ''}" value="${escapeHtml(ruleKeyword)}">
                    </div>
                </div>
                <button type="button" class="delete-rule-btn text-red-600 hover:text-red-800">
                    <i data-lucide="trash-2" class="h-4 w-4"></i>
                </button>
            </div>
        `;
        
        container.append(html);
        lucide.createIcons();
    }

    function closeRiskRulesModal() {
        $('#risk-rules-modal').addClass('hidden');
    }

    function saveRiskRules() {
        const highRiskRules = collectRulesFromContainer('high-risk-items');
        const mediumRiskRules = collectRulesFromContainer('medium-risk-items');
        const lowRiskRules = collectRulesFromContainer('low-risk-items');
        
        const riskRules = {
            High: highRiskRules,
            Medium: mediumRiskRules,
            Low: lowRiskRules
        };
        
        return fetch('/api/risk_rules', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ risk_rules: riskRules }),
        })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.error || `HTTP error ${response.status}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                currentRiskRules = riskRules;
                renderRiskRules(riskRules);
                closeRiskRulesModal();
            });
    }

    function collectRulesFromContainer(containerId) {
        const rules = [];
        $(`#${containerId} .rule-item`).each(function() {
            const $item = $(this);
            const typeRadio = $item.find('.rule-type-radio:checked').val();
            
            if (typeRadio === 'type') {
                const type = $item.find('.rule-type-input').val().trim();
                if (type) {
                    rules.push({ type });
                }
            } else if (typeRadio === 'keyword') {
                const keyword = $item.find('.rule-keyword-input').val().trim();
                if (keyword) {
                    rules.push({ keyword });
                }
            }
        });
        
        return rules;
    }

    // --- 写入风险级别相关函数 ---
    function fetchWriteRiskLevels() {
        fetch('/mysql_audit/api/write_risk_levels')
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.error || `HTTP error ${response.status}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    currentWriteRiskLevels = data.write_risk_levels;
                    renderWriteRiskLevels(currentWriteRiskLevels);
                }
            })
            .catch(error => {
                showErrorAlert(error.message || '加载写入风险级别失败，请刷新页面重试。');
            });
    }

    function renderWriteRiskLevels(levels) {
        const container = $('#write-risk-levels');
        container.empty();
        
        if (!levels || levels.length === 0) {
            container.html('<span class="risk-badge risk-badge-unknown">未配置</span>');
            return;
        }
        
        levels.forEach(level => {
            let badgeClass = 'risk-badge-unknown';
            if (level === 'High') {
                badgeClass = 'risk-badge-high';
            } else if (level === 'Medium') {
                badgeClass = 'risk-badge-medium';
            } else if (level === 'Low') {
                badgeClass = 'risk-badge-low';
            }
            
            container.append(`<span class="risk-badge ${badgeClass}">${riskLevelMap[level] || level}</span>`);
        });
    }

    function openWriteLevelsModal() {
        if (!currentWriteRiskLevels) {
            fetchWriteRiskLevels();
            return;
        }
        
        $('#write-high, #write-medium, #write-low').prop('checked', false);
        
        if (currentWriteRiskLevels.includes('High')) {
            $('#write-high').prop('checked', true);
        }
        if (currentWriteRiskLevels.includes('Medium')) {
            $('#write-medium').prop('checked', true);
        }
        if (currentWriteRiskLevels.includes('Low')) {
            $('#write-low').prop('checked', true);
        }
        
        $('#write-levels-modal').removeClass('hidden');
    }

    function closeWriteLevelsModal() {
        $('#write-levels-modal').addClass('hidden');
    }

    function saveWriteRiskLevels() {
        const levels = [];
        if ($('#write-high').prop('checked')) {
            levels.push('High');
        }
        if ($('#write-medium').prop('checked')) {
            levels.push('Medium');
        }
        if ($('#write-low').prop('checked')) {
            levels.push('Low');
        }
        
        return fetch('/api/write_risk_levels', {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ write_risk_levels: levels }),
        })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(err => {
                        throw new Error(err.error || `HTTP error ${response.status}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                currentWriteRiskLevels = levels;
                renderWriteRiskLevels(levels);
                closeWriteLevelsModal();
            });
    }

    function openDeleteConfirmModal(serverId) {
        currentServerToDelete = serverId;
        $('#confirm-delete-modal').removeClass('hidden');
    }

    function closeDeleteConfirmModal() {
        currentServerToDelete = null;
        $('#confirm-delete-modal').addClass('hidden');
    }

    // --- 服务器配置相关事件绑定 ---
    // 添加服务器按钮
    $('#add-server-btn').on('click', function() {
        openServerModal();
    });
    
    // 服务器表单认证类型切换
    $('#server-auth-type').on('change', function() {
        const authType = $(this).val();
        if (authType === 'password') {
            $('#password-auth-container').show();
            $('#ssh-key-auth-container').hide();
        } else if (authType === 'ssh_key') {
            $('#password-auth-container').hide();
            $('#ssh-key-auth-container').show();
        }
    });
    
    // 服务器表单提交
    $('#server-form').on('submit', function(e) {
        e.preventDefault();
        
        const serverId = $('#server-id').val();
        const formData = {
            name: $('#server-name').val(),
            host: $('#server-host').val(),
            port: parseInt($('#server-port').val(), 10),
            user: $('#server-user').val(),
            auth_type: $('#server-auth-type').val(),
            general_log_path: $('#server-general-log').val(),
            binlog_path: $('#server-binlog').val(),
            enable_general_log: $('#server-enable-general').prop('checked'),
            enable_binlog: $('#server-enable-binlog').prop('checked')
        };
        
        if (formData.auth_type === 'password') {
            formData.password = $('#server-password').val();
        } else if (formData.auth_type === 'ssh_key') {
            formData.ssh_key_path = $('#server-ssh-key').val();
        }
        
        if (serverId) {
            formData.server_id = parseInt(serverId, 10);
        }
        
        saveServer(formData, !serverId)
            .then(data => {
                closeServerModal();
                fetchServers();
            })
            .catch(error => {
                showErrorAlert(error.message || '保存服务器配置失败，请重试。');
            });
    });
    
    // 服务器模态框关闭按钮
    $('#server-modal-close-icon, #server-modal-cancel').on('click', function() {
        closeServerModal();
    });
    
    // 服务器模态框点击背景关闭
    $('#server-modal').on('click', function(event) {
        if (event.target === this) {
            closeServerModal();
        }
    });
    
    // 编辑服务器按钮
    $(document).on('click', '.edit-server-btn', function() {
        const serverId = $(this).data('server-id');
        openServerModal(serverId);
    });
    
    // 删除服务器按钮
    $(document).on('click', '.delete-server-btn', function() {
        const serverId = $(this).data('server-id');
        openDeleteConfirmModal(serverId);
    });
    
    // 确认删除按钮
    $('#confirm-delete-confirm').on('click', function() {
        if (currentServerToDelete) {
            deleteServer(currentServerToDelete)
                .then(data => {
                    closeDeleteConfirmModal();
                    fetchServers();
                })
                .catch(error => {
                    showErrorAlert(error.message || '删除服务器配置失败，请重试。');
                });
        }
    });
    
    // 取消删除按钮
    $('#confirm-delete-cancel').on('click', function() {
        closeDeleteConfirmModal();
    });
    
    // 确认删除模态框点击背景关闭
    $('#confirm-delete-modal').on('click', function(event) {
        if (event.target === this) {
            closeDeleteConfirmModal();
        }
    });
    
    // --- 风险规则相关事件绑定 ---
    // 编辑风险规则按钮
    $('#edit-risk-rules-btn').on('click', function() {
        openRiskRulesModal();
    });
    
    // 风险规则模态框关闭按钮
    $('#risk-rules-modal-close-icon, #risk-rules-modal-cancel').on('click', function() {
        closeRiskRulesModal();
    });
    
    // 风险规则模态框点击背景关闭
    $('#risk-rules-modal').on('click', function(event) {
        if (event.target === this) {
            closeRiskRulesModal();
        }
    });
    
    // 添加风险规则按钮
    $('.add-rule-btn').on('click', function() {
        const level = $(this).data('level');
        const container = $(`#${level.toLowerCase()}-risk-items`);
        addRuleItem(container);
    });
    
    // 规则类型单选按钮切换
    $(document).on('change', '.rule-type-radio', function() {
        const value = $(this).val();
        const $item = $(this).closest('.rule-item');
        
        if (value === 'type') {
            $item.find('.rule-type-input').removeClass('hidden');
            $item.find('.rule-keyword-input').addClass('hidden');
        } else if (value === 'keyword') {
            $item.find('.rule-type-input').addClass('hidden');
            $item.find('.rule-keyword-input').removeClass('hidden');
        }
    });
    
    // 删除规则按钮
    $(document).on('click', '.delete-rule-btn', function() {
        $(this).closest('.rule-item').remove();
    });
    
    // 风险规则表单提交
    $('#risk-rules-form').on('submit', function(e) {
        e.preventDefault();
        
        saveRiskRules()
            .catch(error => {
                showErrorAlert(error.message || '保存风险规则失败，请重试。');
            });
    });
    
    // --- 写入风险级别相关事件绑定 ---
    // 编辑写入级别按钮
    $('#edit-write-levels-btn').on('click', function() {
        openWriteLevelsModal();
    });
    
    // 写入级别模态框关闭按钮
    $('#write-levels-modal-close-icon, #write-levels-modal-cancel').on('click', function() {
        closeWriteLevelsModal();
    });
    
    // 写入级别模态框点击背景关闭
    $('#write-levels-modal').on('click', function(event) {
        if (event.target === this) {
            closeWriteLevelsModal();
        }
    });
    
    // 写入级别表单提交
    $('#write-levels-form').on('submit', function(e) {
        e.preventDefault();
        
        saveWriteRiskLevels()
            .catch(error => {
                showErrorAlert(error.message || '保存写入风险级别失败，请重试。');
            });
    });
    
    // 当切换到配置页面时加载配置数据
    $('nav a[data-tab="config"]').on('click', function() {
        fetchServers();
        fetchRiskRules();
        fetchWriteRiskLevels();
    });

    // --- 数据获取函数 ---
    function fetchData(page = 1) { 
        fetchActivities(page); 
        fetchStats(); 
    }

    // --- 初始化 ---
    // 页面加载完成后自动加载数据
    fetchData(1);
    
    // 初始化窗口大小监听器
    initWindowResizeHandler();
    
    // 绑定报表和导出按钮事件
    bindReportAndExportButtons();
    
    // 添加显示活动标签页的函数
    function showActivitiesTab() {
        handleActivitiesTab();
    }
    
    // 检查当前选中的标签页，确保正确显示
    setTimeout(() => {
        const activeTab = document.querySelector('nav a[data-active="true"]');
        const activeTabId = activeTab ? activeTab.getAttribute('data-tab') : null;
        
        if (activeTabId === 'activities') {
            handleActivitiesTab();
        } else if (activeTabId === 'dashboard') {
            if (!currentStatsData) {
                fetchStats();
            } else {
                setTimeout(() => renderDashboardCharts(), 100);
            }
        }
    }, 200);
    
    // 绑定报表和导出按钮事件
    function bindReportAndExportButtons() {
        document.getElementById('daily-report-btn').addEventListener('click', function() {
            generateReport('daily');
        });
        
        document.getElementById('weekly-report-btn').addEventListener('click', function() {
            generateReport('weekly');
        });
        
        document.getElementById('monthly-report-btn').addEventListener('click', function() {
            generateReport('monthly');
        });
        
        document.getElementById('export-csv-btn').addEventListener('click', function() {
            exportData('csv');
        });
        
        document.getElementById('export-excel-btn').addEventListener('click', function() {
            exportData('excel');
        });
    }
    
    // 生成报表函数，使用当前筛选条件
    function generateReport(type) {
        const filters = getFilters();
        
        const params = new URLSearchParams();
        if (filters.serverId) params.append('server_id', filters.serverId);
        if (filters.startDate) params.append('start_date', filters.startDate);
        if (filters.endDate) params.append('end_date', filters.endDate);
        if (filters.operationType) params.append('operation_type', filters.operationType);
        if (filters.riskLevel) params.append('risk_level', filters.riskLevel);
        if (filters.userName) params.append('user_name', filters.userName);
        
        const url = `/mysql_audit/api/reports/${type}?${params.toString()}`;
        
        fetch(url)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`请求失败：${response.status} ${response.statusText}`);
                }
                
                const contentType = response.headers.get('content-type');
                if (contentType && contentType.includes('application/json')) {
                    return response.json();
                } else {
                    return response.text().then(text => {
                        throw new Error(`服务器返回非JSON格式：${text.substring(0, 100)}...`);
                    });
                }
            })
            .then(data => {
                if (data.error) {
                    showError(`生成${type}报表失败：${data.error}`);
                    return;
                }
                
                // 创建报表预览模态框
                const modal = document.createElement('div');
                modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center z-50';
                modal.innerHTML = `
                    <div class="relative mx-auto p-5 border w-11/12 md:w-3/4 shadow-lg rounded-md bg-white">
                        <div class="mt-3">
                            <div class="flex justify-between items-center mb-4 border-b pb-3">
                                <div class="flex items-center">
                                    <i data-lucide="file-text" class="h-6 w-6 text-indigo-600 mr-2"></i>
                                    <h3 class="text-xl font-semibold text-gray-900">${type === 'daily' ? '日报' : type === 'weekly' ? '周报' : '月报'}</h3>
                                </div>
                                <button class="text-gray-400 hover:text-gray-600 close-modal">
                                    <i data-lucide="x" class="h-5 w-5"></i>
                                </button>
                            </div>
                            <div class="mt-2 px-4 py-3">
                                <!-- 报表周期卡片 -->
                                <div class="mb-6 bg-indigo-50 rounded-lg p-4 border border-indigo-100">
                                    <div class="flex items-center mb-2">
                                        <i data-lucide="calendar" class="h-5 w-5 text-indigo-600 mr-2"></i>
                                        <h6 class="font-semibold text-gray-900">报表周期</h6>
                                    </div>
                                    <div class="grid grid-cols-2 gap-4">
                                        <div class="bg-white rounded p-3 border border-indigo-100">
                                            <div class="text-xs text-gray-500">开始时间</div>
                                            <div class="text-sm font-medium text-gray-700">${new Date(data.data.period.start).toLocaleString()}</div>
                                        </div>
                                        <div class="bg-white rounded p-3 border border-indigo-100">
                                            <div class="text-xs text-gray-500">结束时间</div>
                                            <div class="text-sm font-medium text-gray-700">${new Date(data.data.period.end).toLocaleString()}</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 统计数据网格 -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- 风险等级统计卡片 -->
                                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                        <div class="flex items-center mb-3">
                                            <i data-lucide="shield-alert" class="h-5 w-5 text-gray-600 mr-2"></i>
                                            <h6 class="font-semibold text-gray-900">风险等级统计</h6>
                                        </div>
                                        <div class="space-y-2">
                                            ${Object.entries(data.data.risk_level_summary).map(([level, count]) => `
                                                <div class="flex items-center justify-between bg-white p-2 rounded border border-gray-200">
                                                    <span class="inline-flex items-center">
                                                        <span class="w-2 h-2 rounded-full ${level === 'High' ? 'bg-red-500' : level === 'Medium' ? 'bg-yellow-500' : 'bg-green-500'} mr-2"></span>
                                                        <span class="text-sm text-gray-600">${level === 'High' ? '高危操作' : level === 'Medium' ? '中危操作' : '低危操作'}</span>
                                                    </span>
                                                    <span class="text-sm font-medium ${level === 'High' ? 'text-red-600' : level === 'Medium' ? 'text-yellow-600' : 'text-green-600'}">${count}次</span>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>

                                    <!-- 活跃用户统计卡片 -->
                                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200">
                                        <div class="flex items-center mb-3">
                                            <i data-lucide="users" class="h-5 w-5 text-gray-600 mr-2"></i>
                                            <h6 class="font-semibold text-gray-900">活跃用户统计</h6>
                                        </div>
                                        <div class="space-y-2 max-h-40 overflow-y-auto">
                                            ${Object.entries(data.data.active_users).map(([user, count], index) => `
                                                <div class="flex items-center justify-between bg-white p-2 rounded border border-gray-200">
                                                    <span class="inline-flex items-center">
                                                        <span class="w-5 h-5 rounded-full bg-indigo-100 text-indigo-600 text-xs flex items-center justify-center mr-2">${index + 1}</span>
                                                        <span class="text-sm text-gray-600">${user}</span>
                                                    </span>
                                                    <span class="text-sm font-medium text-indigo-600">${count}次</span>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>

                                    <!-- 操作类型统计卡片 -->
                                    <div class="md:col-span-2 bg-gray-50 rounded-lg p-4 border border-gray-200">
                                        <div class="flex items-center mb-3">
                                            <i data-lucide="activity" class="h-5 w-5 text-gray-600 mr-2"></i>
                                            <h6 class="font-semibold text-gray-900">操作类型统计</h6>
                                        </div>
                                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <!-- 高风险操作 -->
                                            <div class="bg-red-50 rounded-lg p-3 border border-red-100">
                                                <h7 class="text-sm font-medium text-red-700 flex items-center">
                                                    <i data-lucide="alert-triangle" class="h-4 w-4 mr-1"></i>高风险操作
                                                </h7>
                                                <div class="mt-2 space-y-1">
                                                    ${Object.entries(data.data.operation_types.high_risk).map(([type, count]) => `
                                                        <div class="flex justify-between items-center bg-white rounded p-2 text-sm">
                                                            <span class="text-gray-600">${type}</span>
                                                            <span class="font-medium text-red-600">${count}次</span>
                                                        </div>
                                                    `).join('')}
                                                </div>
                                            </div>
                                            <!-- 中风险操作 -->
                                            <div class="bg-yellow-50 rounded-lg p-3 border border-yellow-100">
                                                <h7 class="text-sm font-medium text-yellow-700 flex items-center">
                                                    <i data-lucide="alert-circle" class="h-4 w-4 mr-1"></i>中风险操作
                                                </h7>
                                                <div class="mt-2 space-y-1">
                                                    ${Object.entries(data.data.operation_types.medium_risk).map(([type, count]) => `
                                                        <div class="flex justify-between items-center bg-white rounded p-2 text-sm">
                                                            <span class="text-gray-600">${type}</span>
                                                            <span class="font-medium text-yellow-600">${count}次</span>
                                                        </div>
                                                    `).join('')}
                                                </div>
                                            </div>
                                            <!-- 低风险操作 -->
                                            <div class="bg-green-50 rounded-lg p-3 border border-green-100">
                                                <h7 class="text-sm font-medium text-green-700 flex items-center">
                                                    <i data-lucide="info" class="h-4 w-4 mr-1"></i>低风险操作
                                                </h7>
                                                <div class="mt-2 space-y-1">
                                                    ${Object.entries(data.data.operation_types.low_risk).map(([type, count]) => `
                                                        <div class="flex justify-between items-center bg-white rounded p-2 text-sm">
                                                            <span class="text-gray-600">${type}</span>
                                                            <span class="font-medium text-green-600">${count}次</span>
                                                        </div>
                                                    `).join('')}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-6 px-4 py-3 flex justify-between">
                                <a href="/mysql_audit/api/reports/${type}?${params.toString()}&download=true" class="px-6 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center">
                                    <i data-lucide="download" class="h-4 w-4 mr-2"></i>下载报表
                                </a>
                                <button class="px-6 py-2 bg-gray-600 text-white text-sm font-medium rounded-md shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 close-modal flex items-center">
                                    <i data-lucide="x" class="h-4 w-4 mr-2"></i>关闭
                                </button>
                            </div>
                        </div>
                    </div>
                `;
                
                // 添加到页面
                document.body.appendChild(modal);
                
                // 初始化新的图标
                lucide.createIcons();
                
                // 添加关闭事件
                modal.querySelectorAll('.close-modal').forEach(button => {
                    button.addEventListener('click', () => {
                        document.body.removeChild(modal);
                    });
                });
            })
            .catch(error => {
                showError(`生成报表失败：${error}`);
            });
    }

    // 导出数据函数
    function exportData(format) {
        const filters = getFilters();
        
        const params = new URLSearchParams();
        params.append('format', format);
        
        if (filters.serverId) params.append('server_id', filters.serverId);
        if (filters.startDate) params.append('start_date', filters.startDate);
        if (filters.endDate) params.append('end_date', filters.endDate);
        if (filters.operationType) params.append('operation_type', filters.operationType);
        if (filters.riskLevel) params.append('risk_level', filters.riskLevel);
        if (filters.userName) params.append('user_name', filters.userName);
        
        const url = `/mysql_audit/api/export?${params.toString()}`;
        window.location.href = url;
    }

    // 初始化窗口大小调整处理器
    function initWindowResizeHandler() {
        window.removeEventListener('resize', handleWindowResize);
        window.addEventListener('resize', handleWindowResize);
    }
    
    // 处理窗口大小调整事件
    function handleWindowResize() {
        try {
            if (window.opChart) window.opChart.resize();
            if (window.riskChart) window.riskChart.resize();
            if (window.hourlyChart) window.hourlyChart.resize();
        } catch (e) { }
    }

    // 绑定筛选按钮事件
    document.getElementById('filter-btn').addEventListener('click', function() {
        try {
            const activeTab = document.querySelector('nav a[data-active="true"]');
            const activeTabId = activeTab ? activeTab.getAttribute('data-tab') : null;
            
            testServerSelection();
            clearDashboardCharts();
            fetchData(1);
            
            if (activeTabId === 'dashboard') {
                setTimeout(() => {
                    if (currentStatsData) {
                        renderDashboardCharts();
                    }
                }, 500);
            }
        } catch (e) {
            showError('应用筛选条件时发生错误');
        }
    });
    
    function testServerSelection() {
        const serverSelect = document.getElementById('server-select');
        if (!serverSelect) {
            return;
        }
        
        const selectedOption = serverSelect.options[serverSelect.selectedIndex];
        const selectedValue = serverSelect.value;
        const filters = getFilters();
    }
    
    function getCurrentActiveTab() {
        const activeTab = document.querySelector('nav a[data-active="true"]');
        return activeTab ? activeTab.getAttribute('data-tab') : null;
    }
    
    document.getElementById('pagination').addEventListener('click', function(e) {
        if (e.target.tagName === 'BUTTON') {
            const page = parseInt(e.target.getAttribute('data-page'));
            if (page && page !== currentPage) {
                fetchActivities(page);
            }
        }
    });
    
    document.getElementById('scan-logs-btn').addEventListener('click', function() {
        const scanButton = this;
        const statusDiv = document.getElementById('scan-status');
        const indicatorDiv = document.getElementById('scan-indicator');
        const serverSelect = document.getElementById('server-select');
        const serverId = serverSelect ? serverSelect.value : '';
        const url = '/mysql_audit/api/scan';
        const payload = {};
        let confirmMessage = '确定要扫描所有服务器的日志吗？';
        
        if (serverId) {
            payload.server_id = parseInt(serverId);
            const serverName = serverSelect.options[serverSelect.selectedIndex].text;
            confirmMessage = `确定要扫描服务器 "${serverName}" 的日志吗？`;
        }
        
        if (confirm(confirmMessage)) {
            scanButton.disabled = true;
            scanButton.textContent = '扫描中...';
            if (statusDiv) statusDiv.textContent = '';
            if (indicatorDiv) indicatorDiv.classList.remove('hidden');
            
            fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(payload),
            })
            .then(response => response.json().then(data => ({ status: response.status, body: data })))
            .then(({ status, body }) => {
                if (status >= 200 && status < 300) {
                    if (body.error) {
                        throw new Error(body.error);
                    }
                    
                    const message = body.message || body.msg || "扫描任务已提交";
                    
                    if (statusDiv) {
                        statusDiv.textContent = message.includes("开始扫描") ? "扫描任务已提交" : "扫描完成";
                        statusDiv.classList.remove('text-red-500', 'text-gray-400');
                        statusDiv.classList.add('text-green-500');
                    }
                    
                    fetchData(1);
                } else {
                    throw new Error(body.error || `请求失败，状态码: ${status}`);
                }
            })
            .catch(error => {
                if (statusDiv) {
                    statusDiv.textContent = `扫描失败: ${error.message}`;
                    statusDiv.classList.remove('text-green-500', 'text-gray-400');
                    statusDiv.classList.add('text-red-500');
                }
            })
            .finally(() => {
                scanButton.disabled = false;
                scanButton.textContent = '扫描日志';
                
                if (indicatorDiv) {
                    indicatorDiv.classList.add('hidden');
                }
                
                setTimeout(() => {
                    if (statusDiv) {
                        statusDiv.textContent = '';
                        statusDiv.classList.remove('text-green-500', 'text-red-500');
                    }
                }, 8000);
            });
        } else if (statusDiv) {
            statusDiv.textContent = '';
        }
    });
    
    lucide.createIcons();

    function bindActivityDetailsHandlers() {
        const detailButtons = document.querySelectorAll('.show-details-btn');
        detailButtons.forEach(button => {
            button.removeEventListener('click', handleDetailsButtonClick);
            button.addEventListener('click', handleDetailsButtonClick);
        });
        
        const closeButtons = document.querySelectorAll('#details-modal-close, #details-modal-close-icon');
        closeButtons.forEach(button => {
            button.removeEventListener('click', closeDetailsModal);
            button.addEventListener('click', closeDetailsModal);
        });
        
        const modal = document.getElementById('details-modal');
        if (modal) {
            modal.removeEventListener('click', handleModalBackgroundClick);
            modal.addEventListener('click', handleModalBackgroundClick);
        }
    }
    
    function handleDetailsButtonClick(event) {
        const details = event.currentTarget.getAttribute('data-details');
        if (details) {
            const modalContent = document.getElementById('details-modal-content');
            if (modalContent) {
                modalContent.textContent = details;
            }
            
            const modal = document.getElementById('details-modal');
            if (modal) {
                modal.classList.remove('hidden');
            }
        }
    }
    
    function closeDetailsModal() {
        const modal = document.getElementById('details-modal');
        if (modal) {
            modal.classList.add('hidden');
        }
    }
    
    function handleModalBackgroundClick(event) {
        if (event.target === event.currentTarget) {
            closeDetailsModal();
        }
    }
    
    function handleActivitiesTab() {
        updateTabState('activities', '操作记录');
        fetchActivities(1);
        
        setTimeout(() => {
            lucide.createIcons();
        }, 50);
    }

    function updateTabState(tabId, pageTitle) {
        const navLinks = document.querySelectorAll('nav a[data-tab]');
        navLinks.forEach(link => {
            link.classList.remove('bg-gray-700', 'text-white');
            link.classList.add('text-gray-300', 'hover:bg-gray-700', 'hover:text-white');
            link.setAttribute('data-active', 'false');
        });
        
        const activeLink = document.querySelector(`nav a[data-tab="${tabId}"]`);
        if (activeLink) {
            activeLink.classList.remove('text-gray-300', 'hover:bg-gray-700', 'hover:text-white');
            activeLink.classList.add('bg-gray-700', 'text-white');
            activeLink.setAttribute('data-active', 'true');
        }
        
        const tabContents = document.querySelectorAll('.tab-content');
        tabContents.forEach(content => {
            content.classList.add('hidden');
            content.style.display = '';
        });
        
        const targetContent = document.getElementById(`${tabId}-content`);
        if (targetContent) {
            targetContent.classList.remove('hidden');
            if (tabId === 'activities') {
                targetContent.style.display = 'block';
            }
        }
        
        const mainContentTitle = document.getElementById('main-content-title');
        if (mainContentTitle && pageTitle) {
            mainContentTitle.textContent = pageTitle;
        }
    }

    function renderPagination(totalItems) {
        const totalPages = Math.ceil(totalItems / limit);
        const paginationContainer = $('#pagination');
        paginationContainer.empty();
        
        if (totalPages <= 1) return;
        
        let paginationHtml = '<div class="flex justify-center items-center space-x-2 mt-4">';
        paginationHtml += `<button class="px-3 py-1 border rounded text-sm ${currentPage === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'}" data-page="${currentPage - 1}" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>`;
        paginationHtml += `<span class="text-sm text-gray-700">第 ${currentPage} / ${totalPages} 页</span>`;
        paginationHtml += `<button class="px-3 py-1 border rounded text-sm ${currentPage === totalPages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50'}" data-page="${currentPage + 1}" ${currentPage === totalPages ? 'disabled' : ''}>下一页</button>`;
        paginationHtml += '</div>';
        
        paginationContainer.html(paginationHtml);
    }

    // --- 事件绑定 ---
    document.querySelectorAll('nav a[data-tab]').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            const tabId = this.getAttribute('data-tab');
            const pageTitle = this.textContent.trim();
            
            if (tabId === 'activities') {
                handleActivitiesTab();
                return;
            }
            
            updateTabState(tabId, pageTitle);
            
            if (tabId === 'dashboard') {
                if (!currentStatsData) {
                    fetchStats();
                } else {
                    setTimeout(() => renderDashboardCharts(), 100);
                }
            } else if (tabId === 'config') {
                fetchServers();
                fetchRiskRules();
                fetchWriteRiskLevels();
            }
            
            lucide.createIcons();
        });
    });
});