#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试导入脚本
验证所有模块的导入是否正常工作
"""

import sys
import os

# 添加项目根目录到 Python 路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(current_dir))
sys.path.insert(0, project_root)

def test_imports():
    """测试所有关键模块的导入"""
    print("🔍 测试模块导入...")
    
    tests = []
    
    # 测试配置模块
    try:
        from src.utils.db_config import DB_HOST, DB_PORT, DB_USER, DB_PASSWORD
        tests.append(("✅", "src.utils.db_config", "成功"))
    except Exception as e:
        tests.append(("❌", "src.utils.db_config", f"失败: {str(e)}"))
    
    # 测试配置验证模块
    try:
        from src.utils.validate_config import main
        tests.append(("✅", "src.utils.validate_config", "成功"))
    except Exception as e:
        tests.append(("❌", "src.utils.validate_config", f"失败: {str(e)}"))
    
    # 测试数据库助手模块
    try:
        from src.utils.database_helpers import get_db_connection
        tests.append(("✅", "src.utils.database_helpers", "成功"))
    except Exception as e:
        tests.append(("❌", "src.utils.database_helpers", f"失败: {str(e)}"))
    
    # 测试MySQL审计配置
    try:
        from src.mysql_audit.config import DB_CONFIG, APP_CONFIG
        tests.append(("✅", "src.mysql_audit.config", "成功"))
    except Exception as e:
        tests.append(("❌", "src.mysql_audit.config", f"失败: {str(e)}"))
    
    # 测试Ansible配置
    try:
        from src.ansible_work.config import Config
        tests.append(("✅", "src.ansible_work.config", "成功"))
    except Exception as e:
        tests.append(("❌", "src.ansible_work.config", f"失败: {str(e)}"))
    
    # 输出测试结果
    print("\n📋 导入测试结果:")
    print("-" * 60)
    for status, module, result in tests:
        print(f"{status} {module:<30} {result}")
    
    # 统计结果
    success_count = sum(1 for test in tests if test[0] == "✅")
    total_count = len(tests)
    
    print("-" * 60)
    print(f"总计: {success_count}/{total_count} 个模块导入成功")
    
    if success_count == total_count:
        print("\n🎉 所有模块导入测试通过！")
        return True
    else:
        print(f"\n⚠️  有 {total_count - success_count} 个模块导入失败")
        return False

def test_config_loading():
    """测试配置加载"""
    print("\n🔧 测试配置加载...")

    # 计算项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(current_dir))

    # 检查 .env 文件
    env_path = os.path.join(project_root, '.env')
    if not os.path.exists(env_path):
        print("❌ .env 文件不存在")
        print("💡 请运行: cp .env.example .env")
        return False
    
    # 加载 .env 文件
    try:
        with open(env_path, 'r', encoding='utf-8') as f:
            env_vars = {}
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    env_vars[key.strip()] = value.strip()
                    os.environ[key.strip()] = value.strip()
        
        print(f"✅ .env 文件加载成功，共 {len(env_vars)} 个配置项")
        
        # 检查必需的配置项
        required_vars = ['DB_HOST', 'DB_PORT', 'DB_USER', 'DB_PASSWORD', 'SECRET_KEY']
        missing_vars = [var for var in required_vars if var not in env_vars]
        
        if missing_vars:
            print(f"❌ 缺少必需配置: {', '.join(missing_vars)}")
            return False
        else:
            print("✅ 所有必需配置项都存在")
            return True
            
    except Exception as e:
        print(f"❌ 加载 .env 文件失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🧪 模块导入和配置测试")
    print("=" * 60)
    
    # 测试配置加载
    config_ok = test_config_loading()
    
    # 测试模块导入
    import_ok = test_imports()
    
    # 总结
    print("\n" + "=" * 60)
    if config_ok and import_ok:
        print("🎉 所有测试通过！系统可以正常启动")
        print("\n下一步:")
        print("1. 运行配置验证: python validate_config.py")
        print("2. 启动应用: python start.py")
        return True
    else:
        print("❌ 测试失败，请修复上述问题")
        print("\n建议:")
        if not config_ok:
            print("- 检查 .env 文件配置")
        if not import_ok:
            print("- 检查模块导入路径")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
