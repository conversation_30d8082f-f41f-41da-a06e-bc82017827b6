<!-- 确认操作模态框 -->
<div class="modal fade" id="confirmActionModal" tabindex="-1" aria-labelledby="confirmActionTitle" aria-modal="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-question-circle me-2"></i><span id="confirmActionTitle">确认操作</span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <p id="confirmActionMessage">是否确认执行此操作？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmActionBtn">确认</button>
            </div>
        </div>
    </div>
</div>

<!-- 密码输入模态框 -->
<div class="modal fade" id="passwordPromptModal" tabindex="-1" aria-labelledby="passwordPromptTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-shield-lock me-2"></i><span id="passwordPromptTitle">输入密码</span></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <p id="passwordPromptMessage">请输入服务器密码：</p>
                <div class="mb-3">
                    <input type="password" class="form-control" id="serverPassword" placeholder="请输入密码">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="passwordPromptBtn">确认</button>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title"><i class="bi bi-exclamation-triangle me-2"></i><span id="deleteConfirmTitle">确认删除</span></h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <p id="deleteConfirmMessage">此操作不可撤销，是否确认删除？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="deleteConfirmBtn">删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加服务器分组模态框 -->
<div class="modal fade" id="add-group-modal" tabindex="-1" aria-labelledby="addGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addGroupModalLabel"><i class="bi bi-folder-plus me-2"></i>添加服务器分组</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="add-group-form">
                    <div class="mb-3">
                        <label for="group-name" class="form-label">分组名称</label>
                        <input type="text" class="form-control" id="group-name" required>
                    </div>
                    <div class="mb-3">
                        <label for="group-description" class="form-label">分组描述</label>
                        <textarea class="form-control" id="group-description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addGroup()">添加</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加服务器模态框 -->
<div class="modal fade" id="addServerModal" tabindex="-1" aria-labelledby="addServerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addServerModalLabel"><i class="bi bi-hdd-network me-2"></i>添加服务器</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <form id="add-server-form" onsubmit="event.preventDefault(); addServer();">
                    <div class="mb-3">
                        <label for="serverName" class="form-label">服务器名称</label>
                        <input type="text" class="form-control" id="serverName" placeholder="请输入服务器名称">
                    </div>
                    <div class="mb-3">
                        <label for="serverIP" class="form-label">IP地址</label>
                        <input type="text" class="form-control" id="serverIP" placeholder="请输入IP地址">
                    </div>
                    <div class="mb-3">
                        <label for="serverGroup" class="form-label">所属分组</label>
                        <select class="form-select" id="serverGroup">
                            <!-- 加载中提示 -->
                            <option value="">加载中...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="serverUsername" class="form-label">SSH用户名</label>
                        <input type="text" class="form-control" id="serverUsername" value="root">
                    </div>
                    <div class="mb-3">
                        <label for="serverPassword" class="form-label">SSH密码</label>
                        <input type="password" class="form-control" id="serverPassword" placeholder="请输入密码" autocomplete="new-password">
                    </div>
                    <div class="mb-3">
                        <label for="serverPort" class="form-label">SSH端口</label>
                        <input type="number" class="form-control" id="serverPort" value="22" min="1" max="65535">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addServer()">
                    <i class="bi bi-plus-circle me-1"></i>添加
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 添加服务器组模态框 -->
<div class="modal fade" id="addServerGroupModal" tabindex="-1" aria-labelledby="addServerGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addServerGroupModalLabel"><i class="bi bi-collection me-2"></i>添加服务器组</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <form id="add-server-group-form" onsubmit="event.preventDefault(); addServerGroup();">
                    <div class="mb-3">
                        <label for="groupName" class="form-label">组名称</label>
                        <input type="text" class="form-control" id="groupName" placeholder="请输入组名称">
                    </div>
                    <div class="mb-3">
                        <label for="groupDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="groupDescription" rows="3" placeholder="请输入组描述"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addServerGroup()">
                    <i class="bi bi-plus-circle me-1"></i>添加
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑服务器模态框 -->
<div class="modal fade" id="edit-server-modal" tabindex="-1" aria-labelledby="editServerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editServerModalLabel"><i class="bi bi-pencil-square me-2"></i>编辑服务器</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="edit-server-form">
                    <input type="hidden" id="edit-server-id">
                    <div class="mb-3">
                        <label for="edit-server-name" class="form-label">服务器名称</label>
                        <input type="text" class="form-control" id="edit-server-name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit-server-ip" class="form-label">IP地址</label>
                        <input type="text" class="form-control" id="edit-server-ip" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit-server-port" class="form-label">SSH端口</label>
                        <input type="number" class="form-control" id="edit-server-port" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit-server-username" class="form-label">用户名</label>
                        <input type="text" class="form-control" id="edit-server-username" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit-server-password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="edit-server-password">
                        <div class="form-text">如果使用密钥认证，可以不填写密码。留空表示不修改密码。</div>
                    </div>
                    <div class="mb-3">
                        <label for="edit-server-group" class="form-label">所属分组</label>
                        <select class="form-select" id="edit-server-group" required>
                            <option value="">请选择分组</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateServer()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加任务模态框 -->
<div class="modal fade" id="add-task-modal" tabindex="-1" aria-labelledby="addTaskModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addTaskModalLabel"><i class="bi bi-plus-circle me-2"></i>创建任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="add-task-form">
                    <div class="mb-3">
                        <label for="task-name" class="form-label">任务名称</label>
                        <input type="text" class="form-control" id="task-name" required>
                    </div>
                    <div class="mb-3">
                        <label for="task-type" class="form-label">任务类型</label>
                        <select class="form-select" id="task-type" required>
                            <option value="">请选择任务类型</option>
                            <option value="adhoc">Ad-hoc命令</option>
                            <option value="playbook">Playbook</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="task-target" class="form-label">目标服务器</label>
                        <select class="form-select" id="task-target" required>
                            <option value="">请选择目标服务器</option>
                        </select>
                    </div>
                    <div id="adhoc-options" class="d-none">
                        <div class="mb-3">
                            <label for="task-command" class="form-label">命令内容</label>
                            <textarea class="form-control" id="task-command" rows="5" placeholder="例如：ls -la /var/log"></textarea>
                        </div>
                    </div>
                    <div id="playbook-options" class="d-none">
                        <div class="mb-3">
                            <label for="task-playbook" class="form-label">选择Playbook</label>
                            <select class="form-select" id="task-playbook">
                                <option value="">请选择Playbook</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addTask()">创建</button>
            </div>
        </div>
    </div>
</div>

<!-- 任务结果模态框 -->
<div class="modal fade" id="taskResultModal" tabindex="-1" aria-labelledby="taskResultTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg task-result-modal">
        <div class="modal-content task-result-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskResultTitle"><i class="bi bi-info-circle me-2"></i>任务结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="taskResultContent">
                <div class="text-center p-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-3">正在加载任务结果...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 编辑Playbook模态框 -->
<div class="modal fade" id="edit-playbook-modal" tabindex="-1" aria-labelledby="editPlaybookModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editPlaybookModalLabel"><i class="bi bi-pencil-square me-2"></i>编辑Playbook</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="edit-playbook-form">
                    <input type="hidden" id="edit-playbook-id">
                    <div class="mb-3">
                        <label for="edit-playbook-name" class="form-label">Playbook名称</label>
                        <input type="text" class="form-control" id="edit-playbook-name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit-playbook-content" class="form-label">Playbook内容</label>
                        <textarea class="form-control" id="edit-playbook-content" rows="15" required></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="edit-playbook-description" class="form-label">描述</label>
                        <textarea class="form-control" id="edit-playbook-description" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updatePlaybook()">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 创建Playbook模态框 -->
<div class="modal fade" id="createPlaybookModal" tabindex="-1" aria-labelledby="createPlaybookModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createPlaybookModalLabel"><i class="bi bi-file-earmark-code me-2"></i>创建Playbook</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <form id="create-playbook-form" onsubmit="event.preventDefault(); savePlaybook();">
                    <div class="mb-3">
                        <label for="playbookName" class="form-label">Playbook名称</label>
                        <input type="text" class="form-control" id="playbookName" placeholder="请输入Playbook名称">
                    </div>
                    <div class="mb-3">
                        <label for="playbookContent" class="form-label">Playbook内容</label>
                        <textarea class="form-control" id="playbookContent" rows="10" placeholder="Playbook内容"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="playbookDescription" class="form-label">描述</label>
                        <textarea class="form-control" id="playbookDescription" rows="3" placeholder="请输入Playbook描述"></textarea>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="useDefaultTemplate" checked>
                        <label class="form-check-label" for="useDefaultTemplate">
                            使用默认模板 <span class="text-muted">(推荐)</span>
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="savePlaybook()">
                    <i class="bi bi-plus-circle me-1"></i>创建
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Playbook内容查看模态框 -->
<div class="modal fade" id="playbookContentModal" tabindex="-1" aria-labelledby="playbookContentTitle" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="playbookContentTitle"><i class="bi bi-file-earmark-code me-2"></i>Playbook内容</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <textarea class="form-control code-editor" id="viewPlaybookContent" rows="20" readonly></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 执行结果模态框 -->
<div class="modal fade" id="execution-result-modal" tabindex="-1" aria-labelledby="executionResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="executionResultModalLabel"><i class="bi bi-info-circle me-2"></i>执行结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center p-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-3">正在加载执行结果...</p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 文件管理认证模态框 -->
<div class="modal fade" id="file-manager-auth-modal" tabindex="-1" aria-labelledby="fileManagerAuthModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="fileManagerAuthModalLabel"><i class="bi bi-shield-lock me-2"></i>文件管理认证</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <p>请输入7.26服务器的密码以访问文件管理功能：</p>
                <form id="file-manager-auth-form" onsubmit="event.preventDefault(); authenticateFileManager();">
                    <div class="mb-3">
                        <label for="file-manager-password" class="form-label">密码</label>
                        <input type="password" class="form-control" id="file-manager-password" 
                               placeholder="请输入密码" autocomplete="current-password">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="file-manager-auth-btn">
                    <i class="bi bi-shield-check me-1"></i>验证
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 上传文件模态框 -->
<div class="modal fade" id="uploadFileModal" tabindex="-1" role="dialog" aria-labelledby="uploadFileModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="uploadFileModalLabel"><i class="bi bi-upload me-2"></i>上传文件</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 错误提示区域 -->
                <div id="uploadErrorAlert" class="alert alert-danger d-none" role="alert">
                    <i class="bi bi-exclamation-circle me-2"></i>
                    <span id="uploadErrorMessage"></span>
                </div>
                
                <form id="uploadFileForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="fileToUpload" class="form-label">选择文件</label>
                        <input type="file" class="form-control" id="fileToUpload" name="file">
                    </div>
                    <input type="hidden" id="uploadPath" name="path" value="/">
                    <div class="form-check mb-3">
                        <input type="checkbox" class="form-check-input" id="overwriteFile" name="overwrite">
                        <label class="form-check-label" for="overwriteFile">覆盖已有文件</label>
                    </div>
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        文件将上传到当前目录。如果目标位置已存在同名文件，请勾选"覆盖已有文件"选项。
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="uploadFileButton" onclick="uploadFile()">
                    <i class="bi bi-upload me-1"></i>上传
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 加载中遮罩 -->
<div id="loading-overlay" class="d-none">
    <div class="loading-content">
        <div class="spinner-border text-light" role="status">
            <span class="visually-hidden">加载中...</span>
        </div>
        <p id="loading-message" class="mt-3 text-light">正在处理，请稍候...</p>
    </div>
</div>

<!-- Toast通知容器 -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 11">
    <div id="toastNotification" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <strong class="me-auto" id="toastTitle">通知</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastMessage">
            操作成功完成
        </div>
    </div>
</div>

<!-- Toast通知容器 -->
<div class="toast-container position-fixed bottom-0 end-0 p-3"></div>

<!-- 默认Playbook模板 -->
<script type="text/template" id="default-playbook-template">
- name: 新建Playbook
  hosts: all  # 这里使用all作为默认值，实际执行时会被任务中指定的目标主机覆盖
  gather_facts: yes
  
  tasks:
    - name: 示例任务
      debug:
        msg: "这是一个示例Playbook"
        
    - name: 检查系统信息
      debug:
        msg: "操作系统: {{ '{' }}{'{'}} ansible_distribution {{ '}}' }}{{ '}}' }} {{ '{' }}{'{'}} ansible_distribution_version {{ '}}' }}{{ '}}' }}"
</script>

<!-- 编辑任务模态框 -->
<div class="modal fade" id="edit-task-modal" tabindex="-1" aria-labelledby="editTaskModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editTaskModalLabel"><i class="bi bi-pencil-square me-2"></i>编辑任务</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="edit-task-form">
                    <input type="hidden" id="edit-task-id">
                    <div class="mb-3">
                        <label for="edit-task-name" class="form-label">任务名称</label>
                        <input type="text" class="form-control" id="edit-task-name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit-task-type" class="form-label">任务类型</label>
                        <select class="form-select" id="edit-task-type" required disabled>
                            <option value="adhoc">Ad-hoc命令</option>
                            <option value="playbook">Playbook</option>
                        </select>
                        <small class="form-text text-muted">任务类型创建后不可更改</small>
                    </div>
                    <div class="mb-3" id="edit-adhoc-options">
                        <label for="edit-task-command" class="form-label">命令内容</label>
                        <textarea class="form-control" id="edit-task-command" rows="5"></textarea>
                    </div>
                    <div class="mb-3" id="edit-playbook-options">
                        <label for="edit-task-playbook" class="form-label">选择Playbook</label>
                        <select class="form-select" id="edit-task-playbook">
                            <option value="">请选择Playbook</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit-target-type" class="form-label">目标类型</label>
                        <select class="form-select" id="edit-target-type" required>
                            <option value="">请选择目标类型</option>
                            <option value="server">单台服务器</option>
                            <option value="group">服务器组</option>
                        </select>
                    </div>
                    <div class="mb-3" id="edit-server-target-container">
                        <label for="edit-task-target" class="form-label">目标服务器</label>
                        <select class="form-select" id="edit-task-target">
                            <option value="">请选择目标服务器</option>
                        </select>
                    </div>
                    <div class="mb-3 d-none" id="edit-group-target-container">
                        <label for="edit-task-group" class="form-label">目标服务器组</label>
                        <select class="form-select" id="edit-task-group">
                            <option value="">请选择服务器组</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="updateTask()">保存</button>
            </div>
        </div>
    </div>
</div> 