<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统API管理 - Excel数据管理与MySQL审计及Ansible中台系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .api-card {
            transition: transform 0.2s, box-shadow 0.2s;
            border-left: 4px solid #007bff;
        }
        .api-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .api-item {
            border-left: 3px solid #28a745;
            background: #f8f9fa;
            transition: all 0.2s;
        }
        .api-item:hover {
            background: #e9ecef;
            border-left-color: #007bff;
        }
        .method-badge {
            font-size: 0.75rem;
            font-weight: bold;
        }
        .method-get { background-color: #28a745; }
        .method-post { background-color: #007bff; }
        .method-put { background-color: #ffc107; color: #000; }
        .method-delete { background-color: #dc3545; }
        .type-page { background-color: #6f42c1; }
        .type-api { background-color: #20c997; }
        .search-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }
        .category-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border-radius: 10px 10px 0 0;
        }
        .stats-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border-radius: 15px;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/">
                <i class="bi bi-gear-fill me-2"></i>系统API管理
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/">
                    <i class="bi bi-house-fill me-1"></i>返回首页
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 页面标题和搜索 -->
        <div class="search-box">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="bi bi-api me-2"></i>系统API管理中心
                    </h1>
                    <p class="mb-0">统一管理和访问系统所有API接口，方便维护和调试</p>
                </div>
                <div class="col-md-4">
                    <div class="input-group">
                        <input type="text" class="form-control" id="searchInput" 
                               placeholder="搜索API...">
                        <button class="btn btn-light" type="button" onclick="searchAPIs()">
                            <i class="bi bi-search"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="row mb-4" id="statsRow">
            <div class="col-md-3">
                <div class="stats-card p-3 text-center">
                    <h3 class="mb-1" id="totalCategories">{{ apis|length }}</h3>
                    <small>功能模块</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card p-3 text-center">
                    <h3 class="mb-1" id="totalAPIs">
                        {% set total = 0 %}
                        {% for category, data in apis.items() %}
                            {% set total = total + data.apis|length %}
                        {% endfor %}
                        {{ total }}
                    </h3>
                    <small>API接口</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card p-3 text-center">
                    <h3 class="mb-1" id="pageAPIs">
                        {% set pages = 0 %}
                        {% for category, data in apis.items() %}
                            {% for api in data.apis %}
                                {% if api.type == 'page' %}
                                    {% set pages = pages + 1 %}
                                {% endif %}
                            {% endfor %}
                        {% endfor %}
                        {{ pages }}
                    </h3>
                    <small>页面接口</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card p-3 text-center">
                    <h3 class="mb-1" id="dataAPIs">
                        {% set data_apis = 0 %}
                        {% for category, data in apis.items() %}
                            {% for api in data.apis %}
                                {% if api.type == 'api' %}
                                    {% set data_apis = data_apis + 1 %}
                                {% endif %}
                            {% endfor %}
                        {% endfor %}
                        {{ data_apis }}
                    </h3>
                    <small>数据接口</small>
                </div>
            </div>
        </div>

        <!-- API列表 -->
        <div id="apiContainer">
            {% for category, data in apis.items() %}
            <div class="api-card card mb-4" data-category="{{ category }}">
                <div class="category-header card-header">
                    <h4 class="mb-0">
                        <i class="bi bi-folder-fill me-2"></i>{{ category }}
                        <span class="badge bg-light text-dark ms-2">{{ data.apis|length }} 个接口</span>
                    </h4>
                    <small>{{ data.description }}</small>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for api in data.apis %}
                        <div class="col-md-6 mb-3">
                            <div class="api-item p-3 rounded">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <h6 class="mb-1">{{ api.name }}</h6>
                                    <div>
                                        <span class="badge method-badge method-{{ api.method.split('/')[0].lower() }} me-1">
                                            {{ api.method }}
                                        </span>
                                        <span class="badge type-{{ api.type }}">
                                            {{ '页面' if api.type == 'page' else 'API' }}
                                        </span>
                                    </div>
                                </div>
                                <p class="text-muted small mb-2">{{ api.description }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <code class="small">{{ api.url }}</code>
                                    <div>
                                        <a href="{{ api.url }}" class="btn btn-sm btn-outline-primary me-1" 
                                           target="_blank" title="访问接口">
                                            <i class="bi bi-box-arrow-up-right"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-secondary" 
                                                onclick="copyToClipboard('{{ api.url }}')" title="复制链接">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 无结果提示 -->
        <div id="noResults" class="text-center py-5" style="display: none;">
            <i class="bi bi-search text-muted" style="font-size: 3rem;"></i>
            <h4 class="text-muted mt-3">未找到匹配的API</h4>
            <p class="text-muted">请尝试其他搜索关键词</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 搜索功能
        function searchAPIs() {
            const query = document.getElementById('searchInput').value;
            if (!query.trim()) {
                location.reload();
                return;
            }

            fetch(`/api-management/api/search?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        updateAPIDisplay(data.data);
                    }
                })
                .catch(error => {
                    console.error('搜索失败:', error);
                });
        }

        // 更新API显示
        function updateAPIDisplay(apis) {
            const container = document.getElementById('apiContainer');
            const noResults = document.getElementById('noResults');
            
            if (Object.keys(apis).length === 0) {
                container.style.display = 'none';
                noResults.style.display = 'block';
                return;
            }

            container.style.display = 'block';
            noResults.style.display = 'none';

            // 隐藏所有分类
            document.querySelectorAll('.api-card').forEach(card => {
                card.style.display = 'none';
            });

            // 显示匹配的分类
            Object.keys(apis).forEach(category => {
                const card = document.querySelector(`[data-category="${category}"]`);
                if (card) {
                    card.style.display = 'block';
                }
            });
        }

        // 复制到剪贴板
        function copyToClipboard(text) {
            const fullUrl = window.location.origin + text;
            navigator.clipboard.writeText(fullUrl).then(() => {
                // 显示提示
                const toast = document.createElement('div');
                toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
                toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
                toast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            链接已复制到剪贴板
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                    </div>
                `;
                document.body.appendChild(toast);
                const bsToast = new bootstrap.Toast(toast);
                bsToast.show();
                
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 3000);
            });
        }

        // 回车搜索
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchAPIs();
            }
        });
    </script>
</body>
</html>
