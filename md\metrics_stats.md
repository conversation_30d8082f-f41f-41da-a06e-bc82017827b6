# 指标统计模块说明文档

## 1. 概述

指标统计页面旨在展示各个区县在不同数据治理维度上的表现。页面允许用户选择不同的统计周期，并动态展示所选周期内各区县的指标数据、得分以及与全市、全省最优值的对比。

**核心功能:**

*   **周期选择**：用户可以通过下拉框选择不同的统计周期，包括"当前"、"上一周"、"前两周"等。
*   **动态数据展示**：根据所选周期，页面会重新计算并展示各指标的最新数据（包括区县和部分全市指标）。
*   **指标详情**：展示一级指标、二级指标、区划权重、全省最优值、全市数据。
*   **区县数据对比**：清晰列出各区县在每个二级指标下的具体数值和得分。
*   **最高分高亮**：每个二级指标中得分最高的区县会以特定醒目颜色（例如红色背景）高亮显示。
*   **与上一期指标比较摘要**：在选定周期数据下方，展示当前周期与上一周期的关键指标变化摘要。

## 2. 统计周期定义

统计周期以下列规则定义截止时间 (`cutoff_date`)：

*   **当前 (`current`)**: `cutoff_date` 为用户访问页面的实时时间。
*   **上一周 (`this_monday_11am`)**: `cutoff_date` 为本周一的上午11:00。如果当前时间早于本周一上午11:00，则为上周一上午11:00。
*   **前两周 (`last_monday_11am`)**: `cutoff_date` 为上周一的上午11:00。
*   **前三周 (`two_mondays_ago_11am`)**: `cutoff_date` 为上上周一的上午11:00。
*   **前四周 (`three_mondays_ago_11am`)**: `cutoff_date` 为上上上周一的上午11:00。

Python后端通过 `src/county_data/metrics_stats_routes.py` 中的 `calculate_cutoff_date` 函数实现此逻辑。

## 3. 指标计算详解

所有指标的原始数据主要来源于 `excel_data_{区县后缀}` 表（主数据库）和 `dsp_catalog.data_catalog` 表（目录数据库）。指标计算由 `src/county_data/metrics_calculator.py` 中的函数负责。

### 3.1 数据治理

#### 3.1.1 数据人均汇聚量

*   **指标说明**: 反映各区县及全市平均每人汇聚的数据记录条数。
*   **区县级计算函数**: `calculate_per_capita_data(region_config, cutoff_date, main_db_engine)`
*   **区县级计算逻辑**:
    1.  获取指定区县的总人口数。
        *   SQL (示例: 秦安县 - qin_an):
            ```sql
            SELECT population 
            FROM country_people_metadata 
            WHERE englinsh_provider = 'qin_an';
            ```
    2.  获取指定区县在 `cutoff_date` 前（含）的总数据汇聚量（`record_count` 之和）。
        *   SQL (示例: 秦安县 - qin_an, 假设 `cutoff_date` = '2025-05-15 11:00:00'):
            ```sql
            SELECT COALESCE(SUM(CAST(record_count AS DECIMAL(20,0))), 0)
            FROM `excel_data_qin_an`  -- 注意表名会根据区县变化
            WHERE provide_time <= '2025-05-15 11:00:00'; 
            ```
    3.  区县数据人均汇聚量 = 区县总数据汇聚量 / 区县总人口数。如果人口为0，则结果为0。
*   **全市级计算逻辑** (在 `metrics_stats_routes.py` 中实现，调用 `metrics_calculator.py` 中的辅助函数):
    1.  获取全市总人口数 (`get_city_total_population` 函数)。
        *   SQL:
            ```sql
            SELECT population 
            FROM country_people_metadata 
            WHERE englinsh_provider = 'tss_sz';
            ```
    2.  获取全市总数据汇聚量 (`get_city_total_record_count` 函数)：累加所有区县 `excel_data_{区县后缀}` 表中，在 `cutoff_date` 前（含）的 `record_count` 之和。
        *   内部对每个区县表执行类似区县级的SUM查询，然后加总。
    3.  全市数据人均汇聚量 = 全市总数据汇聚量 / 全市总人口数。如果全市人口为0，则结果为0。

#### 3.1.2 {year}年度数据汇聚人均增长量

*   **指标说明**: 反映各区县及全市在指定年度内（从年初到 `cutoff_date`），平均每人新增的数据记录条数。`{year}` 会被替换为 `cutoff_date` 所在的年份。
*   **区县级计算函数**: `calculate_yearly_increment_data(region_config, cutoff_date, main_db_engine)`
*   **区县级计算逻辑**:
    1.  获取指定区县的总人口数 (同3.1.1区县级)。
    2.  确定年度起始时间。
    3.  获取指定区县从年度起始到 `cutoff_date` (含) 的数据汇聚增量。
        *   SQL (示例: 秦安县 - qin_an, 假设 `cutoff_date` = '2025-05-15 11:00:00', `year_start_date` = '2025-01-01 00:00:00'):
            ```sql
            SELECT COALESCE(SUM(CAST(record_count AS DECIMAL(20,0))), 0)
            FROM `excel_data_qin_an` -- 注意表名会根据区县变化
            WHERE provide_time >= '2025-01-01 00:00:00' AND provide_time <= '2025-05-15 11:00:00';
            ```
    4.  区县年度数据汇聚人均增长量 = 区县年度数据汇聚增量 / 区县总人口数。如果人口为0，则结果为0。
*   **全市级计算逻辑** (在 `metrics_stats_routes.py` 中实现):
    1.  获取全市总人口数 (同3.1.1全市级)。
    2.  获取全市年度数据汇聚增量 (`get_city_yearly_increment_record_count` 函数)：累加所有区县 `excel_data_{区县后缀}` 表中，从年度起始到 `cutoff_date` (含) 的 `record_count` 之和。
        *   内部对每个区县表执行类似区县级的SUM查询（带年度时间范围），然后加总。
    3.  全市年度数据汇聚人均增长量 = 全市年度数据汇聚增量 / 全市总人口数。如果全市人口为0，则结果为0。

#### 3.1.3 接口库表资源挂接率

*   **指标说明**: 反映各区县及全市已发布的有效数据目录中，已成功挂接（包含库表或API）的资源占比。
*   **区县级计算函数**: `calculate_connection_rate_data(region_config, cutoff_date, catalog_db_engine)`
*   **区县级计算逻辑**:
    1.  获取指定区县 (`region_code`) 的总有效数据目录数。
        *   SQL (示例: 秦安县 - region_code '620522000000'):
            ```sql
            SELECT COUNT(1) 
            FROM dsp_catalog.data_catalog 
            WHERE `status` = 4 AND is_del = 0 AND region_code = '620522000000'
            AND (update_time IS NULL OR update_time <= :cutoff_date);
            ```
    2.  获取指定区县已挂接资源的总数据目录数。
        *   SQL (示例: 秦安县 - region_code '620522000000'):
            ```sql
            SELECT COUNT(1) 
            FROM dsp_catalog.data_catalog 
            WHERE `status` = 4 AND is_del = 0 AND region_code = '620522000000'
            AND (table_count + api_count > 0)
            AND (update_time IS NULL OR update_time <= :cutoff_date);
            ```
    3.  区县接口库表资源挂接率 = (区县已挂接目录数 / 区县总目录数) * 100。如果总目录数为0，则结果为0。
*   **全市级计算函数**: `calculate_city_connection_rate_data(cutoff_date, catalog_db_engine)`
*   **全市级计算逻辑**:
    1.  获取全市总有效数据目录数 (查询 `dsp_catalog.data_catalog`，不加 `region_code` 条件)。
        *   SQL (假设 `cutoff_date` = '2025-05-15 11:00:00'):
            ```sql
            SELECT COUNT(1) 
            FROM dsp_catalog.data_catalog 
            WHERE `status` = 4 AND is_del = 0
            AND (update_time IS NULL OR update_time <= '2025-05-15 11:00:00');
            ```
    2.  获取全市已挂接资源的总数据目录数 (查询 `dsp_catalog.data_catalog`，不加 `region_code` 条件)。
        *   SQL (假设 `cutoff_date` = '2025-05-15 11:00:00'):
            ```sql
            SELECT COUNT(1) 
            FROM dsp_catalog.data_catalog 
            WHERE `status` = 4 AND is_del = 0
            AND (table_count + api_count > 0)
            AND (update_time IS NULL OR update_time <= '2025-05-15 11:00:00');
            ```
    3.  全市接口库表资源挂接率 = (全市已挂接目录数 / 全市总目录数) * 100。如果总目录数为0，则结果为0。

#### 3.1.4 数据资源申请利用率

*   **指标说明**: 反映各区县最新的数据资源申请是否为"接口"。若为"接口"，则利用率为100%，否则为0%。
*   **区县级计算函数**: `calculate_resource_usage_data(region_config, cutoff_date, main_db_engine)`
*   **区县级计算逻辑**:
    1.  查询 `resource_application_metadata` 表，获取指定区县 (`english_name` 对应 `region_config` 中的 `db_table_suffix`) 在 `cutoff_date` 前（含）最新的一条申请记录的 `whether_apply_interface` 字段值。
        *   SQL (示例: 秦安县 - english_name 'qin_an', `cutoff_date` = '2025-05-15 11:00:00'):
            ```sql
            SELECT whether_apply_interface 
            FROM resource_application_metadata 
            WHERE english_name = 'qin_an' 
            AND application_time <= '2025-05-15 11:00:00'
            ORDER BY application_time DESC 
            LIMIT 1;
            ```
    2.  如果 `whether_apply_interface` 值为 1，则利用率为 100.0。
    3.  否则（包括值为0或无记录的情况），利用率为 0.0。
*   **全市级数据**: 此指标的"全市"数据**仍然从 `metrics_config.py` 中的 `city_wide_value` 字段获取**，不进行实时计算。

## 4. 得分计算

每个二级指标的得分计算方式如下：

*   **对于百分比类指标 (如挂接率、利用率)**:
    `得分 = (指标值 / 100.0) * 权重`
*   **对于数值类指标 (如人均汇聚量、人均增长量)**:
    1.  首先，计算所有区县在该指标下的最大值 (`max_value_for_normalization`)。如果所有区县值都为0或None，则`max_value_for_normalization`为1（避免除零错误）。
    2.  `得分 = (指标值 / max_value_for_normalization) * 权重`

权重值 (`weight`) 的获取逻辑如下：
1. 首先尝试从数据库表 `indicator_period_weights_metadata` 中获取指定周期和指标的权重值。
2. 如果数据库中不存在对应的权重记录，则使用 `src/county_data/metrics_config.py` 文件中 `INDICATORS_CONFIG` 各指标定义的默认权重值。

### 4.1 动态权重管理

系统支持通过数据库动态管理指标权重，具体实现如下：

* **权重存储表**: `indicator_period_weights_metadata`
  * `id`: 主键
  * `indicator_id`: 指标ID，对应 `INDICATORS_CONFIG` 中的 `id`
  * `period_id`: 统计周期ID，对应 `PERIOD_OPTIONS` 中的 `id`
  * `weight`: 权重值，默认为1.0
  * `created_at`: 记录创建时间
  * `updated_at`: 记录更新时间

* **权重获取逻辑**:
  1. 系统在计算得分时，首先根据当前周期ID和指标ID查询 `indicator_period_weights_metadata` 表
  2. 如果找到对应记录，使用该记录中的权重值
  3. 如果未找到记录，则使用 `INDICATORS_CONFIG` 中定义的默认权重值
  4. 如果配置文件中也未定义权重，则使用默认值1.0

* **权重更新机制**:
  1. 管理员可以通过管理界面更新特定周期和指标的权重
  2. 权重更新后，系统会立即使用新的权重值计算得分
  3. 历史数据的得分不会因权重更新而改变，保持数据一致性

## 5. 前端展示

前端模板 `templates/metrics_stats.html` 负责展示数据。

*   **周期选择器 (`id="periodSelector"`)**: 用户选择后，页面会带 `period_id` 参数重新加载。
*   **截止日期显示 (`id="cutoffDateDisplay"`)**: 显示由后端计算好的 `current_cutoff_date_display`。
*   **与上一期比较信息**:
    *   `comparison_text`: 显示当前所选周期与上一周期的文字说明。
    *   `comparison_highlights`: 列表形式展示关键指标在两个周期之间的显著变化。
*   **数据表格**:
    *   表头包含一级指标、二级指标、权重、全省最优、全市，以及各区县的"本期指标"和"得分"列。**备注列已被移除**。
    *   表格内容主要由服务器端通过Jinja2模板渲染。当用户更改统计周期时，整个页面会重新加载并由后端生成新的表格内容。
    *   **得分高亮**: 对于每个二级指标，得分最高的区县的"得分"单元格会应用 `score-rank-1` CSS 类，使其以醒目的红色背景和加粗字体显示。其他排名靠前的分数也可能有不同的高亮样式（如 `score-rank-2`, `score-rank-3`）。

## 6. 配置文件

*   **`src/county_data/metrics_config.py`**:
    *   `INDICATORS_CONFIG`: 定义了所有指标的元数据，包括ID、名称、权重、单位、数据来源类型 (`data_source_type`)等。`data_source_type` 用于映射到 `metrics_calculator.py` 中的具体计算函数。对于"数据人均汇聚量"、"{year}年度数据汇聚人均增长量"、"接口库表资源挂接率"，其 `city_wide_value` 字段已被注释或移除，因为这些指标的全市数据将实时计算。"数据资源申请利用率"的 `city_wide_value` 依然保留并使用。**`remarks` 字段虽然可能仍存在于此配置中，但已不再被后端处理或在前端页面中显示。**注意：配置文件中的 `weight` 字段仅作为默认/回退值使用，实际权重以数据库 `indicator_period_weights_metadata` 表中的记录为准。
    *   `REGIONS_CONFIG`: 定义了所有区县的元数据，包括ID、名称、英文名 (`english_name`)、数据库表后缀 (`db_table_suffix`)、区划代码 (`region_code`)。
    *   `PERIOD_OPTIONS`: 定义了周期选择下拉框的选项及其对应的参数。

## 7. 后端路由

*   **`src/county_data/metrics_stats_routes.py`**:
    *   `@metrics_stats_bp.route('/metrics_stats', methods=['GET'])` (`metrics_stats_page` 函数):
        1.  根据请求参数 `period_id` 计算当前周期的 `cutoff_date`。
        2.  调用 `get_previous_period_details` 函数确定上一周期的ID和名称，并计算其 `cutoff_date`。
        3.  获取数据库连接。
        4.  分别获取当前周期和上一周期的全市级别基础数据（如总人口、总记录数、年度增量等）。
        5.  分别计算每个指标在每个区县于当前周期和上一周期的具体数值。
        6.  调用 `generate_comparison_highlights` 函数生成指标变化摘要，对比两个周期的数据。
        7.  根据指标类型，确定其"全市"值是实时计算还是从配置读取（仅针对当前周期）。
        8.  根据当前周期的计算值和权重计算各区县得分。
        9.  格式化指标名称（处理年份占位符）。
        10. 准备传递给模板的数据，包括 `indicators` (包含所有处理好的当前周期指标和区县数据)、`regions_for_header`、`periods`、`current_period_id`、`current_period_display_name`、`current_cutoff_date_display`，以及用于比较的 `comparison_text` 和 `comparison_highlights`。**`remarks` 数据不再传递给模板。**
        11. 渲染 `metrics_stats.html` 模板。
    *   `@metrics_stats_bp.route('/api/metrics_stats/data', methods=['GET'])` (`get_metrics_stats_data` 函数): 与 `metrics_stats_page` 类似地获取和处理当前选定周期的数据，用于API输出。**`remarks` 数据不再包含在API响应中。**API暂未包含与上一期比较的数据。

## 8. 注意事项与未来扩展

*   **数据库连接**: 确保 `src/utils/database_helpers.py` 和 `src/county_data/table_connection_rate.py` 中的数据库连接配置正确。
*   **错误处理与日志**: 代码中已加入基本的错误处理和日志记录，方便问题排查。应定期检查日志，确保系统稳定运行。
*   **指标计算方式变更**:
    *   如果仅修改SQL逻辑或简单计算，可以直接修改 `