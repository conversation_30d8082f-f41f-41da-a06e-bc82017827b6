"""
性能监控工具
提供系统性能分析、数据库查询优化建议、内存使用监控等功能
"""

import time
import functools
import psutil
import threading
from typing import Dict, List, Any, Optional, Callable
from datetime import datetime, timedelta
from collections import defaultdict, deque
from dataclasses import dataclass
import logging
from sqlalchemy import text
from contextlib import contextmanager

logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """性能指标数据类"""
    name: str
    duration: float
    timestamp: datetime
    memory_usage: float
    cpu_usage: float
    context: Dict[str, Any] = None

@dataclass
class QueryMetric:
    """数据库查询性能指标"""
    query: str
    duration: float
    timestamp: datetime
    rows_affected: int = 0
    table_name: str = ""
    operation_type: str = ""

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, max_metrics: int = 1000):
        self.max_metrics = max_metrics
        self.metrics: deque = deque(maxlen=max_metrics)
        self.query_metrics: deque = deque(maxlen=max_metrics)
        self.slow_queries: List[QueryMetric] = []
        self.function_stats: Dict[str, Dict] = defaultdict(lambda: {
            'count': 0, 'total_time': 0, 'avg_time': 0, 'max_time': 0, 'min_time': float('inf')
        })
        self._lock = threading.Lock()
        self.slow_query_threshold = 1.0  # 1秒
        
    def record_metric(self, metric: PerformanceMetric):
        """记录性能指标"""
        with self._lock:
            self.metrics.append(metric)
            
    def record_query(self, query_metric: QueryMetric):
        """记录数据库查询指标"""
        with self._lock:
            self.query_metrics.append(query_metric)
            
            # 记录慢查询
            if query_metric.duration > self.slow_query_threshold:
                self.slow_queries.append(query_metric)
                logger.warning(f"慢查询检测: {query_metric.duration:.2f}s - {query_metric.query[:100]}...")
    
    def update_function_stats(self, func_name: str, duration: float):
        """更新函数统计信息"""
        with self._lock:
            stats = self.function_stats[func_name]
            stats['count'] += 1
            stats['total_time'] += duration
            stats['avg_time'] = stats['total_time'] / stats['count']
            stats['max_time'] = max(stats['max_time'], duration)
            stats['min_time'] = min(stats['min_time'], duration)
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统性能指标"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_usage': cpu_percent,
                'memory_usage': memory.percent,
                'memory_available': memory.available / (1024**3),  # GB
                'disk_usage': disk.percent,
                'disk_free': disk.free / (1024**3),  # GB
                'timestamp': datetime.now()
            }
        except Exception as e:
            logger.error(f"获取系统指标失败: {str(e)}")
            return {}
    
    def get_slow_queries(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取慢查询列表"""
        with self._lock:
            sorted_queries = sorted(self.slow_queries, key=lambda x: x.duration, reverse=True)
            return [
                {
                    'query': q.query[:200] + '...' if len(q.query) > 200 else q.query,
                    'duration': q.duration,
                    'timestamp': q.timestamp.isoformat(),
                    'table_name': q.table_name,
                    'operation_type': q.operation_type
                }
                for q in sorted_queries[:limit]
            ]
    
    def get_function_performance(self, limit: int = 20) -> List[Dict[str, Any]]:
        """获取函数性能统计"""
        with self._lock:
            sorted_functions = sorted(
                self.function_stats.items(),
                key=lambda x: x[1]['avg_time'],
                reverse=True
            )
            
            return [
                {
                    'function_name': func_name,
                    'call_count': stats['count'],
                    'avg_time': round(stats['avg_time'], 4),
                    'max_time': round(stats['max_time'], 4),
                    'min_time': round(stats['min_time'], 4),
                    'total_time': round(stats['total_time'], 4)
                }
                for func_name, stats in sorted_functions[:limit]
            ]
    
    def get_recent_metrics(self, minutes: int = 30) -> List[Dict[str, Any]]:
        """获取最近的性能指标"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        with self._lock:
            recent_metrics = [
                {
                    'name': m.name,
                    'duration': m.duration,
                    'timestamp': m.timestamp.isoformat(),
                    'memory_usage': m.memory_usage,
                    'cpu_usage': m.cpu_usage,
                    'context': m.context
                }
                for m in self.metrics
                if m.timestamp >= cutoff_time
            ]
            
        return recent_metrics
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        system_metrics = self.get_system_metrics()
        slow_queries = self.get_slow_queries()
        function_performance = self.get_function_performance()
        recent_metrics = self.get_recent_metrics()
        
        return {
            'report_time': datetime.now().isoformat(),
            'system_metrics': system_metrics,
            'slow_queries': slow_queries,
            'function_performance': function_performance,
            'recent_metrics_count': len(recent_metrics),
            'total_queries': len(self.query_metrics),
            'slow_queries_count': len(self.slow_queries),
            'recommendations': self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 检查慢查询
        if len(self.slow_queries) > 10:
            recommendations.append("检测到大量慢查询，建议优化数据库索引和查询语句")
        
        # 检查系统资源
        system_metrics = self.get_system_metrics()
        if system_metrics.get('memory_usage', 0) > 80:
            recommendations.append("内存使用率过高，建议优化内存使用或增加内存")
        
        if system_metrics.get('cpu_usage', 0) > 80:
            recommendations.append("CPU使用率过高，建议优化计算密集型操作")
        
        # 检查函数性能
        slow_functions = [
            func for func, stats in self.function_stats.items()
            if stats['avg_time'] > 0.5
        ]
        if slow_functions:
            recommendations.append(f"发现慢函数: {', '.join(slow_functions[:3])}，建议优化")
        
        return recommendations

# 全局性能监控器实例
performance_monitor = PerformanceMonitor()

def monitor_performance(name: str = None, include_system_metrics: bool = False):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            func_name = name or f"{func.__module__}.{func.__name__}"
            start_time = time.time()
            
            # 获取初始系统指标
            initial_memory = 0
            initial_cpu = 0
            if include_system_metrics:
                try:
                    process = psutil.Process()
                    initial_memory = process.memory_info().rss / (1024**2)  # MB
                    initial_cpu = process.cpu_percent()
                except:
                    pass
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                
                # 获取最终系统指标
                final_memory = initial_memory
                final_cpu = initial_cpu
                if include_system_metrics:
                    try:
                        process = psutil.Process()
                        final_memory = process.memory_info().rss / (1024**2)  # MB
                        final_cpu = process.cpu_percent()
                    except:
                        pass
                
                # 记录性能指标
                metric = PerformanceMetric(
                    name=func_name,
                    duration=duration,
                    timestamp=datetime.now(),
                    memory_usage=final_memory,
                    cpu_usage=final_cpu,
                    context={
                        'args_count': len(args),
                        'kwargs_count': len(kwargs)
                    }
                )
                
                performance_monitor.record_metric(metric)
                performance_monitor.update_function_stats(func_name, duration)
                
                # 记录慢函数
                if duration > 1.0:
                    logger.warning(f"慢函数检测: {func_name} 耗时 {duration:.2f}s")
        
        return wrapper
    return decorator

@contextmanager
def monitor_query(query: str, table_name: str = "", operation_type: str = ""):
    """数据库查询监控上下文管理器"""
    start_time = time.time()
    rows_affected = 0
    
    try:
        yield
    finally:
        duration = time.time() - start_time
        
        query_metric = QueryMetric(
            query=query,
            duration=duration,
            timestamp=datetime.now(),
            rows_affected=rows_affected,
            table_name=table_name,
            operation_type=operation_type
        )
        
        performance_monitor.record_query(query_metric)
