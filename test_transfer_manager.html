<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件传输管理器测试</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }
        
        .test-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .info-box {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            margin-bottom: 20px;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .feature-list li::before {
            content: '✨';
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fas fa-exchange-alt"></i> 文件传输管理器优化测试</h1>
        
        <div class="info-box">
            <h3>🎯 优化特性</h3>
            <ul class="feature-list">
                <li>现代化渐变背景和毛玻璃效果</li>
                <li>实时传输计数徽章</li>
                <li>文件类型图标识别</li>
                <li>动画进度条和状态指示</li>
                <li>优化的速度显示格式</li>
                <li>悬停效果和交互动画</li>
                <li>Emoji状态图标</li>
                <li>响应式设计</li>
            </ul>
        </div>
        
        <div class="test-buttons">
            <button class="test-btn" onclick="testUpload()">
                <i class="fas fa-upload"></i>
                测试上传
            </button>
            <button class="test-btn" onclick="testDownload()">
                <i class="fas fa-download"></i>
                测试下载
            </button>
            <button class="test-btn" onclick="testMultipleTransfers()">
                <i class="fas fa-tasks"></i>
                测试多个传输
            </button>
            <button class="test-btn" onclick="testFailedTransfer()">
                <i class="fas fa-exclamation-triangle"></i>
                测试失败传输
            </button>
            <button class="test-btn" onclick="clearTransfers()">
                <i class="fas fa-trash"></i>
                清空传输
            </button>
            <button class="test-btn" onclick="toggleManager()">
                <i class="fas fa-eye"></i>
                显示/隐藏管理器
            </button>
        </div>
        
        <div class="info-box">
            <h3>📝 使用说明</h3>
            <p>点击上方按钮测试不同的传输场景。传输管理器将出现在页面右下角，展示优化后的界面效果。</p>
        </div>
    </div>

    <!-- 引入传输管理器 -->
    <script src="static/js/ansible/file-transfer-manager.js"></script>
    
    <script>
        // 测试函数
        function testUpload() {
            const file = new File(['测试文件内容'], 'test-document.pdf', { type: 'application/pdf' });
            window.fileTransferManager.addUpload(file, '/test/upload', {
                data: { path: '/uploads/' },
                onComplete: () => console.log('上传完成')
            });
        }
        
        function testDownload() {
            window.fileTransferManager.addDownload('/test/download', 'sample-video.mp4', {
                onComplete: () => console.log('下载完成')
            });
        }
        
        function testMultipleTransfers() {
            // 模拟多个不同类型的文件传输
            const files = [
                { name: 'presentation.pptx', type: 'upload' },
                { name: 'database.sql', type: 'download' },
                { name: 'image-gallery.zip', type: 'download' },
                { name: 'source-code.js', type: 'upload' }
            ];
            
            files.forEach((fileInfo, index) => {
                setTimeout(() => {
                    if (fileInfo.type === 'upload') {
                        const file = new File(['内容'], fileInfo.name);
                        window.fileTransferManager.addUpload(file, '/test/upload');
                    } else {
                        window.fileTransferManager.addDownload('/test/download', fileInfo.name);
                    }
                }, index * 500);
            });
        }
        
        function testFailedTransfer() {
            const transfer = {
                id: Date.now(),
                type: 'upload',
                file: 'failed-upload.txt',
                status: 'error',
                progress: 45,
                error: '网络连接超时'
            };
            
            window.fileTransferManager.transfers.push(transfer);
            window.fileTransferManager.renderTransfer(transfer);
        }
        
        function clearTransfers() {
            window.fileTransferManager.transfers = [];
            window.fileTransferManager.updateTransferList();
        }
        
        function toggleManager() {
            const container = document.querySelector('#file-transfer-container');
            if (container) {
                if (container.style.display === 'none') {
                    window.fileTransferManager.showTransferManager();
                } else {
                    container.style.display = 'none';
                }
            }
        }
        
        // 页面加载完成后显示传输管理器
        window.addEventListener('load', () => {
            setTimeout(() => {
                window.fileTransferManager.showTransferManager();
            }, 1000);
        });
    </script>
</body>
</html>
