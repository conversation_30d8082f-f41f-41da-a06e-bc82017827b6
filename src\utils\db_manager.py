#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
统一数据库连接管理器
整合所有数据库连接逻辑，提供统一的连接管理接口
"""

import os
import time
import threading
from contextlib import contextmanager
from sqlalchemy import create_engine, text
from sqlalchemy.pool import QueuePool
import pymysql
import logging
from typing import Optional, Dict, Any

# 导入配置
try:
    from .db_config import MAIN_DB_URI, DB_SCHEMA_MAIN
except ImportError:
    MAIN_DB_URI = None
    DB_SCHEMA_MAIN = None

logger = logging.getLogger(__name__)

class DatabaseManager:
    """统一数据库连接管理器"""
    
    _engines = {}
    _connections_cache = {}
    _lock = threading.Lock()
    
    # 数据库配置
    DB_CONFIGS = {
        'main': {
            'uri_env': 'MAIN_DB_URI',
            'host_env': 'DB_HOST',
            'port_env': 'DB_PORT', 
            'user_env': 'DB_USER',
            'password_env': 'DB_PASSWORD',
            'database_env': 'DB_NAME_MAIN',
            'schema_env': 'DB_SCHEMA_MAIN'
        },
        'mysql_audit': {
            'host_env': 'DB_HOST',
            'port_env': 'DB_PORT',
            'user_env': 'DB_USER', 
            'password_env': 'DB_PASSWORD',
            'database_env': 'DB_NAME_MYSQL_LOG'
        },
        'ansible': {
            'host_env': 'DB_HOST',
            'port_env': 'DB_PORT',
            'user_env': 'DB_USER',
            'password_env': 'DB_PASSWORD', 
            'database_env': 'DB_NAME_ANSIBLE'
        },
        'catalog': {
            'host_env': 'CATALOG_DB_HOST',
            'port_env': 'CATALOG_DB_PORT',
            'user_env': 'CATALOG_DB_USER',
            'password_env': 'CATALOG_DB_PASSWORD',
            'database_env': 'CATALOG_DB_NAME'
        }
    }
    
    @classmethod
    def _get_db_config(cls, db_name: str) -> Optional[Dict[str, Any]]:
        """获取数据库配置"""
        if db_name not in cls.DB_CONFIGS:
            logger.error(f"未知的数据库名称: {db_name}")
            return None
            
        config_template = cls.DB_CONFIGS[db_name]
        config = {}
        
        # 检查是否有预定义的URI
        if 'uri_env' in config_template:
            uri = os.environ.get(config_template['uri_env'])
            if uri:
                return {'uri': uri}
        
        # 构建配置
        for key, env_var in config_template.items():
            if key.endswith('_env'):
                config_key = key.replace('_env', '')
                value = os.environ.get(env_var)
                if value:
                    if config_key in ['port']:
                        config[config_key] = int(value)
                    else:
                        config[config_key] = value
        
        # 验证必需的配置
        required_keys = ['host', 'port', 'user', 'password', 'database']
        missing_keys = [key for key in required_keys if key not in config]
        
        if missing_keys:
            logger.error(f"数据库 {db_name} 缺少必需的配置: {missing_keys}")
            return None
            
        return config
    
    @classmethod
    def _create_engine(cls, db_name: str, **kwargs) -> Optional[object]:
        """创建数据库引擎"""
        config = cls._get_db_config(db_name)
        if not config:
            return None
            
        try:
            if 'uri' in config:
                # 使用预定义的URI
                connection_url = config['uri']
            else:
                # 构建连接URL
                connection_url = (
                    f"mysql+pymysql://{config['user']}:{config['password']}"
                    f"@{config['host']}:{config['port']}/{config['database']}"
                )
            
            # 默认连接池配置
            engine_kwargs = {
                'poolclass': QueuePool,
                'pool_size': kwargs.get('pool_size', 10),
                'max_overflow': kwargs.get('max_overflow', 20),
                'pool_timeout': kwargs.get('pool_timeout', 30),
                'pool_recycle': kwargs.get('pool_recycle', 1800),
                'echo': kwargs.get('echo', False)
            }
            
            engine = create_engine(connection_url, **engine_kwargs)
            
            # 测试连接
            with engine.connect() as conn:
                conn.execute(text("SELECT 1"))
                
            logger.info(f"成功创建数据库引擎: {db_name}")
            return engine
            
        except Exception as e:
            logger.error(f"创建数据库引擎失败 {db_name}: {str(e)}")
            return None
    
    @classmethod
    def get_engine(cls, db_name: str, **kwargs) -> Optional[object]:
        """获取数据库引擎（带缓存）"""
        with cls._lock:
            if db_name not in cls._engines:
                cls._engines[db_name] = cls._create_engine(db_name, **kwargs)
            return cls._engines[db_name]
    
    @classmethod
    def get_main_db(cls) -> Optional[object]:
        """获取主数据库引擎"""
        # 优先使用已有的配置
        if MAIN_DB_URI:
            with cls._lock:
                if 'main_legacy' not in cls._engines:
                    try:
                        cls._engines['main_legacy'] = create_engine(
                            MAIN_DB_URI,
                            poolclass=QueuePool,
                            pool_size=10,
                            max_overflow=20,
                            pool_timeout=30,
                            pool_recycle=1800
                        )
                    except Exception as e:
                        logger.error(f"使用legacy配置创建主数据库引擎失败: {str(e)}")
                        return None
                return cls._engines['main_legacy']
        
        # 使用新的配置方式
        return cls.get_engine('main')
    
    @classmethod
    def get_mysql_audit_db(cls) -> Optional[object]:
        """获取MySQL审计数据库引擎"""
        return cls.get_engine('mysql_audit')
    
    @classmethod
    def get_ansible_db(cls) -> Optional[object]:
        """获取Ansible数据库引擎"""
        return cls.get_engine('ansible')
    
    @classmethod
    def get_catalog_db(cls) -> Optional[object]:
        """获取目录数据库引擎"""
        return cls.get_engine('catalog')
    
    @classmethod
    @contextmanager
    def get_connection(cls, db_name: str):
        """获取数据库连接的上下文管理器"""
        engine = cls.get_engine(db_name)
        if not engine:
            raise ValueError(f"无法获取数据库引擎: {db_name}")
            
        conn = None
        try:
            conn = engine.connect()
            yield conn
        except Exception as e:
            logger.error(f"数据库连接错误 {db_name}: {str(e)}")
            raise
        finally:
            if conn:
                try:
                    conn.close()
                except Exception as e:
                    logger.error(f"关闭数据库连接时发生错误 {db_name}: {str(e)}")
    
    @classmethod
    @contextmanager
    def get_pymysql_connection(cls, db_name: str):
        """获取PyMySQL连接的上下文管理器（用于兼容旧代码）"""
        config = cls._get_db_config(db_name)
        if not config or 'uri' in config:
            raise ValueError(f"无法获取PyMySQL配置: {db_name}")
            
        conn = None
        try:
            conn = pymysql.connect(
                host=config['host'],
                port=config['port'],
                user=config['user'],
                password=config['password'],
                database=config['database'],
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor
            )
            yield conn
        except Exception as e:
            logger.error(f"PyMySQL连接错误 {db_name}: {str(e)}")
            raise
        finally:
            if conn:
                try:
                    conn.close()
                except Exception as e:
                    logger.error(f"关闭PyMySQL连接时发生错误 {db_name}: {str(e)}")
    
    @classmethod
    def execute_query(cls, db_name: str, query: str, params: Optional[Dict] = None):
        """执行SQL查询"""
        with cls.get_connection(db_name) as conn:
            if isinstance(query, str):
                query = text(query)
                
            if params:
                result = conn.execute(query, params)
            else:
                result = conn.execute(query)
                
            # 获取列名和数据
            columns = result.keys()
            rows = result.fetchall()
            
            return columns, rows
    
    @classmethod
    def close_all_connections(cls):
        """关闭所有数据库连接"""
        with cls._lock:
            for db_name, engine in cls._engines.items():
                try:
                    if engine:
                        engine.dispose()
                        logger.info(f"已关闭数据库连接: {db_name}")
                except Exception as e:
                    logger.error(f"关闭数据库连接时发生错误 {db_name}: {str(e)}")
            
            cls._engines.clear()
            cls._connections_cache.clear()
    
    @classmethod
    def get_connection_status(cls) -> Dict[str, bool]:
        """获取所有数据库连接状态"""
        status = {}
        
        for db_name in cls.DB_CONFIGS.keys():
            try:
                engine = cls.get_engine(db_name)
                if engine:
                    with engine.connect() as conn:
                        conn.execute(text("SELECT 1"))
                    status[db_name] = True
                else:
                    status[db_name] = False
            except Exception as e:
                logger.error(f"检查数据库连接状态失败 {db_name}: {str(e)}")
                status[db_name] = False
                
        return status

# 向后兼容的函数
def get_db_connection():
    """向后兼容：获取主数据库连接"""
    return DatabaseManager.get_main_db()

def get_connection_config():
    """向后兼容：获取连接配置"""
    return DatabaseManager._get_db_config('main')

# 全局数据库管理器实例
db_manager = DatabaseManager()
