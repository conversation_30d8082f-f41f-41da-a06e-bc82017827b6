// 文件管理模块JavaScript功能
let currentPath = '/';  // 当前浏览路径
let selectedFiles = [];  // 已选择的文件
let isAuthenticated = false;  // 认证状态
let isFileManagerActive = false;  // 文件管理模块是否激活

// 页面加载完成后初始化文件管理模块
document.addEventListener('DOMContentLoaded', function() {
    // 初始化文件管理模块
    initFileManager();
    
    // 绑定事件
    bindFileManagerEvents();
    
    // 监听导航链接点击事件
    document.querySelectorAll('.nav-link').forEach(link => {
        link.addEventListener('click', function(event) {
            // 检查是否点击了文件管理链接
            if (this.getAttribute('href') === '#file-manager') {
                isFileManagerActive = true;
                // 如果已经认证过，直接加载文件列表
                if (isAuthenticated) {
                    document.getElementById('file-manager-auth-prompt').classList.add('d-none');
                    document.getElementById('file-browser').classList.remove('d-none');
                    document.getElementById('upload-file-btn').disabled = false;
                    document.getElementById('batch-download-btn').disabled = false;
                    loadFileList(currentPath);
                }
            } else {
                // 离开文件管理模块
                isFileManagerActive = false;
            }
        });
    });
});

// 初始化文件管理模块
function initFileManager() {
    // 检查认证状态
    checkAuthStatus();
    
    // 初始化路径输入框
    const pathInput = document.getElementById('path-input');
    if (pathInput) {
        pathInput.value = currentPath;
    }
}

// 绑定文件管理相关事件
function bindFileManagerEvents() {
    // 使用安全的方式添加事件监听器，先检查元素是否存在
    const addEventSafely = (elementId, eventType, handler) => {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener(eventType, handler);
        }
    };
    
    // 认证按钮点击事件
    addEventSafely('file-manager-auth-btn', 'click', authenticateFileManager);
    
    // 上传文件按钮点击事件
    addEventSafely('upload-file-btn', 'click', showUploadFileModal);
    
    // 批量下载按钮点击事件
    addEventSafely('batch-download-btn', 'click', batchDownloadFiles);
    
    // 路径导航按钮点击事件
    addEventSafely('go-to-path-btn', 'click', navigateToPath);
    
    // 路径输入框回车事件
    addEventSafely('path-input', 'keyup', function(event) {
        if (event.key === 'Enter') {
            navigateToPath();
        }
    });
    
    // 全选/取消全选文件复选框事件
    addEventSafely('select-all-files', 'change', function() {
        const checkboxes = document.querySelectorAll('#file-list input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedFiles();
    });
    
    // 为上传文件模态框中的上传按钮添加事件
    addEventSafely('uploadFileButton', 'click', uploadFile);
}

// 显示文件管理认证表单
function showFileManagerAuthForm() {
    try {
        // 清理可能存在的模态框
        if (typeof cleanupModals === 'function') {
            cleanupModals();
        }
        
        // 安全地获取模态框元素
        const modalElement = document.getElementById('file-manager-auth-modal');
        if (!modalElement) {
            console.error('认证模态框元素未找到');
            return;
        }
        
        // 使用Bootstrap的Modal对象显示模态框
        try {
            const modal = new bootstrap.Modal(modalElement);
            modal.show();
        } catch (error) {
            console.error('显示认证模态框失败:', error);
        }
    } catch (error) {
        console.error('显示文件管理认证表单失败:', error);
    }
}

// 认证文件管理模块
function authenticateFileManager() {
    try {
        const passwordInput = document.getElementById('file-manager-password');
        if (!passwordInput) {
            console.error('密码输入框未找到');
            return;
        }
        
        const password = passwordInput.value;
        
        if (!password) {
            showToast('error', '密码不能为空');
            return;
        }
        
        showLoading('正在验证身份...');
        
        // 发送认证请求
        fetch('/ansible/api/file-manager/authenticate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ password: password })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.success) {
                // 认证成功
                isAuthenticated = true;
                
                // 隐藏认证模态框
                try {
                    const modalElement = document.getElementById('file-manager-auth-modal');
                    if (modalElement) {
                        const modal = bootstrap.Modal.getInstance(modalElement);
                        if (modal) {
                            modal.hide();
                        }
                    }
                } catch (error) {
                    console.error('隐藏认证模态框失败:', error);
                }
                
                // 隐藏认证提示，显示文件浏览器
                const authPrompt = document.getElementById('file-manager-auth-prompt');
                const fileBrowser = document.getElementById('file-browser');
                const uploadBtn = document.getElementById('upload-file-btn');
                const batchDownloadBtn = document.getElementById('batch-download-btn');
                
                if (authPrompt) authPrompt.classList.add('d-none');
                if (fileBrowser) fileBrowser.classList.remove('d-none');
                if (uploadBtn) uploadBtn.disabled = false;
                if (batchDownloadBtn) batchDownloadBtn.disabled = false;
                
                // 加载文件列表
                loadFileList(currentPath);
                
                showToast('success', '认证成功');
            } else {
                // 认证失败
                showToast('error', data.message || '认证失败');
            }
        })
        .catch(error => {
            hideLoading();
            console.error('认证过程发生错误:', error);
            showToast('error', '认证过程发生错误');
        });
    } catch (error) {
        hideLoading();
        console.error('认证过程发生错误:', error);
        showToast('error', '认证过程发生错误');
    }
}

// 检查认证状态
function checkAuthStatus() {
    fetch('/ansible/api/file-manager/check-auth')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.authenticated) {
            // 已认证
            isAuthenticated = true;
            
            // 如果当前在文件管理模块，显示文件浏览器
            if (isFileManagerActive) {
                // 隐藏认证提示，显示文件浏览器
                document.getElementById('file-manager-auth-prompt').classList.add('d-none');
                document.getElementById('file-browser').classList.remove('d-none');
                
                // 启用按钮
                document.getElementById('upload-file-btn').disabled = false;
                document.getElementById('batch-download-btn').disabled = false;
                
                // 加载文件列表
                loadFileList(currentPath);
            }
        }
    })
    .catch(error => {
        console.error('检查认证状态失败:', error);
    });
}

// 加载指定路径的文件列表
function loadFileList(path) {
    if (!isAuthenticated) {
        return;
    }
    
    showLoading('正在加载文件列表...');
    
    fetch(`/ansible/api/file-manager/list?path=${encodeURIComponent(path)}`)
    .then(response => response.json())
    .then(data => {
        hideLoading();
        
        if (data.success) {
            // 更新当前路径
            currentPath = path;
            document.getElementById('current-path').textContent = currentPath;
            document.getElementById('path-input').value = currentPath;
            
            // 渲染文件列表
            renderFileList(data.data);
        } else {
            showToast('error', data.message || '获取文件列表失败');
        }
    })
    .catch(error => {
        hideLoading();
        showToast('error', '获取文件列表请求失败: ' + error.message);
    });
}

/**
 * 绑定文件下载按钮事件
 */
function bindDownloadButtons() {
    // 获取所有下载按钮
    const downloadButtons = document.querySelectorAll('.download-btn');
    
    // 为每个下载按钮绑定事件
    downloadButtons.forEach(btn => {
        btn.addEventListener('click', function(event) {
            event.preventDefault();
            
            // 获取文件行和文件路径
            const row = this.closest('tr');
            const checkbox = row ? row.querySelector('.file-checkbox') : null;
            const filePath = checkbox ? checkbox.getAttribute('data-path') : null;
            
            if (!filePath) {
                console.error('找不到文件路径，按钮:', this);
                showToast('error', '无法获取文件路径');
                return;
            }
            
            console.log('下载按钮点击，文件路径:', filePath);
            downloadFile(filePath);
        });
    });
}

/**
 * 渲染文件列表
 * @param {Array} files 文件数组
 */
function renderFileList(files) {
    const fileListContainer = document.getElementById('file-list');
    if (!fileListContainer) return;

    // 清空现有内容
    fileListContainer.innerHTML = '';

    // 如果目录为空
    if (!files || files.length === 0) {
        const emptyMessage = document.createElement('tr');
        emptyMessage.className = 'empty-dir';
        emptyMessage.innerHTML = '<td colspan="6" class="text-center py-4">此目录为空</td>';
        fileListContainer.appendChild(emptyMessage);
        return;
    }

    // 按类型排序：目录在前，文件在后
    files.sort((a, b) => {
        if (a.is_directory && !b.is_directory) return -1;
        if (!a.is_directory && b.is_directory) return 1;
        return a.name.localeCompare(b.name);
    });

    // 添加文件行
    files.forEach(file => {
        const row = document.createElement('tr');
        row.className = file.is_directory ? 'dir-row' : 'file-row';
        
        // 格式化文件大小
        const sizeStr = file.is_directory ? '-' : formatFileSize(file.size);
        
        // 确保文件路径格式正确
        const filePath = file.path || (currentPath === '/' ? `/${file.name}` : `${currentPath}/${file.name}`);
        
        // 设置行内容
        row.innerHTML = `
            <td>
                <input type="checkbox" class="file-checkbox" data-path="${filePath}" data-is-dir="${file.is_directory}" data-size="${file.size || 0}">
            </td>
            <td>
                <i class="fas ${file.is_directory ? 'fa-folder' : 'fa-file'}"></i>
            </td>
            <td>
                ${file.is_directory ? 
                    `<a href="#" class="open-btn" data-path="${filePath}">${file.name}</a>` : 
                    `<span>${file.name}</span>`
                }
            </td>
            <td>${sizeStr}</td>
            <td>${file.modified_time || '-'}</td>
            <td>
                ${file.is_directory ? 
                    `<button class="btn btn-sm btn-light open-btn" data-path="${filePath}">
                        <i class="fas fa-folder-open"></i> 打开
                    </button>` : 
                    `<button class="btn btn-sm btn-light download-btn" data-path="${filePath}">
                        <i class="fas fa-download"></i> 下载
                    </button>`
                }
            </td>
        `;
        
        fileListContainer.appendChild(row);
    });
    
    // 绑定事件处理程序
    bindOpenFolderEvents();
    bindDownloadButtons();
    
    // 重置选择状态
    const selectAllCheckbox = document.getElementById('select-all-files');
    if (selectAllCheckbox) {
        selectAllCheckbox.checked = false;
    }
    selectedFiles = [];
    updateBatchDownloadButton();
    
    // 打印调试信息
    console.log('文件列表已渲染，当前路径:', currentPath);
}

/**
 * 绑定打开文件夹事件
 */
function bindOpenFolderEvents() {
    // 绑定所有打开按钮事件
    const openButtons = document.querySelectorAll('.open-btn');
    openButtons.forEach(btn => {
        btn.addEventListener('click', function(event) {
            event.preventDefault();
            
            // 获取文件夹路径
            const path = this.getAttribute('data-path');
            if (path) {
                console.log('打开目录:', path);
                navigateToPath(path);
            }
        });
    });
    
    // 绑定复选框选择事件
    const checkboxes = document.querySelectorAll('.file-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectedFiles();
        });
    });
}

// 获取父级路径
function getParentPath(path) {
    if (path === '/' || !path) {
        return '/';
    }
    
    // 移除末尾的斜杠
    if (path.endsWith('/')) {
        path = path.slice(0, -1);
    }
    
    // 获取最后一个斜杠的位置
    const lastSlashIndex = path.lastIndexOf('/');
    
    if (lastSlashIndex <= 0) {
        return '/';
    }
    
    return path.substring(0, lastSlashIndex);
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const units = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    
    return (bytes / Math.pow(1024, i)).toFixed(2) + ' ' + units[i];
}

// 导航到指定路径
function navigateToPath(path) {
    // 如果提供了路径参数，直接使用，否则从输入框获取
    if (path) {
        loadFileList(path);
        // 更新路径输入框
        const pathInput = document.getElementById('path-input');
        if (pathInput) {
            pathInput.value = path;
        }
    } else {
        // 从输入框获取路径
        const pathInput = document.getElementById('path-input');
        if (pathInput) {
            const inputPath = pathInput.value.trim();
            if (inputPath) {
                loadFileList(inputPath);
            }
        }
    }
}

/**
 * 更新已选择的文件列表
 */
function updateSelectedFiles() {
    selectedFiles = [];
    
    // 获取所有选中的复选框
    const checkboxes = document.querySelectorAll('#file-list input[type="checkbox"]:checked');
    console.log('选中的复选框数量:', checkboxes.length);
    
    checkboxes.forEach(checkbox => {
        // 获取文件路径
        const path = checkbox.getAttribute('data-path');
        if (path) {
            // 获取文件的tr行元素
            const row = checkbox.closest('tr');
            if (row) {
                const name = row.querySelector('td:nth-child(3)').textContent.trim();
                const isDir = checkbox.getAttribute('data-is-dir') === 'true';
                const sizeText = row.querySelector('td:nth-child(4)').textContent;
                let size = 0;
                
                // 尝试解析文件大小
                if (sizeText && sizeText !== '-') {
                    const sizeMatch = sizeText.match(/(\d+\.?\d*)\s*(B|KB|MB|GB)/);
                    if (sizeMatch) {
                        const value = parseFloat(sizeMatch[1]);
                        const unit = sizeMatch[2];
                        
                        // 根据单位转换为字节
                        switch (unit) {
                            case 'GB': size = value * 1024 * 1024 * 1024; break;
                            case 'MB': size = value * 1024 * 1024; break;
                            case 'KB': size = value * 1024; break;
                            default: size = value;
                        }
                    }
                }
                
                // 添加到选中文件列表
                selectedFiles.push({
                    path: path,
                    name: name,
                    isDir: isDir,
                    size: size
                });
                
                // 为了调试，记录选中的文件
                console.log('选中文件:', path, name, isDir, size);
            }
        }
    });
    
    updateBatchDownloadButton();
}

// 更新批量下载按钮状态
function updateBatchDownloadButton() {
    const batchDownloadBtn = document.getElementById('batch-download-btn');
    
    // 只有选择了文件（非目录）才能批量下载
    const hasFiles = selectedFiles.some(file => !file.isDir);
    
    batchDownloadBtn.disabled = !hasFiles;
}

// 显示上传文件模态框
function showUploadFileModal() {
    if (!isAuthenticated) {
        showToast('error', '请先完成认证');
        return;
    }
    
    try {
        // 清理可能存在的模态框
        if (typeof cleanupModals === 'function') {
            cleanupModals();
        }
        
        // 设置上传路径
        const pathInput = document.getElementById('uploadPath');
        if (pathInput) {
            pathInput.value = currentPath;
        }
        
        // 显示模态框
        const modalElement = document.getElementById('uploadFileModal');
        if (modalElement) {
            try {
                // 尝试使用Bootstrap原生方法
                const modal = new bootstrap.Modal(modalElement);
                modal.show();
            } catch (error) {
                // 如果失败，尝试使用jQuery
                try {
                    $('#uploadFileModal').modal('show');
                } catch (jqError) {
                    console.error('显示模态框失败:', error, jqError);
                }
            }
        } else {
            console.error('上传文件模态框未找到');
        }
    } catch (error) {
        console.error('显示上传文件模态框失败:', error);
    }
}

// 上传文件
function uploadFile() {
    try {
        const fileInput = document.getElementById('fileToUpload');
        const pathInput = document.getElementById('uploadPath');
        const overwriteCheckbox = document.getElementById('overwriteFile');
        
        if (!fileInput) {
            showToast('error', '找不到文件上传控件');
            return;
        }
        
        if (!fileInput.files || fileInput.files.length === 0) {
            showToast('error', '请选择要上传的文件');
            return;
        }
        
        const fileObj = fileInput.files[0];
        const path = pathInput ? pathInput.value : currentPath;
        const overwrite = overwriteCheckbox ? overwriteCheckbox.checked : false;
        
        // 显示加载中提示
        showToast('info', '正在上传文件，请稍候...');
        
        // 隐藏模态框
        try {
            const modalElement = document.getElementById('uploadFileModal');
            if (modalElement) {
                try {
                    // 尝试使用Bootstrap原生方法
                    const modal = bootstrap.Modal.getInstance(modalElement);
                    if (modal) {
                        modal.hide();
                    }
                } catch (error) {
                    // 如果失败，尝试使用jQuery
                    try {
                        $('#uploadFileModal').modal('hide');
                    } catch (jqError) {
                        console.error('隐藏模态框失败:', error, jqError);
                    }
                }
            }
        } catch (error) {
            console.error('隐藏上传模态框失败:', error);
        }
        
        // 检查是否有文件传输管理器
        if (window.fileTransferManager) {
            // 使用文件传输管理器上传
            window.fileTransferManager.addUpload(
                fileObj, 
                '/ansible/api/file-manager/upload', 
                {
                    data: { 
                        path: path,
                        overwrite: overwrite
                    },
                    fileFieldName: 'file',
                    onComplete: function() {
                        // 上传完成后重新加载文件列表
                        loadFileList(currentPath);
                    }
                }
            );
            
            // 清空文件输入框
            if (fileInput) {
                fileInput.value = '';
            }
        } else {
            // 降级方案：使用普通的fetch方式上传
            const formData = new FormData();
            formData.append('file', fileObj);
            formData.append('path', path);
            formData.append('overwrite', overwrite);
            
            // 发送请求
            fetch('/ansible/api/file-manager/upload', {
                method: 'POST',
                body: formData,
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast('success', data.message);
                    
                    // 重新加载文件列表
                    loadFileList(currentPath);
                    
                    // 清空文件输入框
                    if (fileInput) {
                        fileInput.value = '';
                    }
                } else {
                    showToast('error', data.message);
                }
            })
            .catch(error => {
                showToast('error', '上传文件失败: ' + error.message);
            });
        }
    } catch (error) {
        console.error('上传文件发生错误:', error);
        showToast('error', '上传文件发生错误');
    }
}

/**
 * 下载单个文件
 * @param {string} filePath 文件路径
 */
function downloadFile(filePath) {
    console.log('开始下载文件，路径:', filePath);
    
    if (!isAuthenticated) {
        showFileManagerAuthForm();
        return;
    }
    
    // 检查文件路径是否有效
    if (!filePath) {
        console.error('下载失败：文件路径无效', filePath);
        showToast('error', '无法下载：文件路径无效');
        return;
    }
    
    // 获取文件名
    const fileName = filePath.split('/').pop();
    
    // 显示加载指示器
    showLoader(true, `准备下载 ${fileName}`);
    
    // 如果文件传输管理器可用，使用它来管理下载
    if (window.fileTransferManager) {
        // 创建传输记录
        const transferId = Date.now();
        const transfer = {
            id: transferId,
            type: 'download',
            file: fileName,
            files: [{
                path: filePath,
                name: fileName
            }],
            status: 'preparing',
            progress: 0
        };
        
        // 添加到传输管理器并显示
        window.fileTransferManager.transfers.push(transfer);
        window.fileTransferManager.showTransferManager();
        window.fileTransferManager.updateTransferList();
        window.fileTransferManager.updateTransferUI(transfer);
        
        // 设置下载参数
        const downloadUrl = `/ansible/api/file-manager/download?path=${encodeURIComponent(filePath)}`;
        transfer.url = downloadUrl;
        
        // 通知用户即将下载
        window.fileTransferManager.showNotification('准备下载', `正在准备下载文件: ${fileName}`, false);
        
        // 更新状态为准备中
        transfer.status = 'preparing';
        window.fileTransferManager.updateTransferList();
        window.fileTransferManager.updateTransferUI(transfer);
        
        // 使用fetch获取文件并创建blob
        fetch(downloadUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`下载失败: ${response.status} ${response.statusText}`);
                }
                showLoader(false);
                return response.blob();
            })
            .then(blob => {
                // 创建下载链接
                const url = URL.createObjectURL(blob);
                transfer.blobUrl = url;
                
                // 使用waitForUserSaveAction处理下载
                return window.fileTransferManager.waitForUserSaveAction(transfer, url);
            })
            .then(success => {
                showLoader(false);
                if (!success) {
                    console.error('用户取消了下载或发生错误');
                    // 错误处理逻辑已在waitForUserSaveAction中实现
                }
            })
            .catch(error => {
                console.error('下载文件时出错:', error);
                showLoader(false);
                showToast('error', `下载文件失败: ${error.message}`);
                
                // 更新传输状态为失败
                transfer.status = 'failed';
                transfer.error = error.message;
                window.fileTransferManager.updateTransferList();
                window.fileTransferManager.updateTransferUI(transfer);
            });
    } else {
        // 传统下载方式（无传输管理器可用时的备用方案）
        showToast('info', `开始下载文件: ${fileName}`);
        
        // 直接使用window.location下载（这种方式不会显示进度）
        const downloadUrl = `/ansible/api/file-manager/download?path=${encodeURIComponent(filePath)}`;
        
        // 创建一个隐藏的a标签来下载
        const a = document.createElement('a');
        a.href = downloadUrl;
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        
        // 延迟移除元素
        setTimeout(() => {
            document.body.removeChild(a);
            showLoader(false);
        }, 1000);
    }
}

/**
 * 批量下载选中的文件
 */
function batchDownloadFiles() {
    console.log('开始批量下载，已选择文件数量:', selectedFiles.length);
    
    // 检查是否已选择文件
    if (selectedFiles.length === 0) {
        // 尝试重新获取选中的文件
        updateSelectedFiles();
        
        // 再次检查
        if (selectedFiles.length === 0) {
            showToast('warning', '请先选择要下载的文件');
            return;
        }
    }
    
    // 过滤掉文件夹（如果需要也可以下载文件夹）
    const filesToDownload = selectedFiles.filter(file => !file.isDir);
    if (filesToDownload.length === 0) {
        showToast('warning', '请选择至少一个文件进行下载，文件夹无法直接下载');
        return;
    }

    // 显示加载状态
    showLoader(true, `正在准备批量下载，共 ${filesToDownload.length} 个文件`);
    
    // 如果文件传输管理器可用，使用它来管理下载
    if (window.fileTransferManager) {
        console.log('使用文件传输管理器下载文件:', filesToDownload);
        // 使用文件传输管理器的批量下载功能
        window.fileTransferManager.batchDownloadFiles(filesToDownload);
        
        // 隐藏加载指示器（传输管理器会显示进度）
        hideLoader();
        return;
    }
    
    // 降级方案：如果没有传输管理器，使用简单的下载方式
    // 创建表单数据
    const formData = new FormData();
    filesToDownload.forEach(file => {
        formData.append('paths', file.path);
    });
    
    // 显示提示
    showToast('info', '正在准备下载，请稍候...');
    
    // 使用原生XHR以便能够添加进度事件
    const xhr = new XMLHttpRequest();
    xhr.open('POST', '/ansible/api/file-manager/batch-download', true);
    xhr.responseType = 'blob';
    
    // 下载完成后处理
    xhr.onload = function() {
        hideLoader();
        
        if (xhr.status === 200) {
            const blob = xhr.response;
            // 使用弹出保存对话框
            const url = URL.createObjectURL(blob);
            
            // 文件名：使用与后端一致的tar.gz格式
            const filename = filesToDownload.length > 1 
                ? `批量下载_${new Date().getTime()}.tar.gz` 
                : filesToDownload[0].name;
            
            // 创建下载链接
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            
            // 点击链接，弹出保存对话框
            a.click();
            
            // 清理
            setTimeout(() => {
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            }, 100);
            
            showToast('success', filesToDownload.length > 1 ? '批量下载已完成' : '文件下载已完成');
        } else {
            showToast('error', '下载过程中发生错误');
        }
    };
    
    // 处理错误
    xhr.onerror = function() {
        hideLoader();
        showToast('error', '下载失败：网络错误');
    };
    
    // 发送请求
    xhr.send(formData);
}

/**
 * 显示加载指示器
 * @param {boolean} show 是否显示
 * @param {string} message 加载信息
 */
function showLoader(show, message = '加载中...') {
    // 查找或创建加载指示器
    let loader = document.getElementById('file-manager-loader');
    
    if (!loader) {
        // 创建加载指示器
        loader = document.createElement('div');
        loader.id = 'file-manager-loader';
        loader.className = 'loader-overlay';
        loader.innerHTML = `
            <div class="loader-content">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="loader-message mt-2">${message}</div>
            </div>
        `;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .loader-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
            }
            .loader-content {
                background: white;
                padding: 20px;
                border-radius: 5px;
                box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
                text-align: center;
            }
            .loader-message {
                margin-top: 10px;
                font-size: 14px;
            }
        `;
        document.head.appendChild(style);
        
        // 添加到文档
        document.body.appendChild(loader);
    } else {
        // 更新消息
        const messageEl = loader.querySelector('.loader-message');
        if (messageEl) {
            messageEl.textContent = message;
        }
    }
    
    // 显示或隐藏
    loader.style.display = show ? 'flex' : 'none';
}

/**
 * 隐藏加载指示器
 */
function hideLoader() {
    showLoader(false);
}

/**
 * 显示通知消息
 * @param {string} type 消息类型 (success, error, info, warning)
 * @param {string} message 消息内容
 */
function showToast(type, message) {
    // 避免递归调用导致栈溢出
    if (window._showingToast) {
        console.log(`[${type}] ${message}`);
        return;
    }
    
    window._showingToast = true;
    
    try {
        // 如果存在全局的showToast函数，且不是本函数自身，则调用它
        if (typeof window.globalShowToast === 'function') {
            window.globalShowToast(type, message);
            window._showingToast = false;
            return;
        }
        
        // 否则使用自定义实现
        console.log(`[${type}] ${message}`);
        
        // 检查是否存在通知容器
        let container = document.getElementById('toast-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(container);
        }
        
        // 创建通知元素
        const toastId = 'toast-' + Date.now();
        const toastEl = document.createElement('div');
        toastEl.id = toastId;
        toastEl.className = 'toast';
        toastEl.role = 'alert';
        toastEl.ariaLive = 'assertive';
        toastEl.ariaAtomic = 'true';
        
        // 设置样式
        let bgClass = 'bg-primary';
        switch (type) {
            case 'success': bgClass = 'bg-success'; break;
            case 'error': bgClass = 'bg-danger'; break;
            case 'warning': bgClass = 'bg-warning'; break;
            case 'info': bgClass = 'bg-info'; break;
        }
        
        // 设置内容
        toastEl.innerHTML = `
            <div class="toast-header">
                <div class="rounded me-2 ${bgClass}" style="width: 20px; height: 20px;"></div>
                <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        `;
        
        // 添加到容器
        container.appendChild(toastEl);
        
        // 尝试使用Bootstrap的Toast
        try {
            const toast = new bootstrap.Toast(toastEl, {
                delay: 5000
            });
            toast.show();
        } catch (error) {
            // 如果失败，使用简单的方式显示和移除
            toastEl.style.display = 'block';
            setTimeout(() => {
                if (toastEl.parentElement) {
                    toastEl.parentElement.removeChild(toastEl);
                }
            }, 5000);
        }
    } finally {
        window._showingToast = false;
    }
}
