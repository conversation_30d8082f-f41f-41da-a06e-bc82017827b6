from sqlalchemy import text
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

# --- Helper: Get Population ---
def get_population_for_region(region_db_table_suffix: str, main_db_engine) -> int:
    """
    Fetches population for a given region using its db_table_suffix (e.g., 'qin_an').
    Matches 'englinsh_provider' in 'country_people_metadata'.
    """
    if not main_db_engine:
        logger.error("Main DB engine not provided for population fetch.")
        return 0 # Default to 0 if no engine or data
        
    population_query = text("""
        SELECT population 
        FROM country_people_metadata 
        WHERE englinsh_provider = :suffix
    """)
    try:
        with main_db_engine.connect() as conn:
            result = conn.execute(population_query, {"suffix": region_db_table_suffix}).scalar()
            return int(result) if result else 0
    except Exception as e:
        logger.error(f"Error fetching population for {region_db_table_suffix}: {str(e)}")
        return 0

# --- NEW HELPER: Get City Total Population ---
def get_city_total_population(main_db_engine) -> int:
    """Fetches total city population using 'tss_sz' from 'country_people_metadata'."""
    if not main_db_engine:
        logger.error("Main DB engine not provided for city population fetch.")
        return 0
        
    population_query = text("""
        SELECT population 
        FROM country_people_metadata 
        WHERE englinsh_provider = 'tss_sz'
    """)
    try:
        with main_db_engine.connect() as conn:
            result = conn.execute(population_query).scalar()
            return int(result) if result else 0
    except Exception as e:
        logger.error(f"Error fetching city total population: {str(e)}")
        return 0

# --- 1. Data Per Capita ---
def calculate_per_capita_data(region_config: dict, cutoff_date: datetime, main_db_engine) -> float | None:
    """
    Calculates '数据人均汇聚量' for a specific region up to the cutoff_date.
    """
    if not main_db_engine:
        logger.warning(f"Main DB Engine not provided for per_capita_data for region {region_config.get('name')}")
        return None

    region_suffix = region_config.get("db_table_suffix")
    if not region_suffix:
        logger.warning(f"Missing 'db_table_suffix' in region_config for {region_config.get('name')}")
        return None

    table_name = f"excel_data_{region_suffix}"
    total_population = get_population_for_region(region_suffix, main_db_engine)

    if total_population == 0:
        logger.warning(f"Population is 0 for {region_config.get('name')}, cannot calculate per capita data.")
        return 0.0 # Consistent with how it might have been handled (division by zero avoidance)

    # Query to sum 'record_count' from the region's table up to the cutoff_date
    # The original logic in get_county_yearly_stats used provide_time <= year_end_date.
    # We adapt this to use the precise cutoff_date.
    query_total_records = text(f"""
        SELECT COALESCE(SUM(CAST(record_count AS DECIMAL(20,0))), 0)
        FROM `{table_name}`
        WHERE provide_time <= :cutoff_date
    """)
    
    try:
        with main_db_engine.connect() as conn:
            total_records = conn.execute(query_total_records, {"cutoff_date": cutoff_date}).scalar() or 0
            total_records = float(total_records)
            
            if total_population > 0:
                return round(total_records / total_population, 2)
            else:
                return 0.0
    except Exception as e:
        logger.error(f"Error calculating per_capita_data for {table_name}: {str(e)}")
        return None

# --- NEW: Calculate City Total Record Count (for Per Capita and Yearly Increment) ---
def get_city_total_record_count(cutoff_date: datetime, main_db_engine, regions_config: list) -> float:
    """Calculates total record_count from all region tables up to cutoff_date."""
    total_city_records = 0.0
    if not main_db_engine:
        logger.error("Main DB Engine not provided for get_city_total_record_count")
        return total_city_records

    for region in regions_config:
        region_suffix = region.get("db_table_suffix")
        if not region_suffix:
            logger.warning(f"Missing 'db_table_suffix' in region_config for {region.get('name')} when calculating city total records.")
            continue
        table_name = f"excel_data_{region_suffix}"
        query = text(f"""
            SELECT COALESCE(SUM(CAST(record_count AS DECIMAL(20,0))), 0)
            FROM `{table_name}`
            WHERE provide_time <= :cutoff_date
        """)
        try:
            with main_db_engine.connect() as conn:
                records = conn.execute(query, {"cutoff_date": cutoff_date}).scalar() or 0
                total_city_records += float(records)
        except Exception as e:
            logger.error(f"Error fetching records for {table_name} in get_city_total_record_count: {str(e)}")
    logger.info(f"City total record count up to {cutoff_date}: {total_city_records}")
    return total_city_records

# --- 2. Yearly Increment Per Capita ---
def calculate_yearly_increment_data(region_config: dict, cutoff_date: datetime, main_db_engine) -> float | None:
    """
    Calculates '年度数据汇聚人均增长量' for a specific region for the year of the cutoff_date.
    The indicator name "2025年度数据汇聚人均增长量" implies a specific year.
    This calculation will be for the year of the `cutoff_date`.
    """
    if not main_db_engine:
        logger.warning(f"Main DB Engine not provided for yearly_increment_data for region {region_config.get('name')}")
        return None

    region_suffix = region_config.get("db_table_suffix")
    if not region_suffix:
        logger.warning(f"Missing 'db_table_suffix' in region_config for {region_config.get('name')}")
        return None

    table_name = f"excel_data_{region_suffix}"
    total_population = get_population_for_region(region_suffix, main_db_engine)

    if total_population == 0:
        logger.warning(f"Population is 0 for {region_config.get('name')}, cannot calculate yearly per capita increment.")
        return 0.0

    year_of_cutoff = cutoff_date.year
    year_start_date = datetime(year_of_cutoff, 1, 1, 0, 0, 0)

    query_yearly_increment = text(f"""
        SELECT COALESCE(SUM(CAST(record_count AS DECIMAL(20,0))), 0)
        FROM `{table_name}`
        WHERE provide_time >= :year_start_date AND provide_time <= :cutoff_date
    """)
    
    try:
        with main_db_engine.connect() as conn:
            yearly_increment = conn.execute(query_yearly_increment, 
                                            {"year_start_date": year_start_date, "cutoff_date": cutoff_date}
                                           ).scalar() or 0
            yearly_increment = float(yearly_increment)
            
            if total_population > 0:
                return round(yearly_increment / total_population, 2)
            else:
                return 0.0
    except Exception as e:
        logger.error(f"Error calculating yearly_increment_data for {table_name}: {str(e)}")
        return None

# --- NEW: Calculate City Yearly Increment Record Count ---
def get_city_yearly_increment_record_count(cutoff_date: datetime, main_db_engine, regions_config: list) -> float:
    """Calculates total yearly_increment record_count from all region tables for the year of cutoff_date."""
    total_city_yearly_increment = 0.0
    if not main_db_engine:
        logger.error("Main DB Engine not provided for get_city_yearly_increment_record_count")
        return total_city_yearly_increment

    year_of_cutoff = cutoff_date.year
    year_start_date = datetime(year_of_cutoff, 1, 1, 0, 0, 0)

    for region in regions_config:
        region_suffix = region.get("db_table_suffix")
        if not region_suffix:
            logger.warning(f"Missing 'db_table_suffix' in region_config for {region.get('name')} when calculating city yearly increment.")
            continue
        table_name = f"excel_data_{region_suffix}"
        query = text(f"""
            SELECT COALESCE(SUM(CAST(record_count AS DECIMAL(20,0))), 0)
            FROM `{table_name}`
            WHERE provide_time >= :year_start_date AND provide_time <= :cutoff_date
        """)
        try:
            with main_db_engine.connect() as conn:
                increment = conn.execute(query, {"year_start_date": year_start_date, "cutoff_date": cutoff_date}).scalar() or 0
                total_city_yearly_increment += float(increment)
        except Exception as e:
            logger.error(f"Error fetching yearly increment for {table_name} in get_city_yearly_increment_record_count: {str(e)}")
    logger.info(f"City yearly increment record count for year {year_of_cutoff} up to {cutoff_date}: {total_city_yearly_increment}")
    return total_city_yearly_increment

# --- 3. Connection Rate ---
def calculate_connection_rate_data(region_config: dict, cutoff_date: datetime, catalog_db_engine) -> float | None:
    """
    Calculates '接口库表资源挂接率' for a specific region up to the cutoff_date.
    Logic adapted from /metrics_table_connection_rate in summary_stats_routes.py
    """
    if not catalog_db_engine:
        logger.warning(f"Catalog DB Engine not provided for connection_rate_data for region {region_config.get('name')}")
        return None

    region_code = region_config.get("region_code")
    if not region_code:
        logger.warning(f"Missing 'region_code' in region_config for {region_config.get('name')}")
        return None
    
    county_total_query_str = f"""
        SELECT COUNT(1) 
        FROM dsp_catalog.data_catalog 
        WHERE `status` = 4 AND is_del = 0 AND region_code = :region_code
        AND (update_time IS NULL OR update_time <= :cutoff_date)
    """
    
    county_connected_query_str = f"""
        SELECT COUNT(1) 
        FROM dsp_catalog.data_catalog 
        WHERE `status` = 4 AND is_del = 0 AND region_code = :region_code
        AND (table_count + api_count > 0)
        AND (update_time IS NULL OR update_time <= :cutoff_date)
    """
    try:
        with catalog_db_engine.connect() as conn:
            county_total = conn.execute(text(county_total_query_str), 
                                        {"region_code": region_code, "cutoff_date": cutoff_date}
                                       ).scalar() or 0
            
            county_connected = conn.execute(text(county_connected_query_str), 
                                            {"region_code": region_code, "cutoff_date": cutoff_date}
                                           ).scalar() or 0
            
            if county_total > 0:
                return round((county_connected / county_total) * 100, 2)
            else:
                return 0.0
    except Exception as e:
        logger.error(f"Error calculating connection_rate_data for region {region_code}: {str(e)}")
        return None

# --- NEW: Calculate City Connection Rate ---
def calculate_city_connection_rate_data(cutoff_date: datetime, catalog_db_engine) -> float | None:
    """Calculates '接口库表资源挂接率' for the entire city (no region_code filter)."""
    if not catalog_db_engine:
        logger.warning("Catalog DB Engine not provided for city_connection_rate_data")
        return None

    city_total_query_str = """
        SELECT COUNT(1) 
        FROM dsp_catalog.data_catalog 
        WHERE `status` = 4 AND is_del = 0
        AND (update_time IS NULL OR update_time <= :cutoff_date)
    """
    city_connected_query_str = """
        SELECT COUNT(1) 
        FROM dsp_catalog.data_catalog 
        WHERE `status` = 4 AND is_del = 0
        AND (table_count + api_count > 0)
        AND (update_time IS NULL OR update_time <= :cutoff_date)
    """
    try:
        with catalog_db_engine.connect() as conn:
            city_total = conn.execute(text(city_total_query_str), {"cutoff_date": cutoff_date}).scalar() or 0
            city_connected = conn.execute(text(city_connected_query_str), {"cutoff_date": cutoff_date}).scalar() or 0
            
            if city_total > 0:
                rate = round((city_connected / city_total) * 100, 2)
                logger.info(f"City connection rate: Total catalogs {city_total}, Connected {city_connected}, Rate {rate}%")
                return rate
            else:
                logger.info("City connection rate: No catalogs found, rate is 0.0%")
                return 0.0
    except Exception as e:
        logger.error(f"Error calculating city_connection_rate_data: {str(e)}")
        return None

# --- 4. Resource Usage Rate ---
def calculate_resource_usage_data(region_config: dict, cutoff_date: datetime, main_db_engine) -> float | None:
    """
    Calculates '数据资源申请利用率' for a specific region up to the cutoff_date.
    Logic adapted from get_resource_usage_data in summary_stats_routes.py
    """
    if not main_db_engine:
        logger.warning(f"Main DB Engine not provided for resource_usage_data for region {region_config.get('name')}")
        return None

    region_key_for_resource_table = region_config.get("db_table_suffix") 
    if not region_key_for_resource_table:
         logger.warning(f"Missing 'db_table_suffix' (for resource_application_metadata key) in region_config for {region_config.get('name')}")
         return None
    
    query_str = """
        SELECT whether_apply_interface 
        FROM resource_application_metadata 
        WHERE english_name = :region_key 
        AND application_time <= :cutoff_date
        ORDER BY application_time DESC 
        LIMIT 1
    """
    try:
        with main_db_engine.connect() as conn:
            result = conn.execute(text(query_str), 
                                  {"region_key": region_key_for_resource_table, "cutoff_date": cutoff_date}
                                 ).scalar()
            
            if result == 1:
                return 100.0
            else: # Includes None (no record found) or other values
                return 0.0 
    except Exception as e:
        logger.error(f"Error calculating resource_usage_data for region key {region_key_for_resource_table}: {str(e)}")
        return None

# Dispatcher function
def calculate_metric_value(indicator_config: dict, region_config: dict, cutoff_date: datetime, main_db_engine, catalog_db_engine):
    """
    根据指标类型、区域配置和截止日期计算指标值
    """
    try:
        indicator_type = indicator_config.get("data_source_type")
        region_name = region_config.get("name")
        indicator_name = indicator_config.get("level2_name")
        if "{year}" in indicator_name:
            indicator_name = indicator_name.format(year=cutoff_date.year)
            
        logger.info(f"计算指标: {indicator_name}, 区划: {region_name}, 类型: {indicator_type}, 截止时间: {cutoff_date}")
        
        result = None
        
        if indicator_type == "per_capita_data":
            result = calculate_per_capita_data(region_config, cutoff_date, main_db_engine)
            if result is not None:
                logger.info(f"人均汇聚量计算结果: {result}")
            else:
                logger.warning(f"人均汇聚量计算结果为空")
                result = 0.0  # 确保返回数值而不是None
        elif indicator_type == "yearly_increment_data":
            result = calculate_yearly_increment_data(region_config, cutoff_date, main_db_engine)
            if result is not None:
                logger.info(f"年度增长量计算结果: {result}")
            else:
                logger.warning(f"年度增长量计算结果为空")
                result = 0.0  # 确保返回数值而不是None
        elif indicator_type == "connection_rate_data":
            result = calculate_connection_rate_data(region_config, cutoff_date, catalog_db_engine)
            if result is not None:
                logger.info(f"挂接率计算结果: {result}")
            else:
                logger.warning(f"挂接率计算结果为空")
                result = 0.0  # 确保返回数值而不是None
        elif indicator_type == "resource_usage_data":
            result = calculate_resource_usage_data(region_config, cutoff_date, main_db_engine)
            if result is not None:
                logger.info(f"资源利用率计算结果: {result}")
            else:
                logger.warning(f"资源利用率计算结果为空")
                result = 0.0  # 确保返回数值而不是None
        else:
            logger.warning(f"未知的指标类型: {indicator_type}, 无法计算")
            return 0.0  # 未知类型时返回0.0而不是None
            
        return result if result is not None else 0.0  # 最终确保返回数值
    except Exception as e:
        logger.error(f"计算指标 {indicator_config.get('id')} 失败: {str(e)}")
        return 0.0  # 发生异常时返回0.0而不是None 

# --- NEW FUNCTIONS TO FETCH CITY TOTALS CONSISTENT WITH /data_display ---
def get_display_page_city_total_records(cutoff_date: datetime, main_db_engine, regions_config: list) -> float:
    """
    Fetches the city's total record count consistent with the /data_display page.
    This uses exactly the same logic as the /api/data_display/yearly_total_stats endpoint.
    """
    logger.info(f"Fetching display page city total records (consistent with data_display page) up to {cutoff_date}")
    
    year = cutoff_date.year
    
    # Set the end date to December 31st of the given year
    # This is what /api/data_display/yearly_total_stats uses as cutoff
    end_date = f"{year}-12-31"
    
    tables = get_data_tables(main_db_engine)
    total_records = 0
    
    with main_db_engine.connect() as conn:
        for table in tables:
            try:
                # 获取截止到指定年份末的数据总量
                yearly_total_query = f"""
                SELECT SUM(record_count) as total 
                FROM `{table}` 
                WHERE provide_time <= :end_date
                """
                
                yearly_total_result = conn.execute(text(yearly_total_query), {"end_date": end_date})
                yearly_total = int(next(iter(yearly_total_result), [0])[0] or 0)
                
                total_records += yearly_total
            except Exception as e:
                logger.error(f"获取表 {table} 截止{year}年数据总量出错: {str(e)}")
                continue
    
    # Add the "split table fixed value" which is also part of the data_display.html page logic
    # The JavaScript in data_display.html has this logic:
    # 拆分表固定值调整，不同年份有不同的基数
    current_year = datetime.now().year
    split_table_value = 4955537612  # 当前年份的拆分表基数
    
    # 如果不是当前年份，根据年份差调整拆分表基数 (每年递减30%)
    if year != current_year:
        year_diff = current_year - year
        # 假设每年数据增量为30%
        yearly_growth_rate = 0.3
        split_table_value = split_table_value / pow(1 + yearly_growth_rate, year_diff)
    
    # 更新总数据量（加上拆分表的固定值）
    total_with_split_table = total_records + round(split_table_value)
    
    logger.info(f"City data_display total records calculation for {year}: Raw total={total_records}, Split table value={round(split_table_value)}, Final total={total_with_split_table}")
    return float(total_with_split_table)


def get_display_page_city_yearly_increment(cutoff_date: datetime, main_db_engine, regions_config: list) -> float:
    """
    Fetches the city's yearly increment record count consistent with the /data_display page.
    This uses exactly the same logic as the monthly_stats aggregation in data_display.html.
    """
    year = cutoff_date.year
    logger.info(f"Fetching display page city yearly increment (consistent with data_display page) for year {year}")
    
    # Fetch monthly stats for the year, summing them up (as done in data_display frontend)
    tables = get_data_tables(main_db_engine)
    
    # Initialize monthly data
    monthly_stats = {month: 0 for month in range(1, 13)}
    
    with main_db_engine.connect() as conn:
        for table in tables:
            try:
                # 获取表月度数据
                year_query = f"""
                SELECT 
                    MONTH(provide_time) as month, 
                    SUM(record_count) as total 
                FROM `{table}` 
                WHERE YEAR(provide_time) = :year
                GROUP BY MONTH(provide_time)
                """
                
                year_result = conn.execute(text(year_query), {"year": year})
                
                for row in year_result:
                    month = row[0]  # 月份(1-12)
                    count = row[1] or 0  # 该月数据量
                    
                    if 1 <= month <= 12:  # 确保月份有效
                        monthly_stats[month] += int(count)
                    else:
                        logger.warning(f"表 {table} 中发现无效月份: {month}")
            except Exception as e:
                logger.error(f"处理表 {table} 年度数据时出错: {str(e)}")
                continue
    
    # Sum up all monthly values (as done in data_display.html)
    yearly_total = sum(monthly_stats.values())
    
    logger.info(f"City data_display yearly increment calculation for {year}: Monthly totals={monthly_stats}, Yearly sum={yearly_total}")
    return float(yearly_total)


# Helper function to match what's in data_display.py
def get_data_tables(db_engine) -> list:
    """
    获取所有数据表名（以excel_data_开头且不以metadata结尾的表）
    这个函数复制自data_display.py以保持一致性
    """
    tables_query = """
    SELECT TABLE_NAME 
    FROM information_schema.tables 
    WHERE table_schema = DATABASE() 
    AND table_name LIKE 'excel_data_%'
    AND table_name NOT LIKE '%metadata'
    """
    
    try:
        with db_engine.connect() as conn:
            tables_result = conn.execute(text(tables_query))
            return [row[0] for row in tables_result]
    except Exception as e:
        logger.error(f"获取数据表列表出错: {str(e)}")
        return [] 