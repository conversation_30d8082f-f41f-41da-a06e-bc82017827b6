"""
数据持久化服务类
负责数据的存储、更新、删除和查询操作
"""
from typing import Dict, List, Tuple, Any, Optional
import uuid
from datetime import datetime
import os
import shutil
from sqlalchemy import create_engine, text, inspect

class DataPersistenceService:
    def __init__(self, db_connection_func):
        """
        初始化数据持久化服务
        
        Args:
            db_connection_func: 获取数据库连接的函数
        """
        self.get_db_connection = db_connection_func

    def create_table_if_not_exists(self, table_name: str) -> Tuple[bool, str]:
        """创建数据表（如果不存在）"""
        try:
            engine = self.get_db_connection()
            inspector = inspect(engine)
            
            if not inspector.has_table(table_name):
                # 创建表
                with engine.connect() as conn:
                    conn.execute(text(f"""
                        CREATE TABLE IF NOT EXISTS {table_name} (
                            id VARCHAR(36) PRIMARY KEY,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                        )
                    """))
                    conn.commit()
                return True, "表创建成功"
            return True, "表已存在"
        except Exception as e:
            return False, f"创建表时发生错误: {str(e)}"

    def clear_table(self, table_name: str) -> Tuple[bool, str]:
        """清空表中的所有数据"""
        try:
            engine = self.get_db_connection()
            with engine.connect() as conn:
                conn.execute(text(f"TRUNCATE TABLE {table_name}"))
                conn.commit()
            return True, "表清空成功"
        except Exception as e:
            return False, f"清空表时发生错误: {str(e)}"

    def insert_data(self, table_name: str, data: List[Dict]) -> Tuple[bool, str]:
        """批量插入数据"""
        if not data:
            return True, "没有数据需要插入"
        try:
            engine = self.get_db_connection()
            inspector = inspect(engine)
            existing_columns = [col['name'] for col in inspector.get_columns(table_name)]
            columns = list(data[0].keys())
            placeholders = ', '.join(['%s'] * len(columns))
            columns_str = ', '.join(columns)
            insert_sql = f"INSERT INTO {table_name} ({columns_str}) VALUES ({placeholders})"
            values = []
            for row in data:
                if isinstance(row, dict):
                    values.append(tuple(row.get(col) for col in columns))
                elif isinstance(row, (list, tuple)):
                    values.append(tuple(row))
                else:
                    raise ValueError(f"插入数据格式错误，必须为字典或元组，实际类型: {type(row)}")
            raw_conn = engine.raw_connection()
            try:
                cursor = raw_conn.cursor()
                cursor.executemany(insert_sql, values)
                raw_conn.commit()
                cursor.close()
            finally:
                raw_conn.close()
            return True, f"成功插入 {len(data)} 条记录"
        except Exception as e:
            return False, f"插入数据时发生错误: {str(e)}"

    def backup_database(self) -> Tuple[bool, str]:
        """备份数据库"""
        try:
            # 创建备份目录
            backup_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'db_backups')
            os.makedirs(backup_dir, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = os.path.join(backup_dir, f'backup_{timestamp}.sql')
            
            # 获取数据库连接信息
            engine = self.get_db_connection()
            url = engine.url
            
            # 构建mysqldump命令
            cmd = f"mysqldump -h {url.host} -P {url.port} -u {url.username} -p{url.password} {url.database} > {backup_file}"
            
            # 执行备份
            os.system(cmd)
            
            return True, backup_file
        except Exception as e:
            return False, f"备份数据库时发生错误: {str(e)}"

    def generate_uuid(self) -> str:
        """生成UUID"""
        return str(uuid.uuid4()) 