import pandas as pd
from datetime import datetime, timedelta
from src.mysql_audit.models import UserActivity, db
from sqlalchemy import func, and_

class ReportGenerator:
    @staticmethod
    def generate_summary_report(start_date=None, end_date=None, server_id=None, operation_type=None, risk_level=None, user_name=None):
        """生成指定时间段的汇总报表，支持多条件筛选"""
        # 构建基础查询条件
        query_conditions = []
        
        # 添加时间范围条件
        if start_date:
            query_conditions.append(UserActivity.timestamp >= start_date)
        if end_date:
            query_conditions.append(UserActivity.timestamp <= end_date)
            
        # 添加服务器ID条件
        if server_id:
            query_conditions.append(UserActivity.server_id == server_id)
            
        # 添加操作类型条件
        if operation_type:
            query_conditions.append(UserActivity.operation_type == operation_type)
            
        # 添加风险等级条件
        if risk_level:
            query_conditions.append(UserActivity.risk_level == risk_level)
            
        # 添加用户名条件
        if user_name:
            query_conditions.append(UserActivity.user_name == user_name)
            
        # 构建基础条件
        base_condition = and_(*query_conditions) if query_conditions else True
        
        activities = UserActivity.query.filter(base_condition)

        # 按风险等级统计
        risk_stats = db.session.query(
            UserActivity.risk_level,
            func.count(UserActivity.id)
        ).filter(base_condition).group_by(UserActivity.risk_level).all()

        # 活跃用户统计
        active_users = db.session.query(
            UserActivity.user_name,
            func.count(UserActivity.id)
        ).filter(base_condition).group_by(UserActivity.user_name).all()

        # 按风险等级分类的操作类型统计
        high_risk_ops = db.session.query(
            UserActivity.operation_type,
            func.count(UserActivity.id)
        ).filter(
            and_(
                base_condition,
                UserActivity.risk_level == 'High'
            )
        ).group_by(UserActivity.operation_type).all()

        medium_risk_ops = db.session.query(
            UserActivity.operation_type,
            func.count(UserActivity.id)
        ).filter(
            and_(
                base_condition,
                UserActivity.risk_level == 'Medium'
            )
        ).group_by(UserActivity.operation_type).all()

        low_risk_ops = db.session.query(
            UserActivity.operation_type,
            func.count(UserActivity.id)
        ).filter(
            and_(
                base_condition,
                UserActivity.risk_level == 'Low'
            )
        ).group_by(UserActivity.operation_type).all()

        return {
            'period': {
                'start': start_date or datetime.now() - timedelta(days=30),
                'end': end_date or datetime.now()
            },
            'risk_level_summary': dict(risk_stats),
            'active_users': dict(active_users),
            'operation_types': {
                'high_risk': dict(high_risk_ops),
                'medium_risk': dict(medium_risk_ops),
                'low_risk': dict(low_risk_ops)
            },
            'total_operations': activities.count()
        }

    @staticmethod
    def export_activities(start_date, end_date, risk_levels=None, users=None, operation_types=None):
        """导出操作记录为DataFrame"""
        query = UserActivity.query.filter(
            and_(
                UserActivity.timestamp >= start_date,
                UserActivity.timestamp <= end_date
            )
        )

        if risk_levels:
            query = query.filter(UserActivity.risk_level.in_(risk_levels))
        if users:
            query = query.filter(UserActivity.user_name.in_(users))
        if operation_types:
            query = query.filter(UserActivity.operation_type.in_(operation_types))

        activities = query.all()
        
        data = []
        for activity in activities:
            data.append({
                '操作ID': activity.id,
                '服务器ID': activity.server_id,
                '用户名': activity.user_name,
                '操作时间': activity.timestamp,
                '客户端主机': activity.client_host,
                '数据库': activity.db_name,
                '操作类型': activity.operation_type,
                '风险等级': activity.risk_level,
                'SQL语句': activity.argument,
                '线程ID': activity.thread_id
            })
        
        return pd.DataFrame(data)

    @classmethod
    def generate_daily_report(cls):
        """生成日报"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=1)
        return cls.generate_summary_report(start_date, end_date)

    @classmethod
    def generate_weekly_report(cls):
        """生成周报"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)
        return cls.generate_summary_report(start_date, end_date)

    @classmethod
    def generate_monthly_report(cls):
        """生成月报"""
        end_date = datetime.now()
        start_date = end_date - timedelta(days=30)
        return cls.generate_summary_report(start_date, end_date)

    @classmethod
    def generate_daily_report_json(cls, start_date=None, end_date=None, server_id=None, operation_type=None, risk_level=None, user_name=None):
        """生成日报的JSON数据，支持自定义参数"""
        # 如果没有提供日期，使用默认的日报时间范围
        if not start_date and not end_date:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=1)
            
        report_data = cls.generate_summary_report(
            start_date=start_date, 
            end_date=end_date,
            server_id=server_id,
            operation_type=operation_type,
            risk_level=risk_level,
            user_name=user_name
        )
        
        # 将日期时间对象转换为字符串，使其可JSON序列化
        report_data['period']['start'] = report_data['period']['start'].strftime('%Y-%m-%d %H:%M:%S')
        report_data['period']['end'] = report_data['period']['end'].strftime('%Y-%m-%d %H:%M:%S')
        
        return report_data
        
    @classmethod
    def generate_weekly_report_json(cls, start_date=None, end_date=None, server_id=None, operation_type=None, risk_level=None, user_name=None):
        """生成周报的JSON数据，支持自定义参数"""
        # 如果没有提供日期，使用默认的周报时间范围
        if not start_date and not end_date:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
        report_data = cls.generate_summary_report(
            start_date=start_date, 
            end_date=end_date,
            server_id=server_id,
            operation_type=operation_type,
            risk_level=risk_level,
            user_name=user_name
        )
        
        # 将日期时间对象转换为字符串，使其可JSON序列化
        report_data['period']['start'] = report_data['period']['start'].strftime('%Y-%m-%d %H:%M:%S')
        report_data['period']['end'] = report_data['period']['end'].strftime('%Y-%m-%d %H:%M:%S')
        
        return report_data
        
    @classmethod
    def generate_monthly_report_json(cls, start_date=None, end_date=None, server_id=None, operation_type=None, risk_level=None, user_name=None):
        """生成月报的JSON数据，支持自定义参数"""
        # 如果没有提供日期，使用默认的月报时间范围
        if not start_date and not end_date:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)
            
        report_data = cls.generate_summary_report(
            start_date=start_date, 
            end_date=end_date,
            server_id=server_id,
            operation_type=operation_type,
            risk_level=risk_level,
            user_name=user_name
        )
        
        # 将日期时间对象转换为字符串，使其可JSON序列化
        report_data['period']['start'] = report_data['period']['start'].strftime('%Y-%m-%d %H:%M:%S')
        report_data['period']['end'] = report_data['period']['end'].strftime('%Y-%m-%d %H:%M:%S')
        
        return report_data 