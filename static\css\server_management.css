/* 服务器管理系统样式 */

body {
    background-color: #fff;
    color: #333;
}

.container {
    max-width: 1400px;
    margin: 20px auto;
    padding: 0 15px;
}

.page-header {
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.page-title {
    color: #2c3e50;
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0;
}

.data-summary {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
    transition: all 0.3s ease;
}

.data-summary:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
}

.summary-title {
    font-size: 1.4rem;
    color: #0d6efd;
    margin-bottom: 20px;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 12px;
    font-weight: 600;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
    padding: 8px 0;
    border-bottom: 1px dashed #e9ecef;
}

.summary-label {
    font-weight: 500;
    color: #495057;
}

.summary-value {
    font-weight: 600;
    color: #0d6efd;
}

.chart-container {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
    height: 400px;
    transition: all 0.3s ease;
}

.chart-container:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.chart-title {
    font-size: 1.3rem;
    color: #0d6efd;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 600;
}

.chart {
    width: 100%;
    height: 320px;
}

.table-container {
    background-color: #fff;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    margin-bottom: 25px;
    transition: all 0.3s ease;
}

.table-container:hover {
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.08);
}

.table-title {
    font-size: 1.3rem;
    color: #0d6efd;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

.table {
    width: 100%;
    margin-bottom: 0;
    color: #212529;
    border-collapse: collapse;
    table-layout: fixed;
}

.table th {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border-bottom: 2px solid #dee2e6;
    padding: 12px 15px;
    text-align: center;
    vertical-align: middle;
    font-size: 0.9rem;
}

.table th[data-bs-toggle="tooltip"] {
    cursor: help;
    border-bottom: 1px dashed #6c757d;
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
    font-size: 0.9rem;
}

.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 150px;
    display: inline-block;
}

.os-column, .components-column {
    max-width: 150px;
    width: 150px;
}

.os-system-cell, .components-cell {
    max-width: 150px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 8px 10px;
}

.action-buttons {
    white-space: nowrap;
    text-align: center;
}

.action-buttons .btn {
    display: inline-block;
    margin: 0 2px;
    border-radius: 4px;
    transition: all 0.2s ease;
    padding: 4px 8px;
    font-size: 0.8rem;
    width: auto;
}

.action-buttons .btn i {
    margin-right: 3px;
}

.action-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.d-flex.justify-content-center {
    display: flex !important;
    justify-content: center !important;
    flex-wrap: nowrap;
}

.me-2 {
    margin-right: 0.5rem !important;
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

.table-responsive {
    overflow-x: auto;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    margin-bottom: 30px;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-bordered {
    border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.tooltip {
    position: absolute;
    z-index: 1070;
    display: block;
    margin: 0;
    font-family: inherit;
    font-style: normal;
    font-weight: 400;
    line-height: 1.5;
    text-align: left;
    text-decoration: none;
    text-shadow: none;
    text-transform: none;
    letter-spacing: normal;
    word-break: normal;
    word-spacing: normal;
    white-space: normal;
    line-break: auto;
    font-size: 14px;
    word-wrap: break-word;
    opacity: 0;
}

.tooltip.show {
    opacity: 0.9;
}

.tooltip .tooltip-inner {
    max-width: 300px;
    padding: 8px 12px;
    color: #fff;
    text-align: left;
    background-color: #2c3e50;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    white-space: pre-wrap;
    word-break: break-word;
}

.nav-tabs {
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    color: #495057;
    border: 1px solid transparent;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
    padding: 12px 20px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #0d6efd;
}

.nav-tabs .nav-link.active {
    color: #0d6efd;
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    font-weight: 600;
}

.tab-content {
    padding: 25px;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-top: none;
    border-radius: 0 0 8px 8px;
}

.modal-header {
    background-color: #0d6efd;
    color: white;
    border-radius: 8px 8px 0 0;
    padding: 15px 20px;
}

.modal-title {
    font-weight: 600;
    font-size: 1.3rem;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 10px 15px;
    transition: border-color 0.2s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.btn-back {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    color: #0d6efd;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-back:hover {
    color: #0a58ca;
    text-decoration: underline;
}

.btn-back i {
    margin-right: 5px;
}

.alert {
    border-radius: 8px;
    padding: 15px 20px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d1e7dd;
    color: #0f5132;
    border: 1px solid #badbcc;
}

.alert-danger {
    background-color: #f8d7da;
    color: #842029;
    border: 1px solid #f5c2c7;
}

.badge {
    padding: 6px 10px;
    border-radius: 6px;
    font-weight: 500;
}

.badge-primary {
    background-color: #0d6efd;
    color: white;
}

.badge-success {
    background-color: #198754;
    color: white;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-danger {
    background-color: #dc3545;
    color: white;
}

.empty-state {
    text-align: center;
    padding: 50px 0;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: #adb5bd;
}

.empty-state p {
    font-size: 1.1rem;
    margin-bottom: 20px;
}

/* 服务器利用率详情样式 */
.server-usage-details {
    padding: 10px;
}

.server-usage-details .usage-card {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.server-usage-details .usage-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.server-usage-details .progress {
    border-radius: 10px;
    overflow: hidden;
    background-color: #e9ecef;
    box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
}

.server-usage-details .progress-bar {
    font-weight: bold;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
}

.server-usage-details .badge {
    font-size: 0.9rem;
    padding: 6px 10px;
}

.server-usage-details h6 {
    font-weight: 600;
}

/* 进度条颜色样式 */
.progress-bar.bg-success {
    background: linear-gradient(45deg, #28a745, #20c997);
}

.progress-bar.bg-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
}

.progress-bar.bg-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

/* 整体布局 */
.container-fluid {
    padding: 0;
    background-color: #fff;
    min-width: 1200px;
}

/* 左侧导航 */
.sidebar {
    background-color: #fff;
    border-right: 1px solid #eee;
    min-height: 100vh;
    padding: 15px 0;
    width: auto;
    min-width: 180px;
    max-width: 250px;
    flex: 0 0 auto;
}

.nav-section {
    margin-top: 10px;
    width: 100%;
}

.nav-item {
    margin-bottom: 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.nav-link {
    display: block;
    padding: 8px 15px;
    color: #666;
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.2s ease;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.platform-nav {
    padding-left: 15px;
    width: 100%;
}

.platform-nav .list-group-item {
    padding: 6px 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 150px;
}

/* 主内容区域 */
.main-content {
    padding: 20px 25px;
    background-color: #fff;
    flex: 1 1 auto;
    min-width: 0;
    overflow-x: auto;
}

/* 表格样式 */
.table-responsive {
    margin: 0;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

.table {
    margin-bottom: 0;
    width: 100%;
    min-width: 1000px;
}

.table th {
    white-space: nowrap;
    min-width: 80px;
}

/* IP地址列 */
.table th:nth-child(1),
.table td:nth-child(1) {
    min-width: 120px;
}

/* 主机名列 */
.table th:nth-child(2),
.table td:nth-child(2) {
    min-width: 180px;
}

/* CPU列 */
.table th:nth-child(3),
.table td:nth-child(3) {
    min-width: 80px;
}

/* 内存列 */
.table th:nth-child(4),
.table td:nth-child(4) {
    min-width: 80px;
}

/* 存储列 */
.table th:nth-child(5),
.table td:nth-child(5) {
    min-width: 80px;
}

/* 操作系统列 */
.table th:nth-child(6),
.table td:nth-child(6) {
    min-width: 100px;
}

/* 服务列 */
.table th:nth-child(7),
.table td:nth-child(7) {
    min-width: 80px;
    width: 80px;
}

/* 操作列 */
.table th:nth-child(8),
.table td:nth-child(8) {
    min-width: 100px;
    width: 100px;
}

.table td {
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 300px;
}

/* 按钮样式 */
.btn-group {
    white-space: nowrap;
}

.btn-group-sm > .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
}

/* 响应式布局 */
@media (max-width: 1400px) {
    .sidebar {
        min-width: 160px;
    }
    
    .main-content {
        padding: 15px 20px;
    }
}

@media (min-width: 1401px) {
    .table td {
        max-width: 400px;
    }
}

/* 工具提示 */
.tooltip {
    max-width: 300px;
}
