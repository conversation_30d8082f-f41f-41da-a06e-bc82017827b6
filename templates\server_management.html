<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务器管理</title>
    <link href="{{ url_for('static', filename='css/bootstrap.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/all.min.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/server_management.css') }}" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/search-modal.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <div class="row flex-nowrap">
            <!-- 左侧导航 -->
            <div class="sidebar">
                <div class="nav-section">
                    <div class="nav-item">
                        <a href="#" class="nav-link d-flex align-items-center" data-type="port">
                            <i class="fas fa-network-wired me-2"></i>
                            <span>端口管理</span>
                        </a>
                    </div>
                    <div class="nav-item">
                        <a href="#" class="nav-link d-flex align-items-center" data-type="server">
                            <i class="fas fa-server me-2"></i>
                            <span>服务器管理</span>
                        </a>
                        <div class="platform-nav" style="display: none;">
                            <div class="list-group" id="platformList">
                                <!-- 平台列表将通过JavaScript动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧内容区域 -->
            <div class="main-content">
                <!-- 服务器管理内容 -->
                <div id="serverManagement" class="container-fluid mt-4">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">服务器管理</h5>
                            <div class="d-flex align-items-center">
                                <div class="search-input-wrapper me-2">
                                    <input type="text" class="form-control form-control-sm" id="directServerSearch" placeholder="输入IP地址搜索..." style="width: 200px;">
                                </div>
                                <button type="button" class="btn btn-outline-info me-2" id="directServerSearchBtn">
                                    <i class="fas fa-search me-1"></i>搜索
                                </button>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addServerModal">
                                    添加服务器
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 服务器管理表格 -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th class="text-center" style="width: 10%">IP地址</th>
                                            <th class="text-center" style="width: 10%">主机名</th>
                                            <th class="text-center" style="width: 6%">CPU(核)</th>
                                            <th class="text-center" style="width: 6%">内存(GB)</th>
                                            <th class="text-center" style="width: 6%">存储(GB)</th>
                                            <th class="text-center" style="width: 8%">利用率</th>
                                            <th class="text-center" style="width: 8%">目录树</th>
                                            <th class="text-center" style="width: 10%">操作系统</th>
                                            <th class="text-center" style="width: 8%">部署组件</th>
                                            <th class="text-center" style="width: 12%">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="serverTableBody">
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 端口管理内容 -->
                <div id="portManagement" class="container-fluid mt-4" style="display: none;">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">端口管理</h5>
                            <div class="d-flex align-items-center">
                                <div class="search-input-wrapper me-2">
                                    <input type="text" class="form-control form-control-sm" id="directPortSearch" placeholder="输入IP地址搜索..." style="width: 200px;">
                                </div>
                                <button type="button" class="btn btn-outline-info me-2" id="directPortSearchBtn">
                                    <i class="fas fa-search me-1"></i>搜索
                                </button>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPortModal">
                                    添加端口
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <!-- 端口管理表格 -->
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th class="text-center">IP地址</th>
                                            <th class="text-center">部署组件</th>
                                            <th class="text-center">服务端口</th>
                                            <th class="text-center">Dubbo端口</th>
                                            <th class="text-center">XXL-JOB端口</th>
                                            <th class="text-center">操作</th>
                                        </tr>
                                    </thead>
                                    <tbody id="portTableBody">
                                        <!-- 端口信息将通过JavaScript动态加载 -->
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详情模态框 -->
    <div class="modal fade" id="detailsModal" tabindex="-1" aria-labelledby="detailsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detailsModalLabel">服务详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="componentsDetails" class="service-details"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- 组件详情模态框 -->
    <div class="modal fade" id="componentDetailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">组件详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="componentDetailContent">
                        <!-- 组件详情内容将通过JavaScript动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 利用率详情模态框 -->
    <div class="modal fade" id="usageDetailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">服务器利用率详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="usageDetailContent">
                        <!-- 利用率详情内容将通过JavaScript动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 目录树详情模态框 -->
    <div class="modal fade" id="directoryTreeModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">服务器目录树</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div id="directoryTreeContent">
                        <!-- 目录树内容将通过JavaScript动态加载 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加服务器模态框 -->
    <div class="modal fade" id="addServerModal" tabindex="-1" aria-labelledby="addServerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addServerModalLabel">添加服务器</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addServerForm">
                        <div class="mb-3">
                            <label for="ip" class="form-label">IP地址</label>
                            <input type="text" class="form-control" id="ip" name="ip" required>
                        </div>
                        <div class="mb-3">
                            <label for="hostname" class="form-label">主机名</label>
                            <input type="text" class="form-control" id="hostname" name="hostname" required>
                        </div>
                        <div class="mb-3">
                            <label for="platform" class="form-label">所属平台</label>
                            <input type="text" class="form-control" id="platform" name="platform" required>
                        </div>
                        <div class="mb-3">
                            <label for="cpu" class="form-label">CPU(核)</label>
                            <input type="number" class="form-control" id="cpu" name="cpu" required>
                        </div>
                        <div class="mb-3">
                            <label for="memory" class="form-label">内存(GB)</label>
                            <input type="number" class="form-control" id="memory" name="memory" required>
                        </div>
                        <div class="mb-3">
                            <label for="disk" class="form-label">存储(GB)</label>
                            <input type="number" class="form-control" id="disk" name="disk" required>
                        </div>
                        <div class="mb-3">
                            <label for="os_system" class="form-label">操作系统</label>
                            <input type="text" class="form-control" id="os_system" name="os_system" required>
                        </div>
                        <div class="mb-3">
                            <label for="deploy_components" class="form-label">部署组件</label>
                            <input type="text" class="form-control" id="deploy_components" name="deploy_components">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveServerBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑服务器模态框 -->
    <div class="modal fade" id="editServerModal" tabindex="-1" aria-labelledby="editServerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editServerModalLabel">编辑服务器信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="editServerForm">
                        <div class="mb-3">
                            <label for="edit_server_ip">IP地址</label>
                            <input type="text" class="form-control" id="edit_server_ip" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="edit_hostname">主机名</label>
                            <input type="text" class="form-control" id="edit_hostname">
                        </div>
                        <div class="mb-3">
                            <label for="edit_cpu">CPU(核)</label>
                            <input type="number" class="form-control" id="edit_cpu">
                        </div>
                        <div class="mb-3">
                            <label for="edit_memory">内存(GB)</label>
                            <input type="number" class="form-control" id="edit_memory">
                        </div>
                        <div class="mb-3">
                            <label for="edit_disk">存储(GB)</label>
                            <input type="number" class="form-control" id="edit_disk">
                        </div>
                        <div class="mb-3">
                            <label for="edit_os_system">操作系统</label>
                            <input type="text" class="form-control" id="edit_os_system">
                        </div>
                        <div class="mb-3">
                            <label for="edit_deploy_components">部署组件</label>
                            <input type="text" class="form-control" id="edit_deploy_components">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="saveServerEdit">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 编辑端口模态框 -->
    <div class="modal fade" id="editPortModal" tabindex="-1" aria-labelledby="editPortModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editPortModalLabel">编辑端口信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="editPortForm">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="edit_port_ip">IP地址</label>
                            <input type="text" class="form-control" id="edit_port_ip" readonly>
                        </div>
                        <div class="mb-3">
                            <label for="edit_deploy_component" class="form-label">部署组件</label>
                            <input type="text" class="form-control" id="edit_deploy_component" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit_app_port" class="form-label">服务端口</label>
                            <input type="text" class="form-control" id="edit_app_port" placeholder="多个端口请用逗号分隔，如：8080, 8081">
                            <div class="form-text">可以输入多个端口，用逗号分隔</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_dubbo_port" class="form-label">Dubbo端口</label>
                            <input type="text" class="form-control" id="edit_dubbo_port" placeholder="多个端口请用逗号分隔，如：20880, 20881">
                            <div class="form-text">可以输入多个端口，用逗号分隔</div>
                        </div>
                        <div class="mb-3">
                            <label for="edit_xxl_job_port" class="form-label">XXL-JOB端口</label>
                            <input type="text" class="form-control" id="edit_xxl_job_port" placeholder="多个端口请用逗号分隔，如：9999, 9998">
                            <div class="form-text">可以输入多个端口，用逗号分隔</div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 删除服务器确认模态框 -->
    <div class="modal fade" id="deleteServerModal" tabindex="-1" aria-labelledby="deleteServerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deleteServerModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除服务器 <span id="deleteServerName"></span> (<span id="deleteServerIP"></span>) 吗？此操作不可逆。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteServerBtn">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 添加服务端口模态框 -->
    <div class="modal fade" id="addPortModal" tabindex="-1" aria-labelledby="addPortModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPortModalLabel">添加服务端口</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="addPortForm">
                        <div class="mb-3">
                            <label for="port_ip" class="form-label">服务器IP</label>
                            <select class="form-select" id="port_ip" name="ip" required>
                                <option value="">选择服务器IP</option>
                                {% for server in server_info %}
                                <option value="{{ server.ip }}">{{ server.ip }} ({{ server.hostname }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="deploy_component" class="form-label">部署组件</label>
                            <input type="text" class="form-control" id="deploy_component" name="deploy_component" required>
                        </div>
                        <div class="mb-3">
                            <label for="app_port" class="form-label">应用端口</label>
                            <input type="text" class="form-control" id="app_port" name="app_port">
                        </div>
                        <div class="mb-3">
                            <label for="dubbo_server_port" class="form-label">Dubbo服务端口</label>
                            <input type="text" class="form-control" id="dubbo_server_port" name="dubbo_server_port">
                        </div>
                        <div class="mb-3">
                            <label for="xxl_job_port" class="form-label">XXL-JOB端口</label>
                            <input type="number" class="form-control" id="xxl_job_port" name="xxl_job_port">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" id="savePortBtn">保存</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 删除服务端口确认模态框 -->
    <div class="modal fade" id="deletePortModal" tabindex="-1" aria-labelledby="deletePortModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="deletePortModalLabel">确认删除</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>您确定要删除服务器 <span id="deletePortIP"></span> 上的 <span id="deletePortComponent"></span> 组件吗？此操作不可逆。</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-danger" id="confirmDeletePortBtn">删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索服务器模态框 -->
    <div class="modal fade search-modal" id="searchServerModal" tabindex="-1" aria-labelledby="searchServerModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="searchServerModalLabel">搜索服务器</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="search-input-group">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="form-control" id="searchServerQuery" placeholder="输入IP地址关键字...">
                        <div class="form-text">支持模糊搜索，例如: 192.168</div>
                    </div>
                    <div id="searchServerResults" class="search-results">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-close-modal" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-search" id="searchServerBtn">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索端口模态框 -->
    <div class="modal fade search-modal" id="searchPortModal" tabindex="-1" aria-labelledby="searchPortModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="searchPortModalLabel">搜索端口</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="search-input-group">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="form-control" id="searchPortQuery" placeholder="输入IP地址关键字...">
                        <div class="form-text">支持模糊搜索，例如: 192.168</div>
                    </div>
                    <div id="searchPortResults" class="search-results">
                        <!-- 搜索结果将在这里显示 -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-close-modal" data-bs-dismiss="modal">关闭</button>
                    <button type="button" class="btn btn-search" id="searchPortBtn">
                        <i class="fas fa-search me-1"></i>搜索
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 组件列表模态框 -->
    <div class="modal fade" id="componentListModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="componentListModalTitle">服务器部署组件</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="componentListModalBody">
                    <!-- 组件列表内容将通过JavaScript动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 资源使用率模态框 -->
    <div class="modal fade" id="usageModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="usageModalTitle">服务器资源使用率</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="usageModalBody">
                    <!-- 资源使用率内容将通过JavaScript动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 端口详情模态框 -->
    <div class="modal fade" id="portDetailModal" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="portDetailModalTitle">端口详情</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="portDetailModalBody">
                    <!-- 端口详情内容将通过JavaScript动态加载 -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <style>
        .action-link {
            color: #666;
            text-decoration: none;
            margin: 0 5px;
            cursor: pointer;
        }
        .action-link:hover {
            color: #333;
        }
        .action-link.edit {
            color: #1890ff;
        }
        .action-link.delete {
            color: #ff4d4f;
        }
        .action-separator {
            color: #ddd;
            margin: 0 2px;
        }
        .btn-details {
            min-width: 60px;
        }
    </style>

    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/server_management.js') }}"></script>
    <script src="{{ url_for('static', filename='js/server_search.js') }}"></script>
    
    <!-- 引入通用版本更新日志模态框 -->
    {% include 'changelog_modal.html' %}
</body>
</html>
