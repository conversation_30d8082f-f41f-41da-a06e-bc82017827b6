# 库表挂接率模块 (`table_connection_rate`) 功能文档

## 模块概述

`table_connection_rate` 模块旨在提供对数据资源目录与实际数据（库表、接口）挂接情况的统计与可视化展示。用户可以通过此模块了解全市及下辖各区县的目录编制情况、已挂接资源的数量和比例，特别是针对库表和API接口的挂接率。

该模块主要由以下部分组成：
- **后端服务 (`src/county_data/table_connection_rate.py`)**: 提供数据获取、处理逻辑以及API接口。
- **前端页面 (`templates/table_connection_rate.html`)**: 负责展示统计数据。
- **外部目录数据库**: 一个独立的数据库（代码中默认为 `dsp_catalog`），存储所有数据目录的元数据信息。

## 核心功能与数据指标

### 1. 数据库连接管理 (`DBConnectionManager`)

- **功能**: 负责建立和管理到外部目录数据库的连接。
- **配置获取**:
    - 连接配置（主机、端口、用户、密码、库名）优先从主应用数据库的 `db_connection_configs_metadata` 表中读取，查询条件为 `config_name = 'ku_biao_gua_jie_lu_db'`。
    - **SQL示例 (从主库获取配置)**:
      ```sql
      SELECT config_name, db_host, db_port, db_username, db_password, db_name 
      FROM db_connection_configs_metadata 
      WHERE config_name = 'ku_biao_gua_jie_lu_db'
      LIMIT 1;
      ```
    - 如果在主库中未找到配置，则会使用一组硬编码的默认连接信息（主要用于开发环境）。
      - 默认主机: `**************`
      - 默认端口: `3310`
      - 默认用户: `root`
      - 默认密码: `123456`
      - 默认数据库: `dsp_catalog`
- **连接缓存**: 包含一个简单的连接缓存机制，以减少不必要的重连开销。

### 2. 统计数据计算 (`get_connection_stats()` 函数)

此函数是核心的数据获取和计算单元，所有展示的统计数据均来源于此。它直接查询目录数据库（如 `dsp_catalog`）。

#### 2.1. 全市统计数据

这些数据从 `dsp_catalog.data_catalog` 表中统计，筛选条件通常为 `status = 4` (表示目录已发布或有效) 和 `is_del = 0` (表示目录未被删除)。

-   **全市总目录数 (`city.total_catalogs`)**:
    -   **描述**: 全市范围内已发布且未删除的有效数据目录总数量。
    -   **SQL**:
        ```sql
        SELECT COUNT(1) 
        FROM dsp_catalog.data_catalog 
        WHERE `status` = 4 AND is_del = 0;
        ```

-   **全市已挂接资源目录数 (`city.connected_catalogs`)**:
    -   **描述**: 全市已发布、未删除且至少挂接了一种资源（文件、库表或API）的数据目录数量。
    -   **SQL**:
        ```sql
        SELECT COUNT(1) 
        FROM dsp_catalog.data_catalog 
        WHERE `status` = 4 AND is_del = 0 AND file_count + table_count + api_count > 0;
        ```

-   **全市资源目录挂接率 (`city.connection_rate`)**:
    -   **描述**: （全市已挂接资源目录数 / 全市总目录数） * 100%。
    -   **计算**: Python代码中计算 `round(results["city"]["connected_catalogs"] / results["city"]["total_catalogs"] * 100, 2)`。

-   **全市已挂接库表接口目录数 (`city.table_api_connected`)**:
    -   **描述**: 全市已发布、未删除且至少挂接了库表或API的数据目录数量。
    -   **SQL**:
        ```sql
        SELECT COUNT(1) 
        FROM dsp_catalog.data_catalog 
        WHERE `status` = 4 AND is_del = 0 AND table_count + api_count > 0;
        ```

-   **全市库表接口挂接率 (`city.table_api_connection_rate`)**:
    -   **描述**: （全市已挂接库表接口目录数 / 全市总目录数） * 100%。
    -   **计算**: Python代码中计算 `round(results["city"]["table_api_connected"] / results["city"]["total_catalogs"] * 100, 2)`。

#### 2.2. 区县统计数据

区县数据通过遍历一个预定义的区县名称到区县代码（`region_code`）的映射 `county_codes` 来分别统计。

-   **预定义区县代码 (`county_codes` 映射)**:
    ```python
    county_codes = {
        '秦安县': '620522000000',
        '张家川回族自治县': '620525000000',
        '甘谷县': '620523000000',
        '武山县': '620524000000',
        '清水县': '620521000000',
        '麦积区': '620503000000',
        '秦州区': '620502000000'
    }
    ```

-   **各区县目录总数 (`counties.{county_name}.total`)**:
    -   **描述**: 特定区县内已发布且未删除的有效数据目录总数量。
    -   **SQL (以特定 `region_code` 为例)**:
        ```sql
        SELECT COUNT(1) 
        FROM dsp_catalog.data_catalog 
        WHERE `status` = 4 AND is_del = 0 AND region_code = '{region_code}'; 
        -- {region_code} 会被替换为实际的区县代码
        ```

-   **各区县已挂接库表接口目录数 (`counties.{county_name}.connected`)**:
    -   **描述**: 特定区县内已发布、未删除且至少挂接了库表或API的数据目录数量。
    -   **SQL (以特定 `region_code` 为例)**:
        ```sql
        SELECT COUNT(1) 
        FROM dsp_catalog.data_catalog 
        WHERE `status` = 4 AND is_del = 0 AND table_count + api_count > 0 AND region_code = '{region_code}';
        -- {region_code} 会被替换为实际的区县代码
        ```

-   **各区县库表接口挂接率 (`connection_rates.{county_name}`)**:
    -   **描述**: （某区县已挂接库表接口目录数 / 该区县总目录数） * 100%。
    -   **计算**: Python代码中计算 `round(county_connected / county_total * 100, 2)`。

### 3. API接口

-   **页面路由 (`@table_connection_rate_bp.route('/')`)**:
    -   **功能**: 渲染 `table_connection_rate.html` 页面。
    -   在渲染前调用 `get_connection_stats()` 获取最新的统计数据传递给模板。
    -   包含错误处理逻辑，如果 `get_connection_stats()` 返回错误或配置问题，会向模板传递错误信息和设置指南。

-   **数据API路由 (`@table_connection_rate_bp.route('/api/stats')`)**:
    -   **功能**: 提供JSON格式的统计数据，主要供前端页面定时刷新数据使用。
    -   **参数**: 支持 `periodOffset` URL参数，用于尝试获取非当前周期的历史数据（例如 `periodOffset=-1` 表示上一周）。
    -   **内部调用**:
        - 根据 `periodOffset` 计算一个 `cutoff_date` (截止日期)。
        - 调用 `get_connection_stats_by_date(cutoff_date_str)` 来获取数据。
        - **重要**: 在提供的 `table_connection_rate.py` 代码片段中，`get_connection_stats_by_date` 函数并未定义。因此，此API接口的实际行为（特别是日期过滤部分）取决于该缺失函数的具体实现。如果该函数未实现或只是简单调用 `get_connection_stats()`，则 `periodOffset` 参数可能不会按预期进行历史数据筛选。当前文档基于已定义的 `get_connection_stats()` 行为。

## 前端页面展示 (`table_connection_rate.html`)

前端页面使用获取到的统计数据进行可视化展示：

-   **全市概览**:
    -   **资源目录挂接情况**: 卡片展示全市总目录数、已挂接（任何类型）资源数、总体挂接率，并附带进度条。
    -   **库表接口挂接情况**: 卡片展示全市总目录数、已挂接库表/接口数、库表/接口挂接率，并附带进度条。
-   **县区目录总数**: 为每个区县分别使用卡片展示其目录总数。
-   **县区库表接口挂接率**: 为每个区县分别使用卡片展示其库表接口挂接率，并附带进度条。进度条颜色会根据挂接率高低变化（例如，高为绿色，中为黄色，低为红色）。
-   **错误提示**: 如果后端数据获取失败或存在配置问题，页面会显示相应的错误信息和配置指引。
-   **自动刷新**: 页面JavaScript包含一个定时器，默认每10分钟通过 `/table_connection_rate/api/stats` 接口请求更新数据，并动态刷新页面上相应的统计数值和进度条。

## 注意事项

-   **`get_connection_stats_by_date` 函数缺失**: 如上文API接口部分所述，`/api/stats` 路由依赖一个名为 `get_connection_stats_by_date` 的函数，该函数在提供的代码中未定义。这将影响历史数据查询功能。
-   **硬编码的区县代码**: 区县的统计依赖于 `table_connection_rate.py`中硬编码的 `county_codes` 字典。如果区县信息或代码发生变更，需要同步更新此字典。
-   **状态与删除标记**: 所有统计查询都强依赖 `status = 4` 和 `is_del = 0` 这两个条件，确保只统计有效且未删除的目录。
-   **缓存机制**: 代码中虽然有 `refresh_cache` 函数，但其日志表明"缓存机制已移除"，该函数目前仅简单调用 `get_connection_stats()`。

此文档旨在为后续的开发和维护工作提供清晰的指引。
