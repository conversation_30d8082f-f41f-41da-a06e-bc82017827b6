from flask import Blueprint, jsonify, request, render_template

from src.utils.error_handler import (
    handle_exceptions, DatabaseError, NetworkError, ValidationError,
    BusinessError, create_error_response, create_success_response,
    ErrorCategory, ErrorLevel
)
from sqlalchemy import text
from datetime import datetime, timedelta
from src.utils.database_helpers import get_db_connection, get_all_tables, get_table_comments, get_table_display_name, get_table_stats
from sqlalchemy.engine import create_engine
from src.county_data.table_connection_rate import DBConnectionManager
import logging

# 配置日志
logger = logging.getLogger(__name__)

# 确保 get_db_connection 能正确工作，或者从 app 实例中获取

summary_stats_bp = Blueprint('summary_stats_bp', __name__, url_prefix='/api/county_summary')

@summary_stats_bp.route('/counties', methods=['GET'])
def get_county_list():
    """获取所有区县列表 (表名和显示名称) """
    try:
        engine = get_db_connection() # 假设这个函数能正确获取数据库引擎
        all_db_tables = get_all_tables(engine)
        comments = get_table_comments(engine)
        
        counties = []
        for tbl in all_db_tables:
            # 假设区县数据表以 'excel_data_' 开头，并排除市级汇总表 (如 'excel_data_tss_sz')
            # 你可能需要根据实际情况调整这里的过滤逻辑
            if tbl.startswith('excel_data_') and 'tss_sz' not in tbl: 
                # 尝试从表注释中提取更友好的县区名，例如 "天水市_甘谷县" -> "甘谷县"
                # 如果表注释格式不符合预期或不存在，则使用处理过的表名
                table_comment = comments.get(tbl, '')
                county_name_from_comment = ''
                if '_' in table_comment:
                    parts = table_comment.split('_')
                    if len(parts) > 1:
                        county_name_from_comment = parts[-1] # 取最后一个部分作为县区名

                display_name = county_name_from_comment if county_name_from_comment else get_table_display_name(tbl)
                counties.append({"table_name": tbl, "display_name": display_name})
        
        # 按显示名称排序 (可选)
        counties.sort(key=lambda x: x['display_name'])
        
        return jsonify(counties)
    except Exception as e:
        print(f"Error in get_county_list: {str(e)}")
        # 在实际应用中，这里可能需要更完善的日志记录
        return create_error_response("Failed to retrieve county list", "ERROR", 500)

@summary_stats_bp.route('/stats', methods=['GET'])
def get_county_card_stats():
    """获取指定区县的卡片统计数据"""
    table_name = request.args.get('table_name')
    if not table_name:
        return create_error_response("Table name (county) is required", "ERROR", 400)

    try:
        engine = get_db_connection()
        stats = get_table_stats(table_name, engine)

        if not stats or stats.get('table_stats') is None:
             return jsonify({"error": f"Could not retrieve stats for table {table_name}. The table might be empty or not configured for stats."}), 404

        current_county_display_name = stats.get('current_county')
        if not current_county_display_name:
            comments = get_table_comments(engine)
            table_comment = comments.get(table_name, '')
            if '_' in table_comment:
                parts = table_comment.split('_')
                if len(parts) > 1:
                    current_county_display_name = parts[-1]
            if not current_county_display_name:
                 current_county_display_name = get_table_display_name(table_name)

        total_population = stats.get('total_population', 0)
        annual_new_additions = stats.get('yearly_increment', 0)

        response_data = {
            "county_name": current_county_display_name,
            "total_aggregation": stats.get('total_records', 0),
            "annual_new_additions": annual_new_additions,
            "total_per_capita": stats.get('per_capita', 0) # 这个已经是 总汇聚量 / 总人口
        }

        # 修正"年度新增人均"的计算逻辑
        # 年度新增人均 = 年度新增数据量 / 县区总人口
        if total_population is not None and total_population > 0 and annual_new_additions is not None:
            try:
                numeric_annual_new_additions = float(annual_new_additions)
                numeric_total_population = float(total_population)
                response_data["annual_new_per_capita"] = round(numeric_annual_new_additions / numeric_total_population, 2)
            except (ValueError, TypeError):
                 response_data["annual_new_per_capita"] = None # 如果转换失败
        else:
            response_data["annual_new_per_capita"] = None 

        return jsonify(response_data)
    except Exception as e:
        print(f"Error in get_county_card_stats for table {table_name}: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"Failed to retrieve stats for {table_name}", "details": str(e)}), 500 

@summary_stats_bp.route('/yearly_stats', methods=['GET'])
def get_county_yearly_stats():
    """获取指定年份的县区卡片统计数据"""
    table_name = request.args.get('table_name')
    if not table_name:
        return create_error_response("Table name (county) is required", "ERROR", 400)
    
    try:
        year = int(request.args.get('year', datetime.now().year))
    except (ValueError, TypeError):
        year = datetime.now().year
        logger.debug(f"无效年份参数，使用当前年份: {year}")
    
    try:
        # 从表名提取县区代码
        from src.utils.data_calculation import (
            get_county_code_from_table, get_county_name_from_code,
            get_county_population, calculate_county_total_data,
            calculate_county_yearly_data, calculate_county_per_capita_total,
            calculate_county_per_capita_yearly
        )
        
        county_code = get_county_code_from_table(table_name)
        county_name = get_county_name_from_code(county_code)
        
        # 使用统一的计算函数获取各项统计数据
        total_population = get_county_population(county_code, year)
        total_records = calculate_county_total_data(county_code, year)
        yearly_increment = calculate_county_yearly_data(county_code, year)
        per_capita = calculate_county_per_capita_total(county_code, year)
        annual_new_per_capita = calculate_county_per_capita_yearly(county_code, year)
        
        response_data = {
            "year": year,
            "county_name": county_name,
            "total_aggregation": int(total_records),
            "annual_new_additions": int(yearly_increment),
            "total_per_capita": per_capita,
            "annual_new_per_capita": annual_new_per_capita,
            "total_population": int(total_population)
        }
        
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"获取表 {table_name} 年份 {year} 统计数据错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": f"获取 {table_name} 年份统计数据失败", "details": str(e)}), 500

@summary_stats_bp.route('/per_capita_ranking', methods=['GET'])
def get_per_capita_ranking():
    """获取所有县区的人均数据排名（总人均量和年度新增人均）"""
    try:
        # 获取年份参数，默认为当前年份
        try:
            year = int(request.args.get('year', datetime.now().year))
        except (ValueError, TypeError):
            year = datetime.now().year
            logger.debug(f"无效年份参数，使用当前年份: {year}")
        
        # 使用公共计算模块获取县区人均排名数据
        from src.utils.data_calculation import (
            get_county_per_capita_ranking,
            calculate_city_total_data,
            calculate_city_yearly_data,
            get_county_population
        )
        
        try:
            # 获取总人均量排名（前10名）
            total_per_capita_ranking = get_county_per_capita_ranking(year, 'total', 10)
            
            # 获取年度新增人均排名（前10名）
            annual_new_per_capita_ranking = get_county_per_capita_ranking(year, 'annual', 10)
            
            # 获取全市数据
            city_total_records = calculate_city_total_data(year)
            city_yearly_increment = calculate_city_yearly_data(year)
            city_total_population = get_county_population('tss_sz', year)
            
            # 计算全市人均数据（保留2位小数）
            city_per_capita = round(float(city_total_records) / float(city_total_population), 2) if city_total_population > 0 else 0
            city_annual_per_capita = round(float(city_yearly_increment) / float(city_total_population), 2) if city_total_population > 0 else 0
            
            response_data = {
                "year": year,
                "total_per_capita_ranking": total_per_capita_ranking,
                "annual_new_per_capita_ranking": annual_new_per_capita_ranking,
                "city_stats": {
                    "total_per_capita": city_per_capita,
                    "annual_per_capita": city_annual_per_capita,
                    "total_population": int(city_total_population),
                    "total_records": int(city_total_records),
                    "yearly_increment": int(city_yearly_increment)
                }
            }
            
            return jsonify(response_data)
            
        except Exception as e:
            logger.error(f"计算县区排名数据时出错: {str(e)}")
            import traceback
            traceback.print_exc()
            # 返回空数据，避免前端显示异常
            return jsonify({
                "year": year,
                "total_per_capita_ranking": [],
                "annual_new_per_capita_ranking": [],
                "city_stats": {
                    "total_per_capita": 0,
                    "annual_per_capita": 0,
                    "total_population": 0,
                    "total_records": 0,
                    "yearly_increment": 0
                }
            })
            
    except Exception as e:
        logger.error(f"获取县区人均数据排名错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return create_error_response("获取县区人均数据排名失败", "ERROR", 500)

@summary_stats_bp.route('/table_connection_rate', methods=['GET'])
def get_table_connection_rate():
    """获取各县区的接口库表资源挂接率"""
    try:
        engine = get_db_connection()
        
        # 定义区县代码映射
        county_codes = {
            'qin_an': ('620522000000', '秦安县'),
            'zhang_jia_chuan': ('620525000000', '张家川县'),
            'qin_shui': ('620521000000', '清水县'),
            'gan_gu': ('620523000000', '甘谷县'),
            'mai_ji': ('620503000000', '麦积区'),
            'wu_shan': ('620524000000', '武山县'),
            'qin_zhou': ('620502000000', '秦州区')
        }
        
        # 存储结果
        results = {}
        
        # 获取目录数据库连接
        catalog_db = DBConnectionManager.get_catalog_db_connection()
        if not catalog_db:
            return create_error_response("无法连接到目录数据库", "ERROR", 500)
        
        with catalog_db.connect() as conn:
            # 获取全市数据
            total_query = text("SELECT COUNT(1) FROM dsp_catalog.data_catalog WHERE `status` = 4 AND is_del = 0")
            total_catalogs = conn.execute(total_query).scalar() or 0
            
            table_api_connected_query = text("SELECT COUNT(1) FROM dsp_catalog.data_catalog WHERE `status` = 4 AND is_del = 0 AND table_count + api_count > 0")
            table_api_connected = conn.execute(table_api_connected_query).scalar() or 0
            
            # 计算全市挂接率
            city_rate = round(table_api_connected / total_catalogs * 100, 2) if total_catalogs > 0 else 0
            results['city'] = city_rate
            
            # 获取各县区数据
            for english_name, (region_code, display_name) in county_codes.items():
                # 县区目录总数
                county_total_query = text(f"SELECT COUNT(1) FROM dsp_catalog.data_catalog WHERE `status` = 4 AND is_del = 0 AND region_code = '{region_code}'")
                county_total = conn.execute(county_total_query).scalar() or 0
                
                # 县区已挂接目录数
                county_connected_query = text(f"SELECT COUNT(1) FROM dsp_catalog.data_catalog WHERE `status` = 4 AND is_del = 0 AND table_count + api_count > 0 AND region_code = '{region_code}'")
                county_connected = conn.execute(county_connected_query).scalar() or 0
                
                # 计算县区挂接率
                county_rate = round(county_connected / county_total * 100, 2) if county_total > 0 else 0
                results[english_name] = county_rate
        
        return jsonify(results)
        
    except Exception as e:
        print(f"获取接口库表资源挂接率数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500 

@summary_stats_bp.route('/metrics_table_connection_rate', methods=['GET'])
def get_metrics_table_connection_rate():
    """获取指标统计页面的接口库表资源挂接率（考虑截止日期）"""
    try:
        # 获取周期偏移参数
        period_offset = request.args.get('periodOffset', 'current')
        
        # 根据周期偏移计算实际的日期范围和截止日期
        _, _, cutoff_date_str = get_period_date_range(period_offset)
        
        # 定义区县代码映射
        county_codes = {
            'qin_an': ('620522000000', '秦安县'),
            'zhang_jia_chuan': ('620525000000', '张家川县'),
            'qin_shui': ('620521000000', '清水县'),
            'gan_gu': ('620523000000', '甘谷县'),
            'mai_ji': ('620503000000', '麦积区'),
            'wu_shan': ('620524000000', '武山县'),
            'qin_zhou': ('620502000000', '秦州区')
        }
        
        # 前端ID到英文名映射
        frontend_to_english = {
            'qinan': 'qin_an',
            'zhangjiachuan': 'zhang_jia_chuan',
            'qingshui': 'qin_shui',
            'gangu': 'gan_gu',
            'maiji': 'mai_ji',
            'wushan': 'wu_shan',
            'qinzhou': 'qin_zhou'
        }
        
        # 存储结果
        results = {
            'cutoff_date': cutoff_date_str,
            'city': 0,
            'connection_rates': {}
        }
        
        # 获取目录数据库连接
        catalog_db = DBConnectionManager.get_catalog_db_connection()
        if not catalog_db:
            return create_error_response("无法连接到目录数据库", "ERROR", 500)
        
        with catalog_db.connect() as conn:
            # 获取全市数据
            total_query = text("""
                SELECT COUNT(1) 
                FROM dsp_catalog.data_catalog 
                WHERE `status` = 4 
                AND is_del = 0
                AND (update_time IS NULL OR update_time <= :cutoff_date)
            """)
            total_catalogs = conn.execute(total_query, {"cutoff_date": cutoff_date_str}).scalar() or 0
            
            table_api_connected_query = text("""
                SELECT COUNT(1) 
                FROM dsp_catalog.data_catalog 
                WHERE `status` = 4 
                AND is_del = 0 
                AND table_count + api_count > 0
                AND (update_time IS NULL OR update_time <= :cutoff_date)
            """)
            table_api_connected = conn.execute(table_api_connected_query, {"cutoff_date": cutoff_date_str}).scalar() or 0
            
            # 计算全市挂接率
            city_rate = round(table_api_connected / total_catalogs * 100, 2) if total_catalogs > 0 else 0
            results['city'] = city_rate
            
            # 获取各县区数据
            for frontend_id, english_name in frontend_to_english.items():
                region_code = county_codes.get(english_name, ['', ''])[0]
                if not region_code:
                    continue
                
                # 县区目录总数
                county_total_query = text(f"""
                    SELECT COUNT(1) 
                    FROM dsp_catalog.data_catalog 
                    WHERE `status` = 4 
                    AND is_del = 0 
                    AND region_code = '{region_code}'
                    AND (update_time IS NULL OR update_time <= :cutoff_date)
                """)
                county_total = conn.execute(county_total_query, {"cutoff_date": cutoff_date_str}).scalar() or 0
                
                # 县区已挂接目录数
                county_connected_query = text(f"""
                    SELECT COUNT(1) 
                    FROM dsp_catalog.data_catalog 
                    WHERE `status` = 4 
                    AND is_del = 0 
                    AND table_count + api_count > 0 
                    AND region_code = '{region_code}'
                    AND (update_time IS NULL OR update_time <= :cutoff_date)
                """)
                county_connected = conn.execute(county_connected_query, {"cutoff_date": cutoff_date_str}).scalar() or 0
                
                # 计算县区挂接率
                county_rate = round(county_connected / county_total * 100, 2) if county_total > 0 else 0
                results['connection_rates'][frontend_id] = county_rate
        
        return jsonify(results)
        
    except Exception as e:
        logger.error(f"获取指标统计-接口库表资源挂接率数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 发生错误时返回固定数据
        return jsonify({
            "error": str(e),
            "cutoff_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "city": 24.59,
            "connection_rates": {
                'qinan': 42.50,
                'zhangjiachuan': 20.68,
                'qingshui': 10.63,
                'gangu': 11.70,
                'maiji': 19.03,
                'wushan': 16.54,
                'qinzhou': 31.93
            }
        }), 500

@summary_stats_bp.route('/resource_usage', methods=['GET'])
def get_resource_usage_data():
    """获取数据资源申请利用率数据"""
    try:
        # 获取周期偏移参数
        period_offset = request.args.get('periodOffset', 'current')
        
        # 根据周期偏移计算实际的日期范围和截止日期
        _, _, cutoff_date_str = get_period_date_range(period_offset)
        
        # 连接数据库
        engine = get_db_connection()
        
        # 县区英文名映射
        county_english_map = {
            'qinan': 'qin_an',
            'zhangjiachuan': 'zhang_jia_chuan',
            'qingshui': 'qin_shui',
            'gangu': 'gan_gu',
            'maiji': 'mai_ji',
            'wushan': 'wu_shan',
            'qinzhou': 'qin_zhou'
        }
        
        with engine.connect() as conn:
            # 查询各县区的资源申请状态
            county_rates = {}
            for frontend_id, english_name in county_english_map.items():
                try:
                    query = text("""
                        SELECT whether_apply_interface 
                        FROM resource_application_metadata 
                        WHERE english_name = :english_name
                        AND application_time <= :cutoff_date
                        ORDER BY application_time DESC 
                        LIMIT 1
                    """)
                    
                    result = conn.execute(query, {
                        "english_name": english_name,
                        "cutoff_date": cutoff_date_str
                    }).scalar()
                    
                    # 如果whether_apply_interface是1则为100%，否则为0%
                    rate = 100.0 if result == 1 else 0.0
                    county_rates[frontend_id] = rate
                except Exception as e:
                    logger.warning(f"查询{frontend_id}资源申请利用率数据失败: {str(e)}")
                    county_rates[frontend_id] = 0.0
            
            # 查询全市的资源申请率
            try:
                query = text("""
                    SELECT whether_apply_interface 
                    FROM resource_application_metadata 
                    WHERE english_name = 'tss_sz'
                    AND application_time <= :cutoff_date
                    ORDER BY application_time DESC 
                    LIMIT 1
                """)
                
                result = conn.execute(query, {"cutoff_date": cutoff_date_str}).scalar()
                city_rate = 100.0 if result == 1 else 0.0
            except Exception as e:
                logger.warning(f"查询全市资源申请利用率数据失败: {str(e)}")
                city_rate = sum(county_rates.values()) / len(county_rates) if county_rates else 0.0
        
        # 返回结果
        response_data = {
            'cutoff_date': cutoff_date_str,
            'city_rate': city_rate,
            'county_rates': county_rates
        }
        
        return jsonify(response_data)
            
    except Exception as e:
        logger.error(f"获取数据资源申请利用率数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        
        # 发生错误时返回空数据
        return jsonify({
            "error": str(e),
            "cutoff_date": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "city_rate": 0.0,
            "county_rates": {
                'qinan': 0.0,
                'zhangjiachuan': 0.0,
                'qingshui': 0.0,
                'gangu': 0.0,
                'maiji': 0.0,
                'wushan': 0.0,
                'qinzhou': 0.0
            }
        }), 500

# 辅助函数：根据周期偏移计算日期范围
def get_period_date_range(period_offset='current'):
    """
    根据周期偏移计算对应的日期范围
    周期定义: 每周一早上11点到下周一早上11点为一个周期
    
    参数:
        period_offset (str): 周期偏移，'current'表示当前周期，'-1'表示上周期，以此类推
        
    返回:
        tuple: (开始日期, 结束日期, 截止日期) 格式为 'YYYY-MM-DD HH:MM:SS'
    """
    # 当前日期时间
    now = datetime.now()
    
    # 计算当前周期的开始时间（本周一上午11点）
    days_since_monday = now.weekday()  # 周一是0，周日是6
    current_period_start = now - timedelta(days=days_since_monday)
    # 设置为当天上午11点
    current_period_start = current_period_start.replace(hour=11, minute=0, second=0, microsecond=0)
    
    # 如果现在的时间早于周一上午11点，那么当前周期应该是上周一到这周一
    if now < current_period_start:
        current_period_start = current_period_start - timedelta(days=7)
    
    # 当前周期的结束时间（下周一上午11点）
    current_period_end = current_period_start + timedelta(days=7)
    
    # 根据周期偏移值计算实际的日期范围
    try:
        if period_offset == 'current':
            period_start = current_period_start
            period_end = current_period_end
            # 截止日期是当前时间或周期结束时间中较早的那个
            cutoff_date = min(now, period_end)
        else:
            # 转换偏移值为整数
            offset = int(period_offset)
            # 负数表示过去的周期
            period_start = current_period_start + timedelta(days=7 * offset)
            period_end = period_start + timedelta(days=7)
            # 对于过去的周期，截止日期就是周期结束时间
            cutoff_date = period_end
    except ValueError:
        # 如果偏移值无效，使用当前周期
        logger.warning(f"无效的周期偏移值: {period_offset}，使用当前周期")
        period_start = current_period_start
        period_end = current_period_end
        cutoff_date = min(now, period_end)
    
    # 格式化日期为字符串
    start_str = period_start.strftime('%Y-%m-%d %H:%M:%S')
    end_str = period_end.strftime('%Y-%m-%d %H:%M:%S')
    cutoff_str = cutoff_date.strftime('%Y-%m-%d %H:%M:%S')
    
    logger.info(f"周期偏移 {period_offset} 对应的日期范围: {start_str} 到 {end_str}，截止日期: {cutoff_str}")
    return start_str, end_str, cutoff_str

# 辅助函数：将县区名称转换为前端使用的ID
def convert_county_name_to_frontend_id(county_name):
    """将县区名称转换为前端使用的ID"""
    county_mapping = {
        '秦安县': 'qinan',
        '张家川县': 'zhangjiachuan',
        '张家川回族自治县': 'zhangjiachuan',
        '清水县': 'qingshui',
        '甘谷县': 'gangu',
        '麦积区': 'maiji',
        '武山县': 'wushan',
        '秦州区': 'qinzhou'
    }
    return county_mapping.get(county_name)