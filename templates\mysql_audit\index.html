<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MySQL 日志审计系统</title>
    <!-- Bootstrap CSS (确保引入) -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <!-- Font Awesome (用于图标) -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }}">
    <!-- 其他样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='mysql_audit/css/daterangepicker.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='mysql_audit/css/style.css') }}">
    <!-- 确保 JQuery 和 Bootstrap JS 在其他脚本之前加载 -->
    <script src="{{ url_for('static', filename='js/lib/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/lib/bootstrap.bundle.min.js') }}"></script>
    <!-- 其他脚本 -->
    <script src="{{ url_for('static', filename='mysql_audit/js/lib/moment.min.js') }}"></script>
    <script src="{{ url_for('static', filename='mysql_audit/js/lib/daterangepicker.min.js') }}"></script>
    <script src="{{ url_for('static', filename='mysql_audit/js/lib/echarts.min.js') }}"></script>
    <script src="{{ url_for('static', filename='mysql_audit/js/lib/tailwind.min.js') }}"></script>
    <script src="{{ url_for('static', filename='mysql_audit/js/lib/lucide.min.js') }}"></script>
    <style>
        /* 基本字体和背景 */
        body {
            font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
            background-color: #f9fafb; /* bg-gray-50 */
        }
        /* Spinner 动画样式 */
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.2);
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border-left-color: #ffffff;
            animation: spin 1s ease infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        /* 风险等级徽章基础样式 */
        .risk-badge {
            display: inline-block;
            padding: 0.15rem 0.5rem;
            font-size: 0.75rem; /* text-xs */
            font-weight: 500; /* font-medium */
            border-radius: 0.375rem; /* rounded-md */
            line-height: 1.2;
        }
        /* 特定风险等级颜色 */
        .risk-badge-low { background-color: #ecfdf5; color: #065f46; } /* green-100 / green-800 */
        .risk-badge-medium { background-color: #fffbeb; color: #b45309; } /* amber-100 / amber-700 */
        .risk-badge-high { background-color: #fee2e2; color: #991b1b; } /* red-100 / red-800 */
        .risk-badge-unknown { background-color: #f3f4f6; color: #4b5563; } /* gray-100 / gray-600 */
        
        /* 隐藏页脚中的版本号 */
        footer #footerVersion, 
        footer .version-info,
        footer #changelogBtn {
            display: none !important;
        }
        
        /* 修复模态框样式问题 */
        #tailwindChangelogModal {
            z-index: 9999 !important;
        }
        
        /* 确保模态框关闭后遮罩层完全消失 */
        body.modal-open {
            overflow: auto !important;
            padding-right: 0 !important;
        }
        
        /* 防止页面滚动被锁定 */
        html.modal-open, body.modal-open {
            height: auto !important;
            overflow: auto !important;
        }
    </style>
    
    <!-- 添加页面初始化脚本，确保快速隐藏页脚版本信息 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 修改页脚内容，移除版本信息
            var footerSpans = document.querySelectorAll('footer span');
            footerSpans.forEach(function(span) {
                if (span.innerHTML.indexOf('版本') > -1) {
                    span.innerHTML = span.innerHTML.replace(/\|\s*版本\s*.*?<\/span>/, '');
                }
            });
            
            // 确保关闭模态框后页面可以正常交互
            document.addEventListener('click', function(e) {
                // 检查是否是关闭模态框的点击
                if (e.target.closest('#tailwind-changelog-close-icon') || 
                    e.target.closest('#tailwind-changelog-close-button') ||
                    e.target.id === 'tailwindChangelogModal') {
                    
                    // 延迟执行确保模态框完全关闭
                    setTimeout(function() {
                        // 移除任何可能导致页面无法交互的样式
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';
                        document.documentElement.style.overflow = '';
                        
                        // 移除模态框相关类
                        document.body.classList.remove('modal-open');
                        
                        // 确保模态框真正隐藏
                        var modal = document.getElementById('tailwindChangelogModal');
                        if (modal) {
                            modal.style.display = 'none';
                        }
                        
                        // 移除所有模态框背景
                        var modalBackdrops = document.querySelectorAll('.modal-backdrop');
                        modalBackdrops.forEach(function(backdrop) {
                            backdrop.parentNode.removeChild(backdrop);
                        });
                    }, 400);
                }
            });
        });
    </script>
</head>
<body class="bg-gray-50">
    <div class="flex h-screen bg-gray-100">
        <div class="w-64 bg-gray-900 text-gray-300 p-4 flex flex-col shadow-lg">
            <h1 class="text-xl font-semibold mb-6 text-white flex items-center">
                <i data-lucide="shield-check" class="mr-2 h-6 w-6"></i>
                MySQL 日志审计
            </h1>
            <nav>
                <ul>
                    <li class="mb-1"><a href="#" class="flex items-center px-3 py-2 rounded text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white data-[active=true]:bg-gray-700 data-[active=true]:text-white" data-tab="dashboard" data-active="true"><i data-lucide="layout-dashboard" class="mr-3 h-5 w-5"></i>仪表盘</a></li>
                    <li class="mb-1"><a href="#" class="flex items-center px-3 py-2 rounded text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white data-[active=true]:bg-gray-700 data-[active=true]:text-white" data-tab="activities" data-active="false"><i data-lucide="list-checks" class="mr-3 h-5 w-5"></i>操作记录</a></li>
                    <li class="mb-1"><a href="#" class="flex items-center px-3 py-2 rounded text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white data-[active=true]:bg-gray-700 data-[active=true]:text-white" data-tab="reports" data-active="false"><i data-lucide="file-text" class="mr-3 h-5 w-5"></i>报表导出</a></li>
                    <li class="mb-1"><a href="#" class="flex items-center px-3 py-2 rounded text-sm font-medium text-gray-300 hover:bg-gray-700 hover:text-white data-[active=true]:bg-gray-700 data-[active=true]:text-white" data-tab="config" data-active="false"><i data-lucide="settings" class="mr-3 h-5 w-5"></i>系统配置</a></li>
                </ul>
            </nav>
            <div class="mt-auto">
                <div id="scan-indicator" class="text-center my-3 hidden">
                    <div class="spinner mx-auto"></div>
                    <p class="text-sm text-gray-300 mt-2">扫描中...</p>
                </div>
                <button id="scan-logs-btn" class="w-full flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded mb-2 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-900 focus:ring-indigo-500 transition duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed">
                    <i data-lucide="scan-line" class="mr-2 h-4 w-4"></i>扫描日志
                </button>
                <div id="scan-status" class="text-xs text-gray-500 text-center h-4"></div>
            </div>
            
            <!-- 左侧边栏版本信息 -->
            <div class="mt-4 pt-4 border-t border-gray-700 text-xs text-gray-500 text-center">
                <p>2025 数据管理系统</p>
                <p class="mt-1">版本 <span id="sideAppVersion">v4</span>
                    <a href="javascript:void(0)" id="changelogBtn" class="text-indigo-400 hover:text-indigo-300 ml-1">更新日志</a>
                </p>
            </div>
        </div>

        <div class="flex-1 flex flex-col overflow-hidden">
            <header class="bg-white shadow-md border-b border-gray-200 p-4">
                <h2 class="text-lg font-semibold text-gray-800" id="main-content-title">仪表盘</h2>
            </header>
            <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 p-6">
                <div class="bg-white p-5 rounded-lg shadow border border-gray-200 mb-6">
                    <h3 class="text-md font-semibold mb-4 text-gray-700 border-b pb-2">筛选条件</h3>
                    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 items-end">
                        <div class="col-span-1 md:col-span-1 lg:col-span-1">
                            <label for="server-select" class="block text-xs font-medium text-gray-600 mb-1">服务器</label>
                            <select id="server-select" name="server" class="mt-1 block w-full pl-3 pr-10 py-1.5 text-sm border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md shadow-sm">
                                <option value="">全部服务器</option>
                                {# Jinja2 循环生成服务器选项 #}
                                {% for server in servers %}
                                <option value="{{ server.server_id }}">{{ server.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-span-1 sm:col-span-2 md:col-span-1 lg:col-span-2">
                            <label for="daterange" class="block text-xs font-medium text-gray-600 mb-1">时间范围</label>
                            <input type="text" id="daterange" name="daterange" class="mt-1 block w-full pl-3 pr-10 py-1.5 text-sm border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md shadow-sm">
                        </div>
                        <div class="col-span-1">
                            <label for="operation-type-select" class="block text-xs font-medium text-gray-600 mb-1">操作类型</label>
                            <select id="operation-type-select" name="operation_type" class="mt-1 block w-full pl-3 pr-10 py-1.5 text-sm border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md shadow-sm">
                                <option value="">全部类型</option>
                                <option value="SELECT">SELECT (查询)</option>
                                <option value="INSERT">INSERT (插入)</option>
                                <option value="UPDATE">UPDATE (更新)</option>
                                <option value="DELETE">DELETE (删除)</option>
                                <option value="DDL">DDL (表结构)</option>
                                <option value="DCL">DCL (权限)</option>
                                <option value="TCL">TCL (事务)</option>
                                <option value="USE_DB">USE DB (切换库)</option>
                                <option value="CONNECT">CONNECT (连接)</option>
                                <option value="QUIT">QUIT (断开)</option>
                                <option value="OTHER">OTHER (其他)</option>
                            </select>
                        </div>
                        <div class="col-span-1">
                            <label for="risk-level-select" class="block text-xs font-medium text-gray-600 mb-1">风险等级</label>
                             <select id="risk-level-select" name="risk_level" class="mt-1 block w-full pl-3 pr-10 py-1.5 text-sm border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md shadow-sm">
                                <option value="">全部</option>
                                <option value="Low">低危</option>
                                <option value="Medium">中危</option>
                                <option value="High">高危</option>
                            </select>
                        </div>
                        <div class="col-span-1">
                            <label for="username-input" class="block text-xs font-medium text-gray-600 mb-1">用户名</label>
                            <input type="text" id="username-input" name="username" class="mt-1 block w-full pl-3 pr-2 py-1.5 text-sm border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 rounded-md shadow-sm" placeholder="输入...">
                        </div>
                        <div class="col-span-1">
                             <button id="filter-btn" class="w-full inline-flex justify-center items-center px-4 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                                <i data-lucide="search" class="mr-1 h-4 w-4"></i>应用筛选
                            </button>
                        </div>
                    </div>
                </div>

                <div id="content-area">
                    <div id="dashboard-content" class="tab-content">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                            <div class="bg-white p-4 rounded-lg shadow border border-gray-200"><h4 class="font-semibold mb-3 text-gray-700 text-sm border-b pb-2">风险等级分布</h4><div id="risk-levels-chart" style="height: 280px;"></div></div>
                            <div class="bg-white p-4 rounded-lg shadow border border-gray-200"><h4 class="font-semibold mb-3 text-gray-700 text-sm border-b pb-2">操作类型分布</h4><div id="op-types-chart" style="height: 280px;"></div></div>
                            <div class="bg-white p-4 rounded-lg shadow border border-gray-200"><h4 class="font-semibold mb-3 text-gray-700 text-sm border-b pb-2">活跃用户排行</h4><ul id="top-users-list" class="text-xs text-gray-600 mt-2 space-y-1 overflow-y-auto" style="max-height: 250px;"></ul></div>
                            <div class="bg-white p-4 rounded-lg shadow border border-gray-200 md:col-span-2 lg:col-span-3"><h4 class="font-semibold mb-3 text-gray-700 text-sm border-b pb-2">操作次数按小时分布</h4><div id="hourly-chart" style="height: 280px;"></div></div>
                        </div>
                         <div id="stats-container"></div> </div>

                    <div id="activities-content" class="tab-content hidden">
                        <div class="bg-white rounded-lg shadow border border-gray-200 overflow-hidden">
                            <h3 class="text-md font-semibold p-4 text-gray-700 border-b">详细操作记录</h3>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">客户端主机</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">数据库</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作类型</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/3">参数/SQL语句</th>
                                            <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">风险等级</th>
                                        </tr>
                                    </thead>
                                    <tbody id="activities-table-body" class="bg-white divide-y divide-gray-200 text-sm">
                                        <tr><td colspan="8" class="text-center py-10 text-gray-500">加载中...</td></tr>
                                    </tbody>
                                </table>
                            </div>
                            <div id="pagination" class="p-4 border-t"></div> </div>
                    </div>
                    
                    <div id="reports-content" class="tab-content hidden">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- 定期报表 -->
                            <div class="bg-white rounded-lg shadow border border-gray-200 p-6">
                                <h3 class="text-lg font-semibold mb-4 text-gray-700 border-b pb-2">定期报表</h3>
                                <div class="space-y-4">
                                    <button id="daily-report-btn" class="w-full flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition duration-150 ease-in-out">
                                        <i data-lucide="file-text" class="mr-2 h-4 w-4"></i>生成日报
                                    </button>
                                    <button id="weekly-report-btn" class="w-full flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition duration-150 ease-in-out">
                                        <i data-lucide="file-text" class="mr-2 h-4 w-4"></i>生成周报
                                    </button>
                                    <button id="monthly-report-btn" class="w-full flex items-center justify-center bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded transition duration-150 ease-in-out">
                                        <i data-lucide="file-text" class="mr-2 h-4 w-4"></i>生成月报
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 数据导出 -->
                            <div class="bg-white rounded-lg shadow border border-gray-200 p-6">
                                <h3 class="text-lg font-semibold mb-4 text-gray-700 border-b pb-2">数据导出</h3>
                                <div class="space-y-4">
                                    <button id="export-csv-btn" class="w-full flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded transition duration-150 ease-in-out">
                                        <i data-lucide="file-text" class="mr-2 h-4 w-4"></i>导出为CSV
                                    </button>
                                    <button id="export-excel-btn" class="w-full flex items-center justify-center bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded transition duration-150 ease-in-out">
                                        <i data-lucide="file-spreadsheet" class="mr-2 h-4 w-4"></i>导出为Excel
                                    </button>
                                </div>
                                <div class="mt-4">
                                    <p class="text-sm text-gray-500">注：导出数据将包含当前筛选条件下的所有记录</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div id="config-content" class="tab-content hidden">
                        <div class="flex mb-6">
                            <div class="w-full bg-white rounded-lg shadow border border-gray-200 p-4">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-md font-semibold text-gray-700">MySQL服务器配置</h3>
                                    <button id="add-server-btn" class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition duration-150 ease-in-out">
                                        <i data-lucide="plus" class="mr-1 h-4 w-4"></i>添加服务器
                                    </button>
                                </div>
                                <div class="mt-4 overflow-x-auto">
                                    <table class="min-w-full divide-y divide-gray-200">
                                        <thead class="bg-gray-50">
                                            <tr>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">名称</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">主机</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">端口</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">认证</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">日志扫描</th>
                                                <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="servers-table-body" class="bg-white divide-y divide-gray-200 text-sm">
                                            <tr><td colspan="8" class="text-center py-10 text-gray-500">加载中...</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="bg-white rounded-lg shadow border border-gray-200 p-4">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-md font-semibold text-gray-700">风险规则配置</h3>
                                    <div>
                                        <button id="edit-risk-rules-btn" class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                                            <i data-lucide="edit" class="mr-1 h-4 w-4"></i>修改规则
                                        </button>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <div class="grid grid-cols-1 gap-4">
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-700 mb-2">高风险操作</h4>
                                            <div id="high-risk-rules" class="bg-gray-50 p-3 rounded text-sm"></div>
                                        </div>
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-700 mb-2">中风险操作</h4>
                                            <div id="medium-risk-rules" class="bg-gray-50 p-3 rounded text-sm"></div>
                                        </div>
                                        <div>
                                            <h4 class="text-sm font-medium text-gray-700 mb-2">低风险操作</h4>
                                            <div id="low-risk-rules" class="bg-gray-50 p-3 rounded text-sm"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="bg-white rounded-lg shadow border border-gray-200 p-4">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-md font-medium text-gray-700">写入过滤规则</h3>
                                    <div>
                                        <button id="edit-write-levels-btn" class="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition duration-150 ease-in-out">
                                            <i data-lucide="edit" class="mr-1 h-4 w-4"></i>修改规则
                                        </button>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <div class="bg-gray-50 p-4 rounded">
                                        <p class="text-sm text-gray-600 mb-3">当前写入数据库的风险级别：</p>
                                        <div id="write-risk-levels" class="flex gap-2"></div>
                                    </div>
                                    <div class="mt-4 text-sm text-gray-600">
                                        <p>说明：系统只会将符合上述风险级别的操作记录写入数据库，其他级别的操作将被忽略。</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </main>
            
            <!-- 右侧底部版本信息 - 完全移除 -->
            <footer class="bg-white border-t border-gray-200 p-3 text-xs text-center text-gray-500">
                <div class="flex justify-center items-center">
                    <span>2025 数据管理系统</span>
                </div>
            </footer>
        </div>
    </div>

    <div id="details-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center hidden z-50">
        <div class="relative mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-3">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">SQL 语句详情</h3>
                    <button id="details-modal-close-icon" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="h-5 w-5"></i>
                    </button>
                </div>
                <div class="mt-2 px-2 py-3 max-h-80 overflow-y-auto bg-gray-50 rounded">
                    <pre id="details-modal-content" class="text-sm text-gray-800 text-left whitespace-pre-wrap break-all"></pre>
                </div>
                <div class="items-center px-4 py-3 mt-2">
                    <button id="details-modal-close" class="px-4 py-2 bg-gray-600 text-white text-sm font-medium rounded-md w-full shadow-sm hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 服务器表单模态框 -->
    <div id="server-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center hidden z-50">
        <div class="relative mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-3">
                    <h3 id="server-modal-title" class="text-lg leading-6 font-medium text-gray-900">添加服务器</h3>
                    <button id="server-modal-close-icon" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="h-5 w-5"></i>
                    </button>
                </div>
                <form id="server-form">
                    <input type="hidden" id="server-id" name="server_id" value="">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div class="md:col-span-2">
                            <label for="server-name" class="block text-sm font-medium text-gray-700">服务器名称</label>
                            <input type="text" id="server-name" name="name" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" required>
                        </div>
                        <div>
                            <label for="server-host" class="block text-sm font-medium text-gray-700">主机地址</label>
                            <input type="text" id="server-host" name="host" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" required>
                        </div>
                        <div>
                            <label for="server-port" class="block text-sm font-medium text-gray-700">SSH端口</label>
                            <input type="number" id="server-port" name="port" value="22" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" required>
                        </div>
                        <div>
                            <label for="server-user" class="block text-sm font-medium text-gray-700">SSH用户名</label>
                            <input type="text" id="server-user" name="user" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md" required>
                        </div>
                        <div>
                            <label for="server-auth-type" class="block text-sm font-medium text-gray-700">认证方式</label>
                            <select id="server-auth-type" name="auth_type" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm">
                                <option value="password">密码</option>
                                <option value="ssh_key">SSH密钥</option>
                            </select>
                        </div>
                        <div id="password-auth-container">
                            <label for="server-password" class="block text-sm font-medium text-gray-700">SSH密码</label>
                            <input type="password" id="server-password" name="password" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>
                        <div id="ssh-key-auth-container" class="hidden">
                            <label for="server-ssh-key" class="block text-sm font-medium text-gray-700">SSH密钥路径</label>
                            <input type="text" id="server-ssh-key" name="ssh_key_path" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>
                        <div class="md:col-span-2">
                            <label for="server-general-log" class="block text-sm font-medium text-gray-700">General Log路径</label>
                            <input type="text" id="server-general-log" name="general_log_path" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>
                        <div class="md:col-span-2">
                            <label for="server-binlog" class="block text-sm font-medium text-gray-700">Binary Log路径</label>
                            <input type="text" id="server-binlog" name="binlog_path" class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                        </div>
                        <div>
                            <div class="flex items-center">
                                <input type="checkbox" id="server-enable-general" name="enable_general_log" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="server-enable-general" class="ml-2 block text-sm text-gray-700">启用General Log扫描</label>
                            </div>
                        </div>
                        <div>
                            <div class="flex items-center">
                                <input type="checkbox" id="server-enable-binlog" name="enable_binlog" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="server-enable-binlog" class="ml-2 block text-sm text-gray-700">启用Binary Log扫描</label>
                            </div>
                        </div>
                    </div>
                    <div class="mt-5 flex justify-end gap-3">
                        <button type="button" id="server-modal-cancel" class="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md shadow-sm hover:bg-gray-200">取消</button>
                        <button type="submit" id="server-modal-save" class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 风险规则编辑模态框 -->
    <div id="risk-rules-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center hidden z-50">
        <div class="relative mx-auto p-5 border w-11/12 md:w-2/3 lg:w-3/4 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-3">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">编辑风险规则</h3>
                    <button id="risk-rules-modal-close-icon" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="h-5 w-5"></i>
                    </button>
                </div>
                <form id="risk-rules-form">
                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <h4 class="text-md font-medium text-gray-700 mb-2">高风险操作</h4>
                            <div id="high-risk-editor" class="bg-gray-50 p-4 rounded">
                                <div id="high-risk-items"></div>
                                <button type="button" class="add-rule-btn mt-2 text-indigo-600 hover:text-indigo-800 text-sm" data-level="High">+ 添加规则</button>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-md font-medium text-gray-700 mb-2">中风险操作</h4>
                            <div id="medium-risk-editor" class="bg-gray-50 p-4 rounded">
                                <div id="medium-risk-items"></div>
                                <button type="button" class="add-rule-btn mt-2 text-indigo-600 hover:text-indigo-800 text-sm" data-level="Medium">+ 添加规则</button>
                            </div>
                        </div>
                        <div>
                            <h4 class="text-md font-medium text-gray-700 mb-2">低风险操作</h4>
                            <div id="low-risk-editor" class="bg-gray-50 p-4 rounded">
                                <div id="low-risk-items"></div>
                                <button type="button" class="add-rule-btn mt-2 text-indigo-600 hover:text-indigo-800 text-sm" data-level="Low">+ 添加规则</button>
                            </div>
                        </div>
                    </div>
                    <div class="mt-5 flex justify-end gap-3">
                        <button type="button" id="risk-rules-modal-cancel" class="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md shadow-sm hover:bg-gray-200">取消</button>
                        <button type="submit" id="risk-rules-modal-save" class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 写入级别编辑模态框 -->
    <div id="write-levels-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center hidden z-50">
        <div class="relative mx-auto p-5 border w-11/12 md:w-2/3 lg:w-1/2 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex justify-between items-center mb-3">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">编辑写入级别</h3>
                    <button id="write-levels-modal-close-icon" class="text-gray-400 hover:text-gray-600">
                        <i data-lucide="x" class="h-5 w-5"></i>
                    </button>
                </div>
                <form id="write-levels-form">
                    <div class="bg-gray-50 p-4 rounded">
                        <p class="text-sm text-gray-700 mb-3">选择需要写入数据库的风险级别：</p>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <input type="checkbox" id="write-high" name="write_levels" value="High" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="write-high" class="ml-2 block text-sm text-gray-700">高风险 (High)</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="write-medium" name="write_levels" value="Medium" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="write-medium" class="ml-2 block text-sm text-gray-700">中风险 (Medium)</label>
                            </div>
                            <div class="flex items-center">
                                <input type="checkbox" id="write-low" name="write_levels" value="Low" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="write-low" class="ml-2 block text-sm text-gray-700">低风险 (Low)</label>
                            </div>
                        </div>
                    </div>
                    <div class="mt-5 flex justify-end gap-3">
                        <button type="button" id="write-levels-modal-cancel" class="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md shadow-sm hover:bg-gray-200">取消</button>
                        <button type="submit" id="write-levels-modal-save" class="px-4 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">保存</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 确认删除模态框 -->
    <div id="confirm-delete-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center hidden z-50">
        <div class="relative mx-auto p-5 border w-11/12 md:w-1/3 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                    <i data-lucide="alert-triangle" class="h-6 w-6 text-red-600"></i>
                </div>
                <h3 class="text-lg leading-6 font-medium text-gray-900 mt-3">确认删除</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500">
                        确定要删除此服务器配置吗？此操作无法撤销。
                    </p>
                </div>
                <div class="items-center px-4 py-3 mt-2 flex justify-center gap-3">
                    <button id="confirm-delete-cancel" class="px-4 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md shadow-sm hover:bg-gray-200">取消</button>
                    <button id="confirm-delete-confirm" class="px-4 py-2 bg-red-600 text-white text-sm font-medium rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">确认删除</button>
                </div>
            </div>
        </div>
    </div>

    <!-- **** 在这里添加 Bootstrap 更新日志模态框的 include **** -->
    {% include 'changelog_modal.html' %}

    <!-- 添加主要JavaScript文件 -->
    <script src="{{ url_for('static', filename='mysql_audit/js/main.js') }}"></script>

    <!-- 版本信息和更新日志触发脚本 -->
    <script>
      // 使用 window.load 确保所有资源加载完毕
      $(window).on('load', function(){
        // 获取并更新版本号
        fetch('/api/version')
          .then(response => response.json())
          .then(data => {
            $('#sideAppVersion').text(data.version);
          })
          .catch(error => {
            console.error('获取版本信息失败:', error);
          });

        // 更新日志按钮点击事件
        // 使用普通事件监听器以防万一 jQuery 事件有问题
        const changelogButton = document.getElementById('changelogBtn');
        if (changelogButton) {
            changelogButton.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                try {
                    const changelogModalElement = document.getElementById('changelogModal');
                    if (changelogModalElement) {
                        // 确保 Bootstrap Modal 类已加载
                        if (typeof bootstrap !== 'undefined' && bootstrap.Modal) {
                            const changelogModal = bootstrap.Modal.getOrCreateInstance(changelogModalElement);
                            changelogModal.show();
                        } else {
                            console.error('Bootstrap Modal 未定义！');
                            alert('无法加载更新日志功能。');
                        }
                    } else {
                        console.error('无法找到模态框元素 #changelogModal');
                        alert('无法找到更新日志窗口。');
                    }
                } catch (error) {
                    console.error("显示 Bootstrap 模态框时出错:", error);
                    alert("打开更新日志时出错。\n错误: " + error.message);
                }
                return false;
            });
        } else {
             console.error('无法找到更新日志按钮 #changelogBtn');
        }
      });
    </script>

</body>
</html>
