import base64
import os
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

class CryptoUtil:
    """
    加密工具类，用于加密和解密敏感信息
    """
    # 密钥，应该存储在环境变量或配置文件中
    # 在实际生产环境中，不应该硬编码
    SECRET_KEY = "data_center_secret_key_2025"
    
    @classmethod
    def _get_key(cls):
        """
        从密钥生成Fernet密钥
        """
        salt = b'data_center_salt'  # 在生产环境中应该是随机生成并安全存储的
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(cls.SECRET_KEY.encode()))
        return key
    
    @classmethod
    def encrypt(cls, data):
        """
        加密数据
        
        Args:
            data: 要加密的字符串
            
        Returns:
            加密后的字符串
        """
        if not data:
            return data
            
        key = cls._get_key()
        f = Fernet(key)
        encrypted_data = f.encrypt(data.encode())
        return base64.urlsafe_b64encode(encrypted_data).decode()
    
    @classmethod
    def decrypt(cls, encrypted_data):
        """
        解密数据
        
        Args:
            encrypted_data: 加密后的字符串
            
        Returns:
            解密后的原始字符串
        """
        if not encrypted_data:
            return encrypted_data
            
        try:
            key = cls._get_key()
            f = Fernet(key)
            decrypted_data = f.decrypt(base64.urlsafe_b64decode(encrypted_data.encode()))
            return decrypted_data.decode()
        except Exception as e:
            # 解密失败，返回空字符串
            print(f"解密失败: {e}")
            return ""
