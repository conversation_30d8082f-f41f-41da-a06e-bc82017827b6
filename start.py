#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简单启动脚本 - 自动加载 .env 文件
"""

import os

def load_env_file():
    """加载 .env 文件"""
    if os.path.exists('.env'):
        print("📁 加载 .env 配置文件...")
        loaded_count = 0
        with open('.env', 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    os.environ[key.strip()] = value.strip()
                    loaded_count += 1
        print(f"✅ .env 文件加载完成，共加载 {loaded_count} 个配置项")
        return True
    else:
        print("❌ .env 文件不存在")
        print("💡 请按以下步骤创建配置文件：")
        print("   1. 复制配置模板：cp .env.example .env")
        print("   2. 编辑 .env 文件，设置您的数据库密码和其他配置")
        print("   3. 重新运行启动脚本")
        return False

print("🚀 启动应用...")

# 加载配置文件
if not load_env_file():
    print("\n❌ 无法启动应用，配置文件缺失")
    exit(1)

# 验证关键配置是否已加载
required_vars = [
    'DB_HOST', 'DB_PORT', 'DB_USER', 'DB_PASSWORD',
    'DB_NAME_MAIN', 'DB_NAME_MYSQL_LOG', 'DB_NAME_ANSIBLE',
    'SECRET_KEY', 'JWT_SECRET', 'EDIT_PASSWORD_HASH'
]

missing_vars = [var for var in required_vars if not os.environ.get(var)]
if missing_vars:
    print(f"\n❌ 缺少必需的环境变量:")
    for var in missing_vars:
        print(f"   - {var}")
    print("\n💡 请检查 .env 文件是否包含所有必需的配置")
    print("   参考 .env.example 文件了解所需的配置项")
    print("   运行配置验证工具: python -m src.utils.validate_config")
    exit(1)

print("✅ 配置验证通过")

# 启动应用
try:
    from app import app
    print(f"🌐 应用启动成功，访问地址: http://localhost:5100")
    app.run(host='0.0.0.0', port=5100, debug=True)
except Exception as e:
    print(f"\n❌ 应用启动失败: {str(e)}")
    print("💡 请检查数据库连接和其他配置是否正确")
    exit(1)
