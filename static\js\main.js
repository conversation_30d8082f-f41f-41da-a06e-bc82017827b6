let editModal;
let currentId;

// 全局缓存对象
const cache = {
    providers: {},
    orgUnits: {}
};

document.addEventListener('DOMContentLoaded', function() {
    // 初始化模态框实例
    editModal = new bootstrap.Modal(document.getElementById('editModal'));
    
    // 初始化模态框
    const dataModal = document.getElementById('dataModal');
    if (dataModal) {
        window.dataModalInstance = new bootstrap.Modal(dataModal);
    }

    // 获取并更新版本号
    updateAppVersion();

    // 初始化日期选择器
    $('.datepicker').datepicker({
        format: 'yyyy年',
        minViewMode: 'years',
        autoclose: true,
        language: 'zh-CN'
    });

    // 添加页面加载动画
    showPageLoadingAnimation();
    
    // 检查URL参数，如果有add_record=1则自动弹出添加记录窗口
    if (window.location.href.includes('add_record=1') || window.location.pathname.includes('/county/data/entry/add_record_page')) {
        // 延迟一点执行，确保页面加载完成
        setTimeout(() => {
            showAddForm();
        }, 500);
    }

    // 绑定添加新记录按钮的点击事件
    const addRecordBtn = document.getElementById('addRecordBtn');
    if (addRecordBtn) {
        addRecordBtn.addEventListener('click', function(e) {
            showAddForm();
        });
    }
    
    // 绑定数据中台按钮的点击事件
    const dataCenterBtn = document.getElementById('dataCenterBtn');
    if (dataCenterBtn) {
        dataCenterBtn.addEventListener('click', function(e) {
            e.preventDefault();
            window.location.href = '/data_center';
        });
    }

    // 初始化自动完成
    initializeAutocomplete();
    
    // 绑定其他按钮事件
    initializeEventListeners();
    
    // 初始化导航栏下拉菜单
    initializeNavbarDropdowns();

    // 导出数据按钮点击事件
    const exportAllBtn = document.getElementById('exportAllBtn');
    if (exportAllBtn) {
        exportAllBtn.addEventListener('click', function(e) {
            showButtonLoadingAnimation(this);
            window.location.href = '/export_all';
        });
    }
    
    // 数据可视化按钮点击事件
    const visualizationBtn = document.getElementById('visualizationBtn');
    if (visualizationBtn) {
        visualizationBtn.addEventListener('click', function() {
            showButtonLoadingAnimation(this);
            window.location.href = '/visualization';
        });
    }

    // 导出当前数据按钮点击事件
    const exportCurrentBtn = document.getElementById('exportCurrentBtn');
    if (exportCurrentBtn) {
        exportCurrentBtn.addEventListener('click', function(e) {
            showButtonLoadingAnimation(this);
            const currentTable = $('#tableSelect').val();
            const currentProvider = $('#providerSelect').val();
            let url = `/export_current?table=${currentTable}`;
            if (currentProvider) {
                url += `&provider=${encodeURIComponent(currentProvider)}`;
            }
            window.location.href = url;
        });
    }

    // 年度新增统计按钮点击事件
    const yearlyIncrementBtn = document.getElementById('yearlyIncrementBtn');
    if (yearlyIncrementBtn) {
        yearlyIncrementBtn.addEventListener('click', function(e) {
            showButtonLoadingAnimation(this);
            window.location.href = '/yearly_increment';
        });
    }

    // 保存数据按钮点击事件
    const saveDataBtn = document.getElementById('saveData');
    if (saveDataBtn) {
        saveDataBtn.addEventListener('click', saveData);
    }

    // 检查是否处于编辑模式
    if (sessionStorage.getItem('isEditing') === 'true') {
        enterEditMode();
    } else {
        // 确保编辑和删除按钮隐藏
        document.querySelectorAll('.edit-btn, .delete-btn').forEach(btn => {
            btn.style.display = 'none';
        });
        
        // 确保添加新记录按钮可见
        const addRecordBtn = document.getElementById('addRecordBtn');
        if (addRecordBtn) {
            addRecordBtn.style.display = 'inline-block';
        }
        
        // 确保数据可视化按钮可见
        const visualizationBtn = document.getElementById('visualizationBtn');
        if (visualizationBtn) {
            visualizationBtn.style.display = 'inline-block';
        }
        
        // 确保年度增长统计和导出数据按钮在非编辑模式下可见
        const yearlyIncrementBtn = document.getElementById('yearlyIncrementBtn');
        if (yearlyIncrementBtn) {
            yearlyIncrementBtn.style.display = 'inline-block';
        }
        
        const exportAllBtn = document.getElementById('exportAllBtn');
        if (exportAllBtn) {
            exportAllBtn.style.display = 'inline-block';
        }
        
        const exportCurrentBtn = document.getElementById('exportCurrentBtn');
        if (exportCurrentBtn) {
            exportCurrentBtn.style.display = 'inline-block';
        }
    }

    // 添加表格行悬停效果
    addTableRowHoverEffects();

    // 添加卡片悬停效果
    addCardHoverEffects();

    // 显示所有操作按钮，不受编辑模式控制
    showAllActionButtons();

    // 清空缓存按钮
    const refreshCacheBtn = document.getElementById('refreshCacheBtn');
    if (refreshCacheBtn) {
        refreshCacheBtn.addEventListener('click', refreshCache);
    }
});

// 初始化导航栏下拉菜单
function initializeNavbarDropdowns() {
    // 处理二级菜单的鼠标悬停事件
    const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
    
    dropdownSubmenus.forEach(submenu => {
        // 鼠标悬停时显示三级菜单
        submenu.addEventListener('mouseenter', function() {
            const dropdown = this.querySelector('.dropdown-menu');
            if (dropdown) {
                dropdown.style.display = 'block';
            }
        });
        
        // 鼠标离开时隐藏三级菜单
        submenu.addEventListener('mouseleave', function() {
            const dropdown = this.querySelector('.dropdown-menu');
            if (dropdown) {
                dropdown.style.display = 'none';
            }
        });
    });
    
    // 处理移动设备上的点击事件
    const dropdownItems = document.querySelectorAll('.dropdown-submenu > .dropdown-item');
    
    dropdownItems.forEach(item => {
        item.addEventListener('click', function(e) {
            // 阻止链接默认行为
            e.preventDefault();
            e.stopPropagation();
            
            // 获取父级菜单项
            const parent = this.parentElement;
            
            // 获取子菜单
            const submenu = parent.querySelector('.dropdown-menu');
            
            // 切换子菜单的显示状态
            if (submenu) {
                if (submenu.style.display === 'block') {
                    submenu.style.display = 'none';
                } else {
                    submenu.style.display = 'block';
                }
            }
        });
    });
}

// 添加表格行悬停效果
function addTableRowHoverEffects() {
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.3s ease';
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 8px rgba(0,0,0,0.05)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });
}

// 添加卡片悬停效果
function addCardHoverEffects() {
    const cards = document.querySelectorAll('.stats-card, .selector-area, .table-container, .action-buttons');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transition = 'all 0.3s ease';
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = '0 8px 16px rgba(0,0,0,0.1)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '0 4px 12px rgba(0,0,0,0.08)';
        });
    });
}

// 显示页面加载动画
function showPageLoadingAnimation() {
    // 创建加载动画覆盖层
    const loadingOverlay = document.createElement('div');
    loadingOverlay.id = 'loading-overlay';
    loadingOverlay.style.position = 'fixed';
    loadingOverlay.style.top = '0';
    loadingOverlay.style.left = '0';
    loadingOverlay.style.width = '100%';
    loadingOverlay.style.height = '100%';
    loadingOverlay.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
    loadingOverlay.style.display = 'flex';
    loadingOverlay.style.justifyContent = 'center';
    loadingOverlay.style.alignItems = 'center';
    loadingOverlay.style.zIndex = '9999';
    
    // 创建加载动画
    const spinner = document.createElement('div');
    spinner.className = 'loading-spinner';
    spinner.style.width = '50px';
    spinner.style.height = '50px';
    spinner.style.border = '5px solid rgba(0, 0, 0, 0.1)';
    spinner.style.borderRadius = '50%';
    spinner.style.borderTop = '5px solid #3498db';
    spinner.style.animation = 'spin 1s linear infinite';
    
    // 添加加载文本
    const loadingText = document.createElement('div');
    loadingText.textContent = '加载中...';
    loadingText.style.marginLeft = '15px';
    loadingText.style.color = '#3498db';
    loadingText.style.fontWeight = '600';
    
    // 创建加载动画容器
    const loadingContainer = document.createElement('div');
    loadingContainer.style.display = 'flex';
    loadingContainer.style.alignItems = 'center';
    loadingContainer.appendChild(spinner);
    loadingContainer.appendChild(loadingText);
    
    loadingOverlay.appendChild(loadingContainer);
    document.body.appendChild(loadingOverlay);
    
    // 添加旋转动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
    
    // 1秒后移除加载动画
    setTimeout(() => {
        loadingOverlay.style.opacity = '0';
        loadingOverlay.style.transition = 'opacity 0.5s ease';
        setTimeout(() => {
            document.body.removeChild(loadingOverlay);
        }, 500);
    }, 800);
}

// 显示按钮加载动画
function showButtonLoadingAnimation(button) {
    // 保存原始按钮内容
    const originalContent = button.innerHTML;
    
    // 创建加载动画
    const spinner = document.createElement('span');
    spinner.className = 'spinner-border spinner-border-sm';
    spinner.setAttribute('role', 'status');
    spinner.setAttribute('aria-hidden', 'true');
    
    // 清空按钮内容并添加加载动画
    button.innerHTML = '';
    button.appendChild(spinner);
    button.appendChild(document.createTextNode(' 处理中...'));
    
    // 禁用按钮
    button.disabled = true;
    
    // 3秒后恢复按钮状态（如果页面没有跳转）
    setTimeout(() => {
        button.innerHTML = originalContent;
        button.disabled = false;
    }, 3000);
}

// 将自动完成初始化移到单独的函数中
function initializeAutocomplete() {
    const dataProviderInput = $("#data_provider");
    if (dataProviderInput.length) {
        dataProviderInput.autocomplete({
            source: function(request, response) {
                $.getJSON("/get_org_suggestions", {
                    term: request.term
                }, function(data) {
                    console.log("Received suggestions:", data);
                    response(data);
                });
            },
            minLength: 1,
            select: function(event, ui) {
                console.log("Selected item:", ui.item);
                $("#data_provider").val(ui.item.value);
                $("#org_code").val(ui.item.org_code);
                return false;
            },
            response: function(event, ui) {
                console.log("Response received:", ui.content);
            }
        }).autocomplete("instance")._renderItem = function(ul, item) {
            return $("<li>")
                .append(`<div class='ui-menu-item-wrapper'>
                            <div>${item.label}</div>
                            <small class='text-muted'>${item.org_code}</small>
                        </div>`)
                .appendTo(ul);
        };
    }
}

// 初始化其他事件监听器
function initializeEventListeners() {
    // 绑定编辑按钮的点击事件（使用事件委托）
    document.addEventListener('click', function(e) {
        if (e.target.closest('.edit-btn')) {
            const id = e.target.closest('.edit-btn').dataset.id;
            editRow(id);
        }
    });
    
    // 绑定删除按钮的点击事件（使用事件委托）
    document.addEventListener('click', function(e) {
        if (e.target.closest('.delete-btn')) {
            const id = e.target.closest('.delete-btn').dataset.id;
            deleteRecord(id);
        }
    });
    
    // MySQL日志审计按钮点击事件
    const mysqlAuditBtn = document.querySelector('a[href="/mysql_audit"]');
    if (mysqlAuditBtn) {
        mysqlAuditBtn.addEventListener('click', function(e) {
            // 正常导航到MySQL日志审计页面
        });
    }
    
    // Ansible管理按钮点击事件
    const ansibleBtn = document.querySelector('a[href="/ansible/"]');
    if (ansibleBtn) {
        ansibleBtn.addEventListener('click', function(e) {
            // 正常导航到Ansible管理页面
        });
    }

    // 添加表格行动画效果
    animateTableRows();
}

// 添加表格行动画效果
function animateTableRows() {
    const rows = document.querySelectorAll('tbody tr');
    rows.forEach((row, index) => {
        row.style.opacity = '0';
        row.style.transform = 'translateY(20px)';
        row.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        
        setTimeout(() => {
            row.style.opacity = '1';
            row.style.transform = 'translateY(0)';
        }, 50 * index); // 错开动画时间
    });
}

// 显示添加表单
function showAddForm() {
    const editModal = new bootstrap.Modal(document.getElementById('editModal'));
    $('#modalTitle').text('添加新记录');
    
    // 获取当前选中的表名
    const currentTable = $('#tableSelect').val();
    
    // 获取表字段并生成表单
    $.get(`/get_table_fields/${currentTable}`, function(fields) {
        const container = $('#dynamicFields');
        container.empty();
        
        // 遍历字段生成表单
        fields.forEach(field => {
            let input;
            switch(field.name) {
                case 'data_provider':
                    // 数据单位（带自动完成）
                    input = `<input type="text" class="form-control" id="data_provider" name="data_provider" required>`;
                    setTimeout(() => initUnitAutocomplete(), 100);
                    break;
                case 'org_code':
                    // 统一社会信用代码（自动填充）
                    input = `<input type="text" class="form-control" id="org_code" name="org_code" readonly>`;
                    break;
                case 'provide_time':
                    // 提供时间（日期选择器）
                    input = `<input type="date" class="form-control" id="provide_time" name="provide_time" required>`;
                    break;
                case 'is_pushed':
                case 'is_imported':
                    // 是否推送/是否入湖（下拉选择）
                    input = `
                        <select class="form-select" id="${field.name}" name="${field.name}">
                            <option value="0">否</option>
                            <option value="1">是</option>
                        </select>
                    `;
                    break;
                default:
                    // 其他文本字段
                    input = `<input type="text" class="form-control" id="${field.name}" name="${field.name}" required>`;
            }
            
            container.append(`
                <div class="mb-3">
                    <label class="form-label">${field.comment}</label>
                    ${input}
                </div>
            `);
        });
        
        // 设置默认的当前时间
        const now = new Date().toISOString().slice(0, 10);
        $('#provide_time').val(now);
    });
    
    editModal.show();
}

// 验证密码 - 已不再需要，系统默认授予编辑权限
function verifyPassword() {
    // 直接进入编辑模式
    enterEditMode();
    // 显示成功提示
    showToast('验证成功', '已进入编辑模式', 'success');
}

// 显示提示消息
function showToast(title, message, type) {
    // 创建toast元素
    const toastContainer = document.createElement('div');
    toastContainer.style.position = 'fixed';
    toastContainer.style.top = '20px';
    toastContainer.style.right = '20px';
    toastContainer.style.zIndex = '9999';
    
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');
    
    const toastContent = `
        <div class="d-flex">
            <div class="toast-body">
                <strong>${title}</strong>: ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    toast.innerHTML = toastContent;
    toastContainer.appendChild(toast);
    document.body.appendChild(toastContainer);
    
    // 显示toast
    const bsToast = new bootstrap.Toast(toast, { delay: 3000 });
    bsToast.show();
    
    // 自动移除toast元素
    toast.addEventListener('hidden.bs.toast', function() {
        document.body.removeChild(toastContainer);
    });
}

// 进入编辑模式
function enterEditMode() {
    // 更改按钮文本
    const toggleEditModeBtn = document.getElementById('toggleEditModeBtn');
    if (toggleEditModeBtn) {
        toggleEditModeBtn.innerHTML = '<i class="fas fa-sign-out-alt me-2"></i>退出编辑模式';
    }
    
    // 显示编辑相关按钮
    document.querySelectorAll('.edit-btn, .delete-btn').forEach(btn => {
        btn.style.display = 'inline-block';
    });
    
    // 显示添加新记录按钮
    const addRecordBtn = document.getElementById('addRecordBtn');
    if (addRecordBtn) {
        addRecordBtn.style.display = 'inline-block';
    }
    
    // 显示导出按钮
    const exportAllBtn = document.getElementById('exportAllBtn');
    if (exportAllBtn) {
        exportAllBtn.style.display = 'inline-block';
    }
    
    const exportCurrentBtn = document.getElementById('exportCurrentBtn');
    if (exportCurrentBtn) {
        exportCurrentBtn.style.display = 'inline-block';
    }
    
    // 显示年度新增统计按钮
    const yearlyIncrementBtn = document.getElementById('yearlyIncrementBtn');
    if (yearlyIncrementBtn) {
        yearlyIncrementBtn.style.display = 'inline-block';
    }
    
    // 设置编辑模式标志
    sessionStorage.setItem('isEditing', 'true');
    
    // 移除成功提示，不再显示右上角的提示框
    // showToast('编辑模式已启用', '您现在可以编辑和管理数据记录。', 'success');
}

// 退出编辑模式
function exitEditMode() {
    // 更改按钮文本
    const toggleEditModeBtn = document.getElementById('toggleEditModeBtn');
    if (toggleEditModeBtn) {
        toggleEditModeBtn.innerHTML = '<i class="fas fa-edit me-2"></i>编辑模式';
    }
    
    // 隐藏编辑相关按钮
    document.querySelectorAll('.edit-btn, .delete-btn').forEach(btn => {
        btn.style.display = 'none';
    });
    
    // 确保添加新记录按钮在退出编辑模式时保持可见
    const addRecordBtn = document.getElementById('addRecordBtn');
    if (addRecordBtn) {
        addRecordBtn.style.display = 'inline-block';
    }
    
    // 确保年度增长统计和导出数据按钮在退出编辑模式时保持可见
    // 不再隐藏导出按钮
    const exportAllBtn = document.getElementById('exportAllBtn');
    if (exportAllBtn) {
        exportAllBtn.style.display = 'inline-block';
    }
    
    const exportCurrentBtn = document.getElementById('exportCurrentBtn');
    if (exportCurrentBtn) {
        exportCurrentBtn.style.display = 'inline-block';
    }
    
    // 不再隐藏年度新增统计按钮
    const yearlyIncrementBtn = document.getElementById('yearlyIncrementBtn');
    if (yearlyIncrementBtn) {
        yearlyIncrementBtn.style.display = 'inline-block';
    }
    
    // 移除编辑模式标志
    sessionStorage.removeItem('isEditing');
    
    // 显示成功提示
    showToast('已退出编辑模式', '您已退出编辑模式，部分功能已被隐藏。', 'info');
}

// 切换表格
function changeTable(tableName) {
    // 使用新的入口点URL
    window.location.href = '/county/data/entry?table=' + encodeURIComponent(tableName) + '&page=1';
}

// 切换数据提供者
function changeProvider(provider, orgCode) {
    const currentTable = document.getElementById('tableSelect').value;
    if (provider === "" || provider === "全部") {
        window.location.href = `/county/data/entry?table=${encodeURIComponent(currentTable)}`;
    } else {
        window.location.href = `/county/data/entry?table=${encodeURIComponent(currentTable)}&provider=${encodeURIComponent(provider)}&org_code=${encodeURIComponent(orgCode || '')}`;
    }
}

// 分页切换函数
function changePage(page) {
    const urlParams = new URLSearchParams(window.location.search);
    const currentTable = urlParams.get('table') || document.getElementById('tableSelect').value;
    const currentProvider = urlParams.get('provider');
    const currentOrgCode = urlParams.get('org_code');
    
    let url = `/county/data/entry?table=${encodeURIComponent(currentTable)}&page=${page}`;
    if (currentProvider) {
        url += `&provider=${encodeURIComponent(currentProvider)}`;
    }
    if (currentOrgCode) {
        url += `&org_code=${encodeURIComponent(currentOrgCode)}`;
    }
    window.location.href = url;
}

// 编辑记录函数
function editRow(id) {
    currentId = id;
    const currentTable = new URLSearchParams(window.location.search).get('table') || document.getElementById('tableSelect').value;
    const container = $('#dynamicFields');
    container.empty(); // Clear previous fields

    // 1. Fetch fields first to get comments (labels)
    fetch(`/get_table_fields/${currentTable}`)
        .then(response => response.json())
        .then(fields => {
            const fieldMap = {}; // Store fields by name for easy lookup
            fields.forEach(field => {
                fieldMap[field.name] = field; // Store the whole field object
                let input;
                // Generate input fields based on field type (similar to openEditModal)
                switch(field.name) {
                    case 'data_provider':
                        input = `<input type="text" class="form-control" id="${field.name}" name="${field.name}" required>`;
                        setTimeout(() => initUnitAutocomplete(), 100);
                        break;
                    case 'org_code':
                        input = `<input type="text" class="form-control" id="${field.name}" name="${field.name}" readonly>`;
                        break;
                    case 'provide_time':
                        input = `<input type="date" class="form-control" id="${field.name}" name="${field.name}" required>`;
                        break;
                    case 'is_pushed':
                    case 'is_imported':
                        input = `
                            <select class="form-select" id="${field.name}" name="${field.name}">
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                        `;
                        break;
                    default:
                        input = `<input type="text" class="form-control" id="${field.name}" name="${field.name}" required>`;
                }
                container.append(`
                    <div class="mb-3">
                        <label class="form-label">${field.comment}</label> <!-- Use field.comment for label -->
                        ${input}
                    </div>
                `);
            });

            // 2. Fetch record data after form structure is built
            return fetch(`/county/data/records/${id}?table=${currentTable}`);
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // 3. Populate form fields with fetched data
            Object.keys(data).forEach(key => {
                const input = document.getElementById(key);
                if (input) {
                    if (key === 'provide_time' && data[key]) {
                         try {
                            // Ensure date is in YYYY-MM-DD format for the input type="date"
                            input.value = new Date(data[key]).toISOString().split('T')[0];
                        } catch (e) {
                            input.value = data[key]; // Fallback if date parsing fails
                        }
                    } else if (input.tagName === 'SELECT') {
                         // Handle select elements specifically for boolean flags
                        input.value = (data[key] == 1 || data[key] === '1' || data[key] === true) ? '1' : '0';
                    } else {
                        input.value = data[key];
                    }
                }
            });

             // 4. Show the modal
            const editModalInstance = new bootstrap.Modal(document.getElementById('editModal')); // Renamed variable
            document.getElementById('modalTitle').textContent = '编辑记录';
            editModalInstance.show();
        })
        .catch(error => {
            console.error('Error fetching or populating edit data:', error);
            showToast('错误', '加载编辑数据失败: ' + error.message, 'danger');
        });
}

// 删除记录函数
function deleteRecord(id) {
    const table = new URLSearchParams(window.location.search).get('table') || document.getElementById('tableSelect').value;
    if (confirm('确定要删除这条记录吗？')) {
        showToast('处理中', '正在删除记录...', 'info');
        fetch(`/county/data/records/${id}?table=${table}`, {
            method: 'DELETE',
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('删除失败');
            }
            return response.json();
        })
        .then(result => {
            if (result.success) {
                // 显示成功提示
                showToast('成功', '记录已删除', 'success');
                
                // 直接刷新页面，不需要调用refresh_cache
                location.reload();
            } else {
                showToast('错误', result.error || '删除失败', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('错误', '删除失败：' + error.message, 'danger');
        });
    }
}

// 打开添加/编辑模态框
function openEditModal(id = null) {
    currentId = id;
    // Clear the form
    document.getElementById('editForm').reset();
    const formFields = document.getElementById('formFields');
    formFields.innerHTML = ''; // Clear previous fields
    const currentTable = document.getElementById('tableSelect').value;

    // Fetch fields for the current table
    fetch(`/get_table_fields/${currentTable}`)
        .then(response => response.json())
        .then(fields => {
            fields.forEach(field => {
                let input;
                switch(field.name) {
                    case 'data_provider':
                        // 数据单位（带自动完成）
                        input = `<input type="text" class="form-control" id="${field.name}" name="${field.name}" required>`;
                        break;
                    case 'org_code':
                        // 统一社会信用代码（自动填充）
                        input = `<input type="text" class="form-control" id="${field.name}" name="${field.name}" readonly>`;
                        break;
                    case 'provide_time':
                        // 提供时间（日期选择器）
                        input = `<input type="date" class="form-control" id="${field.name}" name="${field.name}" required>`;
                        break;
                    case 'is_pushed':
                    case 'is_imported':
                        // 是否推送/是否入湖（下拉选择）
                        input = `
                            <select class="form-select" id="${field.name}" name="${field.name}">
                                <option value="0">否</option>
                                <option value="1">是</option>
                            </select>
                        `;
                        break;
                    default:
                        // 其他文本字段
                        input = `<input type="text" class="form-control" id="${field.name}" name="${field.name}" required>`;
                }
                
                formFields.innerHTML += `
                    <div class="mb-3">
                        <label class="form-label">${field.comment}</label>
                        ${input}
                    </div>
                `;
            });

            if (id) {
                // Fetch record data if editing
                fetch(`/county/data/records/${id}?table=${currentTable}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.error) {
                            console.error('Error:', data.error);
                            showToast('错误', data.error, 'danger');
                        } else {
                            Object.keys(data).forEach(key => {
                                const input = document.getElementById(key);
                                if (input) {
                                    if (key === 'provide_time') {
                                        input.value = data[key].split(' ')[0];
                                    } else {
                                        input.value = data[key];
                                    }
                                }
                            });
                        }
                    });
            } else {
                // 添加模式：设置默认值
                const now = new Date().toISOString().slice(0, 10);
                document.getElementById('provide_time').value = now;
            }
        });
    
    const editModal = new bootstrap.Modal(document.getElementById('editModal'));
    editModal.show();
}

// 保存数据函数
function saveData() {
    const form = document.getElementById('editForm');
    if (!form || form.tagName !== 'FORM') {
        console.error("Save button clicked but editForm is not a valid form element.");
        showToast('错误', '无法找到编辑表单，请联系管理员。', 'danger');
        return;
    }
    const formData = new FormData(form);
    const data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });
    
    // 添加当前选择的表名
    data['table'] = document.getElementById('tableSelect').value;
    
    const url = currentId ? `/county/data/records/${currentId}` : '/county/data/records';
    const method = currentId ? 'PUT' : 'POST';
    
    // 显示保存中的提示
    showToast('提示', '正在保存数据...', 'info');
    
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(result => {
        if (result.success) {
            // 关闭模态框
            const editModalInstance = bootstrap.Modal.getInstance(document.getElementById('editModal'));
            if (editModalInstance) {
                 editModalInstance.hide();
            }
            
            // 显示成功提示
            showToast('成功', '数据已保存', 'success');
            
            // 获取当前表格，并显式重定向到该表格的页面，保留筛选条件（如果需要）
            // const currentTable = document.getElementById('tableSelect').value;
            // // 可选：保留其他筛选条件如 provider, org_code, page (从当前 URL 获取)
            // const urlParams = new URLSearchParams(window.location.search);
            // const currentPage = urlParams.get('page') || '1'; // 保持在当前页或第一页
            // const currentProvider = urlParams.get('provider');
            // const currentOrgCode = urlParams.get('org_code');
            // let redirectUrl = `/county/data/entry?table=${encodeURIComponent(currentTable)}&page=${currentPage}`;
            // if (currentProvider) redirectUrl += `&provider=${encodeURIComponent(currentProvider)}`;
            // if (currentOrgCode) redirectUrl += `&org_code=${encodeURIComponent(currentOrgCode)}`;
            // window.location.href = redirectUrl; 
            
            // 简单地重新加载当前页面，依赖于浏览器保持URL参数
            // 如果上面显式重定向的方式解决了问题，可以用它替换下面的 reload
            location.reload(); 

        } else {
            throw new Error(result.error || '未知错误');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('错误', '保存失败: ' + error.message, 'danger');
    });
}

function exportAllData() {
    window.location.href = '/export_all';
}

function exportCurrentData() {
    const currentTable = $('#tableSelect').val();
    const currentProvider = $('#providerSelect').val();
    window.location.href = '/export_current?table=' + currentTable + 
        (currentProvider ? '&provider=' + currentProvider : '');
}

// 刷新缓存函数
function refreshCache() {
    const currentTable = document.getElementById('tableSelect').value;
    
    // 显示加载动画
    showToast('提示', '正在刷新数据...', 'info');
    
    // 直接刷新页面，不需要调用refresh_cache接口
    location.reload();
}

// 初始化单位自动完成
function initUnitAutocomplete() {
    $("#data_provider").autocomplete({
        source: function(request, response) {
            const term = request.term;
            
            // 检查缓存
            if (cache.orgUnits[term]) {
                console.log("Using cached results for", term);
                response(cache.orgUnits[term]);
                return;
            }
            
            // 缓存未命中，发送请求
            $.getJSON("/search_org_units", {
                term: term
            }, function(data) {
                // 缓存结果
                const mappedData = $.map(data, function(item) {
                    return {
                        label: item.value,
                        value: item.value,
                        org_code: item.org_code
                    };
                });
                
                // 存入缓存
                cache.orgUnits[term] = mappedData;
                
                // 返回结果
                response(mappedData);
            });
        },
        minLength: 1,
        select: function(event, ui) {
            // 自动填充组织机构代码
            $("#org_code").val(ui.item.org_code);
        }
    });
}

// 格式化日期
function formatDateForInput(dateStr) {
    if (!dateStr) return '';
    // 尝试解析日期字符串
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) return dateStr;
    // 返回 YYYY-MM-DD 格式
    return date.toISOString().split('T')[0];
}

// 获取并更新应用版本号
function updateAppVersion() {
    const versionElement = document.getElementById('appVersion');
    if (versionElement) {
        fetch('/api/version')
            .then(response => response.json())
            .then(data => {
                versionElement.textContent = data.version;
            })
            .catch(error => {
                console.error('获取版本信息失败:', error);
            });
    }
}

// 显示所有操作按钮，不受编辑模式控制
function showAllActionButtons() {
    // 显示所有编辑按钮
    document.querySelectorAll('.edit-btn, .delete-btn').forEach(btn => {
        btn.style.display = 'inline-block';
    });
    
    // 显示添加新记录按钮
    const addRecordBtn = document.getElementById('addRecordBtn');
    if (addRecordBtn) {
        addRecordBtn.style.display = 'inline-block';
    }
    
    // 显示导出按钮
    const exportAllBtn = document.getElementById('exportAllBtn');
    if (exportAllBtn) {
        exportAllBtn.style.display = 'inline-block';
    }
    
    const exportCurrentBtn = document.getElementById('exportCurrentBtn');
    if (exportCurrentBtn) {
        exportCurrentBtn.style.display = 'inline-block';
    }
    
    // 显示年度新增统计按钮
    const yearlyIncrementBtn = document.getElementById('yearlyIncrementBtn');
    if (yearlyIncrementBtn) {
        yearlyIncrementBtn.style.display = 'inline-block';
    }
    
    // 设置编辑模式标志（用于保持与现有代码的兼容性）
    sessionStorage.setItem('isEditing', 'true');
}
