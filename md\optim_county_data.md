# src/county_data/ 模块优化思路

## 1. 总体目标

对 `src/county_data/` 目录下的代码进行重构，旨在减少文件数量，提高模块化程度，明确各模块职责，降低耦合，并提升代码的可读性和可维护性。核心思路是整合路由、分离服务、并统一数据库访问方式。

## 2. 主要优化策略与文件重组

### 2.1. 待删除或职责转移的文件

*   **`indicator_data_api.py`**: 内容为空，直接删除。
*   **`run_county_data.py`**: 其通过 `store_data.main()` 处理 `moban.xlsx` 的功能，应转为独立的管理脚本（如Flask CLI命令 `flask process-template-excel`）或在应用内部按需触发，不再作为模块内的常规运行文件。脚本本身可移至项目根目录的 `scripts/` 文件夹或删除（如果功能完全由其他方式替代）。
*   **`store_data.py`**:
    *   其包装函数将逐步被淘汰，项目中其他代码直接调用新的服务类。
    *   其 `main()` 函数的逻辑（处理 `moban.xlsx`）将按上述 `run_county_data.py` 的方式处理。
    *   最终目标是移除此文件。
*   **`db_manager.py`**:
    *   其通用的数据库连接和操作功能，将完全由项目级的 `src/utils/database_helpers.py` 替代。
    *   其特有的业务逻辑（如针对 `excel_data_{区县代码}` 表的DDL、数据备份逻辑等）将移至新的 `DataPersistenceService`。
    *   最终目标是移除此文件。

### 2.2. 路由层整合

创建一个统一的路由文件（或少数几个按主要功能划分的路由文件），例如：

*   **`data_management_routes.py`** (可由 `data_routes.py` 重命名或作为新文件创建)
    *   **合并来源**: `data_routes.py` (核心数据增删改查)、`export_routes.py` (数据导出)、`file_uploader.py` (文件上传及后台处理)。
    *   **功能**: 统一处理所有与数据生命周期管理相关的HTTP请求，包括数据的CRUD、Excel导入（带异步处理、状态查询、日志查看）、Excel导出。
    *   **蓝图**: 定义并注册一个核心蓝图，如 `data_management_bp`。

*   **`metrics_routes.py`** (可由 `metrics_stats_routes.py` 重命名或作为新文件创建)
    *   **合并来源**: `metrics_stats_routes.py`。
    *   **功能**: 处理所有与自定义指标统计、计算、展示相关的HTTP请求。
    *   **蓝图**: 定义并注册一个指标蓝图，如 `metrics_bp`。

*   **保留的独立路由文件** (如果其功能足够独立和复杂):
    *   `data_visualization_routes.py`
    *   `summary_stats_routes.py`
    *   `table_connection_rate.py`
    *   `data_display.py`
    *   (未来可评估这些模块是否也能进一步整合或其部分API归入更大的路由模块)

### 2.3. 服务层抽象与创建

将核心业务逻辑从路由处理函数中剥离出来，形成独立的服务层。

*   **`ExcelProcessingService`** (或在现有 `excel_processor.py` 基础上强化)
    *   **来源**: `excel_processor.py` 的核心Excel解析逻辑。
    *   **职责**: 负责Excel文件的读取、解析、数据清洗、格式转换。返回结构化的数据对象给调用方。不直接进行数据库操作。

*   **`DataPersistenceService`** (新建服务类)
    *   **来源**: `db_manager.py` 中的业务相关数据库操作（如特定表结构的创建与管理），`store_data.py` 中关于数据入库的协调逻辑，以及 `file_uploader.py` 中实际将数据写入数据库的部分。
    *   **职责**: 负责将处理好的数据持久化到数据库。包括：
        *   根据业务规则创建和管理 `excel_data_{区县}` 等相关数据表。
        *   提供数据批量插入、更新、删除、清空等接口。
        *   （可选）数据库备份的相关接口，如果业务需要此模块触发。
    *   **依赖**: 完全依赖 `src/utils/database_helpers.py` 执行底层的数据库交互。

*   **`MetricsCalculationService`** (或在现有 `metrics_calculator.py` 基础上强化)
    *   **来源**: `metrics_calculator.py` 的核心计算逻辑。`metrics_config.py` 中的默认配置（如权重）可以直接整合到此类中，或通过应用配置注入。
    *   **职责**: 封装所有自定义指标的计算逻辑。接收必要参数，返回计算结果。

*   **`DataExportService`** (新建服务类，或逻辑保留在 `data_management_routes.py` 中作为辅助类/函数)
    *   **来源**: `export_routes.py` 中复杂的Excel文件构建逻辑及相关的特定汇总SQL查询。
    *   **职责**: 根据请求参数从数据库提取数据（可能通过 `DataPersistenceService` 或直接查询），并构建符合要求的Excel文件。

### 2.4. 更新模块初始化文件

*   **`src/county_data/__init__.py`**:
    *   移除对已删除或合并模块的导入。
    *   更新蓝图的注册，以反映路由文件的整合和新蓝图的定义（如 `data_management_bp`, `metrics_bp`）。
    *   按需导出新的服务类接口，如果它们需要在 `county_data` 模块外部被直接调用。

## 3. 预期效果

*   `src/county_data/` 目录下的文件数量显著减少。
*   路由层更轻量，主要负责请求分发和调用服务。
*   服务层封装核心业务逻辑，易于测试和复用。
*   数据库操作统一通过 `src/utils/database_helpers.py` 和 `DataPersistenceService`，职责清晰。
*   整体代码结构更清晰，模块职责更明确，有助于长期维护和扩展。