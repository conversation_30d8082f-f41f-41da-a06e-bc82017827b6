import openpyxl
from sqlalchemy import text # Added for process_excel_data if it uses text

# Note: process_excel_data may need 'engine' if it interacts directly with the DB
# or it should be refactored to return data to be processed by another function.
# For now, assuming it might need 'text' for potential direct DB interactions.

def get_merged_cell_value(sheet, row, col):
    """获取单元格的值，处理合并单元格的情况"""
    cell = sheet.cell(row=row, column=col)
    
    # 如果单元格有值，直接返回
    if cell.value is not None:
        return cell.value
        
    # 检查这个单元格是否在合并区域内
    for merged_range in sheet.merged_cells.ranges:
        if cell.coordinate in merged_range:
            # 获取合并区域的第一个单元格的值
            return sheet.cell(row=merged_range.min_row, column=merged_range.min_col).value
            
    return None

def process_excel_data(file_path, table_name, engine):
    """处理Excel文件数据，正确处理合并单元格和空值"""
    try:
        wb = openpyxl.load_workbook(file_path)
        sheet = wb.active
        
        # 获取表头
        headers = []
        for col in range(1, sheet.max_column + 1):
            header = get_merged_cell_value(sheet, 1, col)
            if header:
                headers.append(header)
        
        # 处理数据行
        data = []
        current_values = {}  # 用于存储当前有效的非空值
        
        for row in range(2, sheet.max_row + 1):
            row_data = {}
            
            for col_idx, header in enumerate(headers, 1): # Corrected col to col_idx
                value = get_merged_cell_value(sheet, row, col_idx) # Corrected col to col_idx
                
                # 如果是关键字段且为空，使用上一个非空值
                if header in ['data_provider', 'chinese_name', 'org_code'] and value is None:
                    value = current_values.get(header)
                else:
                    # 更新当前值
                    if value is not None: # Only update if there's a new non-empty value
                        current_values[header] = value
                
                row_data[header] = value
            
            # 只有当必要字段都有值时才添加行
            if row_data.get('data_provider') and row_data.get('org_code'):
                data.append(row_data)
        
        # 构建插入语句
        if data:
            # Filter out rows where essential keys might still be None if they weren't in current_values initially
            # and ensure all headers exist in the first valid row_data for column names.
            valid_data = []
            final_columns = []
            if data:
                # Attempt to get columns from the first row that has them
                first_valid_row = next((r for r in data if r.get('data_provider') and r.get('org_code')), None)
                if first_valid_row:
                    final_columns = list(first_valid_row.keys()) 
                else: # No valid data to insert
                    return True, "数据导入成功 (无有效数据行)"

            for r_data in data:
                # Ensure all necessary columns are present, fill with None if not.
                # This step is more about ensuring uniform structure for DB insertion
                # if some rows lack certain non-essential headers found in others.
                processed_r_data = {col: r_data.get(col) for col in final_columns}
                if processed_r_data.get('data_provider') and processed_r_data.get('org_code'):
                     valid_data.append(processed_r_data)
            
            if not valid_data:
                 return True, "数据导入成功 (无有效数据行)"

            # Use final_columns derived from data that will be inserted
            placeholders = ', '.join([':' + col for col in final_columns])
            insert_query_sql = f"""
            INSERT INTO `{table_name}` ({ ', '.join(f'`{col}`' for col in final_columns) })
            VALUES ({placeholders})
            """ # Renamed variable to avoid conflict
            insert_query = text(insert_query_sql)
            
            # 执行插入
            with engine.connect() as conn:
                for row_to_insert in valid_data: # Corrected variable name
                    conn.execute(insert_query, row_to_insert)
                conn.commit()
                
        return True, "数据导入成功"

    except Exception as e:
        print(f"Error in process_excel_data for table {table_name}: {str(e)}")
        import traceback
        traceback.print_exc() # For more detailed error logging
        return False, str(e) 