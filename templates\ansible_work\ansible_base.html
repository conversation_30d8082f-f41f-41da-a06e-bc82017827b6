<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Ansible 中台{% endblock %}</title>
    <!-- Ansible 模块专属 CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap-icons.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/all.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/ansible/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/ansible/ansible-result.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/ansible/sidebar.css') }}">
    {% block styles %}{% endblock %}
</head>
<body id="ansible-module-body">
    <div class="sidebar-layout">
        <!-- 左侧导航栏 -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <a class="navbar-brand" href="{{ url_for('ansible_work.index') }}"><i class="bi bi-hdd-network me-2"></i>Ansible 中台</a>
            </div>
            <div class="sidebar-nav">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link {% if request.endpoint == 'ansible_work.index' %}active{% endif %}" href="{{ url_for('ansible_work.index') }}#servers"><i class="bi bi-server"></i>服务器管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('ansible_work.index') }}#tasks"><i class="bi bi-list-task"></i>任务管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('ansible_work.index') }}#playbooks"><i class="bi bi-file-earmark-code"></i>Playbook管理</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('ansible_work.index') }}#file-manager"><i class="bi bi-folder"></i>文件管理</a>
                    </li>
                    <!-- 可以添加返回主应用的链接 -->
                    <li class="nav-item mt-auto border-top pt-2">
                         <a class="nav-link" href="{{ url_for('core_bp.dashboard') }}"><i class="bi bi-arrow-left-square"></i> 返回主系统</a>
                    </li>
                </ul>
            </div>
        </nav>

        <!-- 移动设备导航切换按钮 -->
        <button class="sidebar-toggle d-md-none">
            <i class="bi bi-list"></i>
        </button>

        <!-- 主要内容区域 -->
        <div class="content-wrapper">
            {% block content %}
            <!-- 子模板的内容将在这里填充 -->
            {% endblock %}

            <!-- 版本信息页脚 -->
            <footer class="mt-5 text-center text-muted">
                <p><small>2025 数据管理系统 | 版本 <span id="appVersion">v4</span></small></p>
            </footer>
        </div>
    </div>

    <!-- 通用模态框等可以放在这里 -->
    {% include 'ansible_work/_modals.html' %}

    <!-- Ansible 模块专属 JS -->
    <script src="{{ url_for('static', filename='js/lib/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/lib/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ansible/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ansible/file-manager.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ansible/sidebar.js') }}"></script>
    <script src="{{ url_for('static', filename='js/ansible/file-transfer-manager.js') }}"></script>
    {% block scripts %}{% endblock %}

    <!-- 版本信息脚本 -->
    <script>
      $(function(){
        // 获取并更新版本号
        fetch('/api/version')
          .then(response => response.json())
          .then(data => {
            $('#appVersion').text(data.version);
          })
          .catch(error => {
            console.error('获取版本信息失败:', error);
          });
      });
    </script>
</body>
</html> 