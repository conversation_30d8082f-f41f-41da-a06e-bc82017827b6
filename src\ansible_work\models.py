from src.utils.extensions import db  # 从主应用导入 db
from datetime import datetime
# Import association_proxy
from sqlalchemy.ext.associationproxy import association_proxy

# Remove the explicit definition of the association table.
# SQLAlchemy will create it implicitly based on the relationship's secondary argument.
# server_group_mappings = db.Table(
#     'server_group_mappings',
#     db.metadata, 
#     db.Column('server_id', db.Integer, db.<PERSON>ey('servers.id'), primary_key=True),
#     db.Column('group_id', db.Integer, db.<PERSON>('server_groups.id'), primary_key=True),
#     extend_existing=True, 
#     info={'bind_key': 'ansible_db'}
# )

class TimestampMixin:
    """Mixin for adding timestamp fields to models."""
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ServerGroup(db.Model, TimestampMixin):
    """Model for managing server groups."""
    __tablename__ = 'server_groups'
    __bind_key__ = 'ansible_db'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)

    # Relationship TO the association object ServerGroupMapping
    server_associations = db.relationship("ServerGroupMapping", back_populates="group", cascade="all, delete-orphan")

    # Proxy 'servers' attribute through the association object
    servers = association_proxy("server_associations", "server")

class Server(db.Model, TimestampMixin):
    """Model for managing server information."""
    __tablename__ = 'servers'
    __bind_key__ = 'ansible_db'
    
    id = db.Column(db.Integer, primary_key=True)
    hostname = db.Column(db.String(100), nullable=False)
    ip_address = db.Column(db.String(15), nullable=False)
    ssh_port = db.Column(db.Integer, default=22)
    description = db.Column(db.Text)

    # Relationship TO the association object ServerGroupMapping
    group_associations = db.relationship("ServerGroupMapping", back_populates="server", cascade="all, delete-orphan")

    # Proxy 'groups' attribute through the association object
    groups = association_proxy("group_associations", "group")

# Define the Association Object Model matching the SQL table
class ServerGroupMapping(db.Model, TimestampMixin):
    """Association object mapping Servers to ServerGroups."""
    __tablename__ = 'server_group_mappings'
    __bind_key__ = 'ansible_db'

    id = db.Column(db.Integer, primary_key=True) # Surrogate PK as in SQL
    # Foreign keys referencing the parent tables
    # Mark as nullable=False if a mapping must exist
    group_id = db.Column(db.Integer, db.ForeignKey('server_groups.id'), nullable=False, index=True)
    server_id = db.Column(db.Integer, db.ForeignKey('servers.id'), nullable=False, index=True)

    # Relationships back to the parent objects for the proxy to work
    group = db.relationship("ServerGroup", back_populates="server_associations")
    server = db.relationship("Server", back_populates="group_associations")

class Playbook(db.Model, TimestampMixin):
    """Model for managing Ansible playbooks."""
    __tablename__ = 'playbooks'
    __bind_key__ = 'ansible_db'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    playbook_path = db.Column(db.String(255), nullable=False)
    created_by = db.Column(db.Integer, nullable=True)

class AnsibleTaskTemplate(db.Model, TimestampMixin):
    """Model for managing Ansible task templates."""
    __tablename__ = 'ansible_task_templates'
    __bind_key__ = 'ansible_db'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, unique=True)
    description = db.Column(db.Text)
    task_type = db.Column(db.String(50), nullable=False)
    playbook_path = db.Column(db.String(255))
    extra_vars = db.Column(db.Text)

class AnsibleTask(db.Model, TimestampMixin):
    """Model for managing Ansible task executions."""
    __tablename__ = 'ansible_tasks'
    __bind_key__ = 'ansible_db'
    
    id = db.Column(db.Integer, primary_key=True)
    task_name = db.Column(db.String(100), nullable=False)
    task_type = db.Column(db.String(50), nullable=False)
    target_type = db.Column(db.String(50))  # 'server' or 'group'
    target_servers = db.Column(db.Text, nullable=False)
    playbook_path = db.Column(db.String(255))
    command = db.Column(db.Text)
    status = db.Column(db.String(20), default='pending')
    result = db.Column(db.UnicodeText)
    formatted_result = db.Column(db.UnicodeText)
    start_time = db.Column(db.DateTime)
    end_time = db.Column(db.DateTime)
    playbook_id = db.Column(db.Integer, db.ForeignKey('playbooks.id'))
    celery_task_id = db.Column(db.String(255)) # 用于存储Celery任务ID

    # 修改关系定义，不使用backref，避免冲突
    executions = db.relationship('AnsibleTaskExecution', back_populates='task', lazy=True, cascade="all, delete-orphan")
    playbook = db.relationship('Playbook', backref='tasks')

    def __repr__(self):
        return f'<AnsibleTask {self.task_name}>'

class AnsibleTaskExecution(db.Model, TimestampMixin):
    """Model for storing Ansible task execution history."""
    __tablename__ = 'ansible_task_executions'
    __bind_key__ = 'ansible_db'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey('ansible_tasks.id'))
    status = db.Column(db.String(20), default='pending')
    result = db.Column(db.UnicodeText)
    formatted_result = db.Column(db.UnicodeText)
    start_time = db.Column(db.DateTime)
    end_time = db.Column(db.DateTime)
    
    # 修改关系定义，使用back_populates替代backref
    task = db.relationship('AnsibleTask', back_populates='executions')

class AnsibleTaskLog(db.Model, TimestampMixin):
    """Model for storing Ansible task execution logs."""
    __tablename__ = 'ansible_task_logs'
    __bind_key__ = 'ansible_db'
    
    id = db.Column(db.Integer, primary_key=True)
    task_id = db.Column(db.Integer, db.ForeignKey('ansible_tasks.id'))
    log_type = db.Column(db.String(20), nullable=False)
    message = db.Column(db.Text, nullable=False)