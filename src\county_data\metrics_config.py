# src/county_data/metrics_config.py

INDICATORS_CONFIG = [
    {
        "id": 1,
        "level1_name": "数据治理",
        "level2_name": "数据人均汇聚量",
        # 注意：weight字段仅作默认/回退，实际权重以数据库indicator_period_weights_metadata为准
        "weight": 1.34,
        "provincial_best_value": None, # 根据实际情况填写或保持 None
        "unit": None, # 根据实际情况填写单位，如 "条/人"
        "data_source_type": "per_capita_data", # 用于映射到计算函数
        "remarks": "数据人均汇聚量备注" # 可选
    },
    {
        "id": 2,
        "level1_name": "数据治理",
        "level2_name": "{year}年度数据汇聚人均增长量",
        # 注意：weight字段仅作默认/回退，实际权重以数据库indicator_period_weights_metadata为准
        "weight": 1.34,
        "provincial_best_value": None,
        "unit": None, # 如 "条/人"
        "data_source_type": "yearly_increment_data",
        "remarks": "年度数据汇聚人均增长量备注"
    },
    {
        "id": 3,
        "level1_name": "数据治理",
        "level2_name": "接口库表资源挂接率",
        # 注意：weight字段仅作默认/回退，实际权重以数据库indicator_period_weights_metadata为准
        "weight": 1.34,
        "provincial_best_value": None,
        "unit": "%",
        "data_source_type": "connection_rate_data",
        "remarks": "接口库表资源挂接率备注"
    },
    {
        "id": 4,
        "level1_name": "数据治理",
        "level2_name": "数据资源申请利用率",
        # 注意：weight字段仅作默认/回退，实际权重以数据库indicator_period_weights_metadata为准
        "weight": 1.34,
        "provincial_best_value": "100.00%", # 示例值
        "city_wide_value": "100.00%", # 示例值
        "unit": "%",
        "data_source_type": "resource_usage_data",
        "remarks": "数据资源申请利用率备注"
    }
]

REGIONS_CONFIG = [
    {"id": 1, "name": "秦安县", "english_name": "qinan", "db_table_suffix": "qin_an", "region_code": "620522000000"},
    {"id": 2, "name": "张家川县", "english_name": "zhangjiachuan", "db_table_suffix": "zhang_jia_chuan", "region_code": "620525000000"},
    {"id": 3, "name": "清水县", "english_name": "qingshui", "db_table_suffix": "qin_shui", "region_code": "620521000000"},
    {"id": 4, "name": "甘谷县", "english_name": "gangu", "db_table_suffix": "gan_gu", "region_code": "620523000000"},
    {"id": 5, "name": "麦积区", "english_name": "maiji", "db_table_suffix": "mai_ji", "region_code": "620503000000"},
    {"id": 6, "name": "武山县", "english_name": "wushan", "db_table_suffix": "wu_shan", "region_code": "620524000000"},
    {"id": 7, "name": "秦州区", "english_name": "qinzhou", "db_table_suffix": "qin_zhou", "region_code": "620502000000"}
]

# 用于前端下拉框选项及其对应的后端逻辑参数
# offset_weeks: None 表示当前实时；0 表示本周一11点；-1 表示上周一11点，以此类推。
PERIOD_OPTIONS = [
    {'id': 'current', 'name': '当前', 'offset_weeks': None},
    {'id': 'this_monday_11am', 'name': '上一周', 'offset_weeks': 0},
    {'id': 'last_monday_11am', 'name': '前两周', 'offset_weeks': -1},
    {'id': 'two_mondays_ago_11am', 'name': '前三周', 'offset_weeks': -2},
    {'id': 'three_mondays_ago_11am', 'name': '前四周', 'offset_weeks': -3},
] 