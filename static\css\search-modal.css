/**
 * 服务器管理系统搜索弹窗样式
 */

/* 搜索弹窗样式优化 */
.search-modal .modal-dialog {
    max-width: 600px;
    margin: 1.75rem auto;
}

.search-modal .modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.search-modal .modal-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eaeaea;
    padding: 1.25rem 1.5rem;
}

.search-modal .modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
}

.search-modal .modal-body {
    padding: 1.5rem;
    max-height: 60vh;
    overflow-y: auto;
    overflow-x: hidden;
}

.search-modal .modal-footer {
    border-top: 1px solid #eaeaea;
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
}

/* 搜索输入框样式 */
.search-modal .search-input-group {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-modal .search-input-group .form-control {
    padding-left: 40px;
    height: 46px;
    border-radius: 8px;
    border: 1px solid #ddd;
    box-shadow: none;
    transition: all 0.3s;
}

.search-modal .search-input-group .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.search-modal .search-input-group .search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-modal .form-text {
    color: #6c757d;
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

/* 直接搜索输入框样式 */
.search-input-wrapper {
    position: relative;
}

.search-input-wrapper .form-control {
    border-radius: 6px;
    border: 1px solid #ddd;
    padding: 0.375rem 0.75rem;
    transition: all 0.3s;
}

.search-input-wrapper .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 搜索结果样式 */
.search-modal .search-results {
    margin-top: 1.5rem;
}

.search-modal .search-results-header {
    padding: 0.75rem 1rem;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 1rem;
    font-weight: 500;
}

.search-modal .search-results-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.search-modal .search-results-table th,
.search-modal .search-results-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #eaeaea;
}

.search-modal .search-results-table th {
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
    text-align: left;
}

.search-modal .search-results-table tr:last-child td {
    border-bottom: none;
}

.search-modal .search-results-table tr:hover {
    background-color: #f8f9fa;
}

.search-modal .search-results-table .action-link {
    color: #007bff;
    text-decoration: none;
    font-weight: 500;
}

.search-modal .search-results-table .action-link:hover {
    text-decoration: underline;
}

/* 无结果状态 */
.search-modal .no-results {
    text-align: center;
    padding: 2rem 1rem;
    color: #6c757d;
}

.search-modal .no-results i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: #adb5bd;
}

.search-modal .no-results h5 {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.search-modal .no-results p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* 按钮样式 */
.search-modal .btn-search {
    background-color: #007bff;
    border-color: #007bff;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    border-radius: 6px;
}

.search-modal .btn-search:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

.search-modal .btn-close-modal {
    background-color: #f8f9fa;
    border-color: #ddd;
    color: #495057;
    padding: 0.5rem 1.5rem;
    font-weight: 500;
    border-radius: 6px;
}

.search-modal .btn-close-modal:hover {
    background-color: #e9ecef;
    border-color: #ccc;
}

/* 加载状态 */
.search-modal .loading-indicator {
    text-align: center;
    padding: 2rem 0;
}

.search-modal .loading-indicator .spinner-border {
    width: 3rem;
    height: 3rem;
    color: #007bff;
}

.search-modal .loading-indicator p {
    margin-top: 1rem;
    color: #6c757d;
    font-weight: 500;
}

/* 移除滚动条 */
.search-modal .modal-body::-webkit-scrollbar {
    width: 8px;
}

.search-modal .modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.search-modal .modal-body::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.search-modal .modal-body::-webkit-scrollbar-thumb:hover {
    background: #999;
}

/* 响应式调整 */
@media (max-width: 767.98px) {
    .search-modal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    .search-modal .modal-body {
        padding: 1rem;
    }
    
    .search-modal .search-results-table th,
    .search-modal .search-results-table td {
        padding: 0.5rem;
    }
    
    .search-input-wrapper .form-control {
        width: 150px !important;
    }
}

@media (max-width: 575.98px) {
    .search-input-wrapper {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .search-input-wrapper .form-control {
        width: 100% !important;
    }
    
    .card-header .d-flex {
        flex-direction: column;
        align-items: flex-start !important;
    }
    
    .card-header .d-flex .d-flex {
        margin-top: 0.5rem;
        width: 100%;
    }
}
