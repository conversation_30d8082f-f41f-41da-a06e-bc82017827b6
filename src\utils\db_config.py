# db_config.py - 数据库配置适配器
# 作用：从 .env 文件读取配置，为系统其他模块提供统一的数据库配置接口
# 注意：所有配置必须在 .env 文件中设置，此文件不包含任何默认值

import os
import logging

logger = logging.getLogger(__name__)

def _get_required_env(key: str, description: str = None) -> str:
    """获取必需的环境变量，如果不存在则抛出错误"""
    value = os.environ.get(key)
    if not value:
        error_msg = f"环境变量 {key} 未设置"
        if description:
            error_msg += f"（{description}）"
        error_msg += "，请在 .env 文件中配置"
        logger.error(error_msg)
        raise ValueError(error_msg)
    return value

# 从 .env 文件读取数据库配置（必需）
try:
    DB_HOST = _get_required_env('DB_HOST', '数据库主机地址')
    DB_PORT = int(_get_required_env('DB_PORT', '数据库端口'))
    DB_USER = _get_required_env('DB_USER', '数据库用户名')
    DB_PASSWORD = _get_required_env('DB_PASSWORD', '数据库密码')
    DB_NAME_MAIN = _get_required_env('DB_NAME_MAIN', '主数据库名')
    DB_NAME_MYSQL_LOG = _get_required_env('DB_NAME_MYSQL_LOG', 'MySQL审计数据库名')
    DB_NAME_ANSIBLE = _get_required_env('DB_NAME_ANSIBLE', 'Ansible数据库名')
    
    logger.info(f"数据库配置加载成功 - 主机: {DB_HOST}:{DB_PORT}, 用户: {DB_USER}")
    
except ValueError as e:
    logger.error(f"数据库配置加载失败: {str(e)}")
    logger.error("请检查 .env 文件是否存在并包含所有必需的数据库配置")
    raise

# 数据库名称常量，用于信息模式查询等
DB_SCHEMA_MAIN = DB_NAME_MAIN

# MySQL审计模块数据库配置
DB_CONFIG = {
    'host': DB_HOST,
    'port': DB_PORT,
    'user': DB_USER,
    'password': DB_PASSWORD,
    'database': DB_NAME_MYSQL_LOG
}

# 数据库连接字符串
ANSIBLE_DB_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME_ANSIBLE}'
MAIN_DB_URI = f'mysql+pymysql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME_MAIN}'
