<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>指标统计 - 数据管理系统</title>
    <!-- 本地Bootstrap CSS -->
    <link rel="stylesheet" href="/static/css/bootstrap.min.css">
    <!-- Font Awesome 图标库 -->
    <link rel="stylesheet" href="/static/css/all.min.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "SF Pro Display", "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
            background-color: #f5f7fa;
            min-height: 100vh;
            padding: 32px;
            color: #1d1d1f;
        }

        .container {
            max-width: 1600px; /* Increased max-width for more columns */
            margin: 0 auto;
            padding-top: 40px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: #1d1d1f;
            margin-bottom: 30px;
        }

        /* 表格样式 */
        .metrics-table {
            width: 100%;
            border-collapse: collapse;
            background-color: #fff;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }

        .metrics-table th,
        .metrics-table td {
            border: 1px solid #e0e0e0;
            padding: 10px 8px; /* Adjusted padding */
            text-align: center;
            font-size: 13px; /* Adjusted font size */
            white-space: nowrap; /* Prevent text wrapping in cells */
        }
        
        .metrics-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #333;
            vertical-align: middle;
        }

        .metrics-table tr:hover {
            background-color: #f8f9fa;
        }
        
        .metrics-table .region-header {
            background-color: #f1f3f5;
            font-weight: 600;
        }
        
        .metrics-table .indicator-name {
            text-align: left;
            font-weight: 500;
            color: #333;
            white-space: normal; /* Allow indicator names to wrap */
        }
        
        .metrics-table .score {
            /* color: #2196f3; */ /* Color will be handled by rank classes */
            font-weight: 600;
        }
        
        .text-success {
            color: #28a745 !important;
        }
        .text-danger {
            color: #dc3545 !important;
        }
        .text-warning {
            color: #ffc107 !important;
        }
        .font-weight-bold {
            font-weight: bold;
        }

        /* 表格容器样式 */
        .table-container {
            overflow-x: auto;
            margin-top: 20px;
            padding: 20px;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        }

        /* 响应式布局 */
        @media (max-width: 768px) {
            .container {
                padding: 20px;
                 max-width: 100%; 
            }
            
            .metrics-table th,
            .metrics-table td {
                padding: 6px 4px;
                font-size: 11px;
            }
        }

        /* 加载动画 */
        .loading {
            text-align: center;
            padding: 20px;
            font-size: 14px;
            color: #666;
        }

        .loading i {
            margin-right: 8px;
        }

        .metrics-table td {
            vertical-align: top; /* Changed to top for better alignment with multi-line indicator names */
            padding: 12px 8px; /* Adjusted padding */
        }

        /* 得分颜色样式 */
        .score-rank-1 {
            color: #e74c3c;  /* 改为更醒目的红色 */
            font-weight: bold;
            background-color: #ffeaea; /* 添加背景色 */
        }
        .score-rank-2 {
            color: #27ae60;  /* 保留绿色 */
            font-weight: bold;
        }
        .score-rank-3 {
            color: #3498db;  /* 保留蓝色 */
            font-weight: bold;
        }
        
        /* 周期选择器样式 */
        .period-selector {
            max-width: 300px;
            margin-left: 20px;
        }
        .info-box {
            padding: 10px 15px;
            border-radius: 0.25rem;
            margin-bottom: 15px;
            font-size: 0.9rem;
        }
        .info-box strong {
            font-weight: 600;
        }
        .comparison-text {
            font-size: 0.85rem;
            color: #555;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="d-flex align-items-center justify-content-between mb-4">
            <div class="d-flex align-items-center">
            <h1 class="page-title m-0">
                <i class="fas fa-chart-line me-2"></i>指标统计
            </h1>
                <div class="period-selector">
                    <select class="form-select" id="periodSelector" aria-label="选择周期">
                        {% if periods %}
                            {% for period in periods %}
                                <option value="{{ period.id }}" {% if period.id == current_period_id %}selected{% endif %}>
                                    {{ period.name }}
                                </option>
                            {% endfor %}
                        {% else %}
                            <option value="current">当前</option> {# Fallback, should be populated by backend #}
                        {% endif %}
                    </select>
                </div>
            </div>
            <a href="/" class="btn btn-outline-primary">
                <i class="fas fa-home me-2"></i>返回首页
            </a>
        </div>
        
        <div class="alert alert-info info-box" id="cutoffDateInfo" {% if not current_cutoff_date_display %}style="display: none;"{% endif %}>
            <strong>当前数据截止日期：</strong> <span id="cutoffDateDisplay">{{ current_cutoff_date_display if current_cutoff_date_display else '加载中...' }}</span>
            <p style="margin-top:5px;font-size:12px;color:#6c757d;">注意：每个时间周期为周一上午11点到下周一上午11点</p>
        </div>

        <!-- 用于显示对比周期的信息 -->
        {% if comparison_text %}
        <div class="alert alert-secondary info-box comparison-text" id="comparisonInfo">
            {{ comparison_text }}
        </div>
        {% endif %}
        
        <!-- 新增：显示指标变化摘要 -->
        {% if comparison_highlights %}
        <div class="alert alert-warning info-box" id="comparisonHighlights">
            <strong>指标变化摘要 (与上一期比较):</strong>
            <ul class="list-unstyled mb-0 mt-2" style="font-size: 0.85rem;">
                {% for highlight in comparison_highlights %}
                    <li>{{ highlight|safe }}</li>
                {% else %}
                    <li>与上一期相比，各项主要指标无显著变化。</li>
                {% endfor %}
            </ul>
        </div>
        {% endif %}
        
        <div class="table-container">
            <div id="loadingIndicator" class="loading" {% if indicators or error %}style="display: none;"{% endif %}>
                <i class="fas fa-spinner fa-spin"></i>加载数据中...
            </div>
            {% if error %}
                <div class="alert alert-danger">{{ error }}</div>
            {% endif %}

            {% if indicators %}
            <table class="metrics-table">
                <thead>
                    <tr>
                        <th rowspan="2" style="width: 8%;">一级指标</th>
                        <th rowspan="2" style="width: 15%;">二级指标</th>
                        <th rowspan="2" style="width: 5%;">区划<br>权重</th>
                        <th rowspan="2" style="width: 7%;">全省<br>最优</th>
                        <th rowspan="1" style="width: 7%;">全市</th>
                        {% for region in regions_for_header %}
                            <th colspan="2">{{ region.name }}</th>
                        {% endfor %}
                    </tr>
                    <tr>
                        <th>本期指标</th>
                        {% for _ in regions_for_header %}
                            <th>本期指标</th>
                            <th>得分</th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody>
                    {% for indicator in indicators %}
                    <tr>
                        {# 一级指标 (处理 rowspan) #}
                        {% if loop.first or indicators[loop.index0-1].level1_name != indicator.level1_name %}
                            {% set rowspan_val = namespace(count = 0) %}
                            {% set current_level1_name_for_calc = indicator.level1_name %}
                            {# This inner loop calculates rowspan. It assumes 'indicators' is sorted by level1_name. #}
                            {% for i in range(loop.index0, indicators|length) %}
                                {% if indicators[i].level1_name == current_level1_name_for_calc %}
                                    {% set rowspan_val.count = rowspan_val.count + 1 %}
                                {% endif %}
                            {% endfor %}
                            <td rowspan="{{ rowspan_val.count if rowspan_val.count > 0 else 1 }}" class="indicator-name">{{ indicator.level1_name }}</td>
                        {% endif %}

                        <td class="indicator-name">
                            {{ indicator.level2_name }}
                            {% if indicator.unit %}<span class="text-muted"> ({{ indicator.unit }})</span>{% endif %}
                        </td>
                        <td>{{ indicator.weight | round(2) if indicator.weight is not none else '-' }}</td>
                        <td>{{ indicator.provincial_best_value if indicator.provincial_best_value else '/' }}</td>
                        
                        {# 全市数据 #}
                        <td>
                            {{ indicator.city_wide_value if indicator.city_wide_value is not none else '-' }}
                            {% if indicator.city_wide_change_indicator == 'up' %}
                                <i class="fas fa-arrow-up text-danger ms-1"></i>
                            {% elif indicator.city_wide_change_indicator == 'down' %}
                                <i class="fas fa-arrow-down text-success ms-1"></i>
                            {% endif %}
                        </td>

                        {# 区县数据 #}
                        {% for region_data in indicator.regional_data %}
                        <td>
                            {{ region_data.current_value_display if region_data.current_value_display is not none else '-' }}
                            {% if region_data.change_indicator == 'up' %}
                                <i class="fas fa-arrow-up text-danger ms-1"></i>
                            {% elif region_data.change_indicator == 'down' %}
                                <i class="fas fa-arrow-down text-success ms-1"></i>
                            {% endif %}
                        </td>
                        <td class="score
                            {%- if region_data.score is not none -%}
                                {%- set scores_list = indicator.regional_data | map(attribute='score') | reject('none') | list -%}
                                {%- if scores_list -%}
                                    {%- set sorted_scores = scores_list | sort(reverse=true) -%}
                                    {%- if region_data.score == sorted_scores[0] and (sorted_scores | length > 1 or region_data.score > 0) %} score-rank-1
                                    {%- elif (sorted_scores | length > 1 and region_data.score == sorted_scores[1]) or (sorted_scores | length == 1 and region_data.score > 0 and region_data.score != sorted_scores[0]) %} score-rank-2
                                    {%- elif (sorted_scores | length > 2 and region_data.score == sorted_scores[2]) %} score-rank-3
                                    {%- endif -%}
                                {%- endif -%}
                            {%- endif -%}
                        ">
                            {{ region_data.score | round(2) if region_data.score is not none else '-' }}
                        </td>
                        {% endfor %}
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% elif not error %}
                 <div class="alert alert-warning">当前选择的周期无指标数据。</div>
            {% endif %}
        </div>
    </div>

    <!-- 本地 jQuery -->
    <script src="/static/js/lib/jquery-3.6.0.min.js"></script>
    <!-- 本地 Bootstrap JS -->
    <script src="/static/js/lib/bootstrap.bundle.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 监听周期选择器变化
            const periodSelector = document.getElementById('periodSelector');
            if (periodSelector) {
                periodSelector.addEventListener('change', function() {
                    const selectedValue = this.value;
                    // 显示加载指示器
                    const loadingIndicator = document.getElementById('loadingIndicator');
                    if (loadingIndicator) {
                        loadingIndicator.style.display = 'block';
                    }
                    // 清空可能存在的错误信息
                    const errorAlert = document.querySelector('.table-container .alert-danger');
                    if(errorAlert) errorAlert.remove();
                    
                    // 清空表格内容（或者用一个覆盖层）
                    const table = document.querySelector('.metrics-table');
                    if(table) {
                        // table.style.opacity = 0.5; // Optional: visual feedback
                    }

                    window.location.href = `/metrics_stats?period_id=${selectedValue}`;
                });
            }

            // Optional: If there's an error message from backend, ensure loading indicator is hidden.
            const serverError = {{ error|tojson|safe }};
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (serverError && loadingIndicator) {
                loadingIndicator.style.display = 'none';
            }
            
            // Handle cutoff date display from Jinja if available, otherwise it's set by Jinja directly.
            // const serverCurrentCutoffDate = {{ current_cutoff_date_display|tojson|safe }};
            // const cutoffDateDisplayEl = document.getElementById('cutoffDateDisplay');
            // const cutoffDateInfoEl = document.getElementById('cutoffDateInfo');
            // if (serverCurrentCutoffDate) {
            //     if (cutoffDateDisplayEl) cutoffDateDisplayEl.textContent = serverCurrentCutoffDate;
            //     if (cutoffDateInfoEl) cutoffDateInfoEl.style.display = 'block';
            // } else if (cutoffDateInfoEl && !serverError){ // only hide if no date AND no error
            //    // cutoffDateInfoEl.style.display = 'none'; // Keep it visible with "加载中..." or actual date
            // }

            // The main data population is now handled by Jinja2 server-side rendering.
            // The old populatePageWithServerData and extensive DOM manipulation for table content are removed.
            console.log("Page loaded. Data is rendered server-side via Jinja2.");
        });
    </script>
</body>
</html> 